{"name": "laminas/laminas-stdlib", "description": "SPL extensions, array utilities, error handlers, and more", "license": "BSD-3-<PERSON><PERSON>", "keywords": ["laminas", "stdlib"], "homepage": "https://laminas.dev", "support": {"docs": "https://docs.laminas.dev/laminas-stdlib/", "issues": "https://github.com/laminas/laminas-stdlib/issues", "source": "https://github.com/laminas/laminas-stdlib", "rss": "https://github.com/laminas/laminas-stdlib/releases.atom", "chat": "https://laminas.dev/chat", "forum": "https://discourse.laminas.dev"}, "config": {"sort-packages": true, "platform": {"php": "8.1.99"}, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}, "extra": {}, "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "require-dev": {"laminas/laminas-coding-standard": "^3.0", "phpbench/phpbench": "^1.3.1", "phpunit/phpunit": "^10.5.38", "psalm/plugin-phpunit": "^0.19.0", "vimeo/psalm": "^5.26.1"}, "autoload": {"psr-4": {"Laminas\\Stdlib\\": "src/"}}, "autoload-dev": {"psr-4": {"LaminasTest\\Stdlib\\": "test/", "LaminasBench\\Stdlib\\": "benchmark/"}}, "scripts": {"check": ["@cs-check", "@test"], "cs-check": "phpcs", "cs-fix": "phpcbf", "static-analysis": "psalm --shepherd --stats", "test": "phpunit --colors=always", "test-coverage": "phpunit --colors=always --coverage-clover clover.xml"}, "conflict": {"zendframework/zend-stdlib": "*"}}