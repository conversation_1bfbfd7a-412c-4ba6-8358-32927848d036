<link href="<?php echo base_url('application/modules/report/assets/css/ajaxsalereport.css'); ?>" rel="stylesheet" type="text/css" />

<div class="table-responsive">
	<table class="table table-bordered table-striped table-hover" id="respritbl">
		<thead>
			<tr>
				<th><?php echo display('customer_name') ?></th>
				<th>Nama <PERSON>u</th>
				<th>Total Jumlah Beli</th>
			</tr>
		</thead>
		<tbody class="ajaxsalereport">
			<?php
			$cumTotalJumlahBeli = 0;
			if ($dataReportSoldItemByCustomer) {
				foreach ($dataReportSoldItemByCustomer as $value) {
					$cumTotalJumlahBeli += $value->total_jumlah_beli;
			?>
					<tr>
						<!-- nama pelanggan -->
						<td>
							<a href="<?php echo base_url("ordermanage/order/orderdetails/" . $value->id) ?>" target="_blank">
								<?= $value->customer_name; ?>
							</a>
						</td>

						<!-- nama menu -->
						<td>
							<?= $value->item_name ?>
						</td>

						<!-- total jumlah beli -->
						<td class="text-center">
							<?= 
								helperFormatNumber([
									'number' => $value->total_qty,
									'code' => 'thousandSeparator',
									'decimalPlace' => 0
								]);
							?>
						</td>
					</tr>
			<?php }
			}
			?>
		</tbody>
		<tfoot class="ajaxsalereport-footer">
			<tr>
				<td class="ajaxsalereport-fo-total-sale" colspan="2" align="right">&nbsp; <b></b></td>
				<td class="fo-total-sale">
					<b>		
						<?=
							helperFormatNumber([
								'number' => $totalprice,
								'code' => 'thousandSeparator',
								'decimalPlace' => 0
							]);
						?>
					</b>
				</td>
			</tr>
		</tfoot>
	</table>
</div>
