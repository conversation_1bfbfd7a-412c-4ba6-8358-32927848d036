"use strict";var today=$("#today").val(),csrf=$("#csrf").val();$(document).ready((function(){var view_name=$("#view_name").val();console.log({view_name:view_name});var myurl=baseurl+"report/reports/"+view_name,csrf=$("#csrfhashresarvation").val(),dataString="from_date="+today+"&to_date="+today+"&csrf_test_name="+csrf;$.ajax({type:"POST",url:myurl,data:dataString,success:function(data){$("#getresult2").html(data)}})}));