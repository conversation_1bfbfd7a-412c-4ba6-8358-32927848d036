<script src="<?php echo base_url('application/modules/report/assets/js/salereportfrmItems.js'); ?>"
    type="text/javascript"></script>
<link href="<?php echo base_url('application/modules/report/assets/css/salereportfrmItems.css'); ?>" rel="stylesheet"
    type="text/css" />

<div class="row">
    <div class="col-sm-12 col-md-12">
        <div class="panel">

            <div class="panel-body">
                <fieldset class="border p-2">
                    <legend class="w-auto"><?php echo $title; ?></legend>
                </fieldset>
                <div class="row">
                    <div class="col-sm-12">
                        <div class="panel panel-default">
                            <div class="panel-body">
                                <?php echo form_open('report/index', array('class' => 'form-inline')) ?>
                                <?php $today = date('d-m-Y'); ?>
                                <!-- CSRF <PERSON> -->
                                <input name="csrfres" id="csrfresarvation" type="hidden" value="<?php echo $this->security->get_csrf_token_name(); ?>" />
                                <input name="csrfhash" id="csrfhashresarvation" type="hidden" value="<?php echo $this->security->get_csrf_hash(); ?>" />
                                <div class="form-group">
                                    <label class="" for="from_date"><?php echo display('start_date') ?></label>
                                    <input type="hidden" name="" id="view_name" value="<?php echo $view; ?>">
                                    <input type="text" name="from_date" class="form-control datepicker" id="from_date"
                                        value="<?php echo $today ?>" placeholder="<?php echo display('start_date') ?>"
                                        readonly="readonly">
                                </div>

                                <div class="form-group">
                                    <label class="" for="to_date"><?php echo display('end_date') ?></label>
                                    <input type="text" name="to_date" class="form-control datepicker" id="to_date"
                                        placeholder="<?php echo display('end_date') ?>" value="<?php echo $today ?>"
                                        readonly="readonly">
                                </div>
                                <?php if (!empty($categorylist)) { ?>
                                    <div class="form-group">
                                        <label for="catid"><?php echo display('category') ?></label>
                                        <?php echo form_dropdown('categorylist', $categorylist, (!empty($catelist) ? $catelist : null), 'class="form-control" id="catid"') ?>
                                    </div>
                                <?php } else { ?>
                                    <input type="hidden" id="catid" value="">
                                <?php } ?>
                                <a class="btn btn-success"
                                    onclick="getreportupdate()"><?php echo display('search') ?></a>
                                <a class="btn btn-warning" href="#"
                                    onclick="printDiv('purchase_div')"><?php echo display('print') ?></a>
                            </div>


                            <?php echo form_close() ?>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="panel panel-bd lobidrag">
                        <div class="panel-heading">
                            <div class="panel-title">
                                <h4><?php echo display('sell_report') ?></h4>
                            </div>
                        </div>
                        <div class="panel-body">
                            <div id="purchase_div">
                                <div class="text-center">
                                    <h3> <?php echo $setting->storename; ?> </h3>
                                    <h4><?php echo $setting->address; ?> </h4>
                                    <h4><?php echo display('print_date') ?>: <?php echo date("d/m/Y h:i:s"); ?> </h4>
                                </div>
                                <div class="table-responsive" id="getresult2">

                                </div>
                            </div>
                            <div class="text-right">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
<input type="hidden" id="today" value="<?php echo date('d-m-Y'); ?>">

<script type="text/javascript">
    var baseurl = '<?php echo base_url(); ?>';

    $(document).ready(function() {
        console.log('Report page loaded');
        console.log('Category dropdown element:', $('#catid'));
        console.log('Category dropdown length:', $('#catid').length);
        console.log('Category dropdown class:', $('#catid').attr('class'));
        console.log('Select2 available:', typeof $.fn.select2);

        // Check if Select2 is already initialized
        setTimeout(function() {
            console.log('After 1 second:');
            console.log('Category dropdown has select2 class:', $('#catid').hasClass('select2-hidden-accessible'));

            // Force initialize if not already done
            if (!$('#catid').hasClass('select2-hidden-accessible') && typeof $.fn.select2 !== 'undefined') {
                console.log('Manually initializing Select2...');
                $('#catid').select2({
                    placeholder: "Select Category",
                    allowClear: true,
                    width: '100%'
                });
            }
        }, 1000);
    });
</script>

<script src="<?php echo base_url('application/modules/report/assets/js/salereportfrmItems_script.js'); ?>"
    type="text/javascript"></script>