<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Install extends MX_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->db->query('SET SESSION sql_mode = ""');

        // Check if user is admin
        if (!$this->session->userdata('isAdmin')) {
            redirect('login');
        }
    }

    // Install language phrases
    public function phrases()
    {
        // Phrases yang akan ditambahkan
        $phrases = array(
            // Module name and menu items
            'customermanage' => 'Manajemen Pelanggan',
            'customer_list' => 'Daftar Pelanggan',
            'customer_ranking' => 'Ranking Pelanggan',
            'customer_details' => 'Detail Pelanggan',

            // Page content
            'customer_information' => 'Informasi Pelanggan',
            'customer_statistics' => 'Statistik Pelanggan',
            'total_orders' => 'Total Pesanan',
            'total_spent' => 'Total Pengeluaran',
            'last_order' => 'Pesanan Terak<PERSON>',
            'order_history' => 'Riwayat Pesanan',
            'member_since' => 'Member Sejak',
            'view_details' => 'Lihat Detail',
            'customer_management' => 'Manajemen Pelanggan',
            'no_record_found' => 'Tidak ada data yang ditemukan',
            'search' => 'Cari',
            'loading' => 'Memuat',
            'close' => 'Tutup',
            'back' => 'Kembali',
            'details' => 'Detail',
            'item_name' => 'Nama Item',
            'quantity' => 'Jumlah',
            'price' => 'Harga',
            'total' => 'Total',
            'invoice_no' => 'No. Invoice',
            'order_date' => 'Tanggal Pesanan',
            'table' => 'Meja',
            'waiter' => 'Pelayan',
            'total_amount' => 'Total',
            'status' => 'Status',
            'action' => 'Aksi',
            'rank' => 'Ranking',
            'top_customers_by_spent' => 'Top Pelanggan Berdasarkan Pengeluaran',
            'top_customers_by_orders' => 'Top Pelanggan Berdasarkan Jumlah Pesanan',
            'sl' => 'No',
            'customer_id' => 'Customer ID',
            'customer_name' => 'Nama Pelanggan',
            'email' => 'Email',
            'mobile' => 'Telepon',
            'address' => 'Alamat',
            'customer_type' => 'Jenis Pelanggan',
            'order_details' => 'Detail Pesanan'
        );

        $added = 0;
        $updated = 0;

        foreach ($phrases as $phrase => $english) {
            // Check if phrase already exists
            $existing = $this->db->get_where('language', array('phrase' => $phrase))->num_rows();

            if ($existing == 0) {
                // Insert new phrase
                $this->db->insert('language', array('phrase' => $phrase, 'english' => $english));
                $added++;
            } else {
                // Update existing phrase
                $this->db->where('phrase', $phrase);
                $this->db->update('language', array('english' => $english));
                $updated++;
            }
        }

        echo "<h2>Customer Management Module - Language Installation</h2>";
        echo "<p>Added $added new phrases</p>";
        echo "<p>Updated $updated existing phrases</p>";
        echo "<p><strong>Installation completed successfully!</strong></p>";
        echo "<p><a href='" . base_url('customermanage/customer/index') . "'>Go to Customer List</a></p>";
    }
}
