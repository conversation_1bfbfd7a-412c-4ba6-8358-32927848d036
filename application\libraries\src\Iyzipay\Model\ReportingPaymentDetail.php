<?php

namespace Iy<PERSON>pay\Model;

use Iyzipay\Model\Mapper\ReportingPaymentDetailMapper;
use <PERSON>yzipay\Options;
use Iyzipay\Request\ReportingPaymentDetailRequest;
use Iy<PERSON>pay\RequestStringBuilder;

class ReportingPaymentDetail extends ReportingPaymentDetailResource
{
    public static function create(ReportingPaymentDetailRequest $request, Options $options)
    {
        $uri = $options->getBaseUrl() . "/v2/reporting/payment/details" . RequestStringBuilder::requestToStringQuery($request, 'reporting');
        $rawResult = parent::httpClient()->getV2($uri, parent::getHttpHeadersV2($uri, null, $options));
        return ReportingPaymentDetailMapper::create($rawResult)->jsonDecode()->mapReportingPaymentDetail(new ReportingPaymentDetail());

     }
}