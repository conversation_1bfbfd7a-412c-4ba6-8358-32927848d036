<?php
/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace SquareConnect\Model;

use \ArrayAccess;
/**
 * InventoryAdjustment Class Doc Comment
 *
 * @category Class
 * @package  SquareConnect
 * <AUTHOR> Inc.
 * @license  http://www.apache.org/licenses/LICENSE-2.0 Apache License v2
 * @link     https://squareup.com/developers
 */
class InventoryAdjustment implements ArrayAccess
{
    /**
      * Array of property to type mappings. Used for (de)serialization 
      * @var string[]
      */
    static $swaggerTypes = array(
        'id' => 'string',
        'reference_id' => 'string',
        'from_state' => 'string',
        'to_state' => 'string',
        'location_id' => 'string',
        'catalog_object_id' => 'string',
        'catalog_object_type' => 'string',
        'quantity' => 'string',
        'total_price_money' => '\SquareConnect\Model\Money',
        'occurred_at' => 'string',
        'created_at' => 'string',
        'source' => '\SquareConnect\Model\SourceApplication',
        'employee_id' => 'string',
        'transaction_id' => 'string',
        'refund_id' => 'string',
        'purchase_order_id' => 'string',
        'goods_receipt_id' => 'string'
    );
  
    /** 
      * Array of attributes where the key is the local name, and the value is the original name
      * @var string[] 
      */
    static $attributeMap = array(
        'id' => 'id',
        'reference_id' => 'reference_id',
        'from_state' => 'from_state',
        'to_state' => 'to_state',
        'location_id' => 'location_id',
        'catalog_object_id' => 'catalog_object_id',
        'catalog_object_type' => 'catalog_object_type',
        'quantity' => 'quantity',
        'total_price_money' => 'total_price_money',
        'occurred_at' => 'occurred_at',
        'created_at' => 'created_at',
        'source' => 'source',
        'employee_id' => 'employee_id',
        'transaction_id' => 'transaction_id',
        'refund_id' => 'refund_id',
        'purchase_order_id' => 'purchase_order_id',
        'goods_receipt_id' => 'goods_receipt_id'
    );
  
    /**
      * Array of attributes to setter functions (for deserialization of responses)
      * @var string[]
      */
    static $setters = array(
        'id' => 'setId',
        'reference_id' => 'setReferenceId',
        'from_state' => 'setFromState',
        'to_state' => 'setToState',
        'location_id' => 'setLocationId',
        'catalog_object_id' => 'setCatalogObjectId',
        'catalog_object_type' => 'setCatalogObjectType',
        'quantity' => 'setQuantity',
        'total_price_money' => 'setTotalPriceMoney',
        'occurred_at' => 'setOccurredAt',
        'created_at' => 'setCreatedAt',
        'source' => 'setSource',
        'employee_id' => 'setEmployeeId',
        'transaction_id' => 'setTransactionId',
        'refund_id' => 'setRefundId',
        'purchase_order_id' => 'setPurchaseOrderId',
        'goods_receipt_id' => 'setGoodsReceiptId'
    );
  
    /**
      * Array of attributes to getter functions (for serialization of requests)
      * @var string[]
      */
    static $getters = array(
        'id' => 'getId',
        'reference_id' => 'getReferenceId',
        'from_state' => 'getFromState',
        'to_state' => 'getToState',
        'location_id' => 'getLocationId',
        'catalog_object_id' => 'getCatalogObjectId',
        'catalog_object_type' => 'getCatalogObjectType',
        'quantity' => 'getQuantity',
        'total_price_money' => 'getTotalPriceMoney',
        'occurred_at' => 'getOccurredAt',
        'created_at' => 'getCreatedAt',
        'source' => 'getSource',
        'employee_id' => 'getEmployeeId',
        'transaction_id' => 'getTransactionId',
        'refund_id' => 'getRefundId',
        'purchase_order_id' => 'getPurchaseOrderId',
        'goods_receipt_id' => 'getGoodsReceiptId'
    );
  
    /**
      * $id A unique ID generated by Square for the [InventoryAdjustment](#type-inventoryadjustment).
      * @var string
      */
    protected $id;
    /**
      * $reference_id An optional ID provided by the application to tie the [InventoryAdjustment](#type-inventoryadjustment) to an external system.
      * @var string
      */
    protected $reference_id;
    /**
      * $from_state The [InventoryState](#type-inventorystate) of the related quantity of items before the adjustment.
      * @var string
      */
    protected $from_state;
    /**
      * $to_state The [InventoryState](#type-inventorystate) of the related quantity of items after the adjustment.
      * @var string
      */
    protected $to_state;
    /**
      * $location_id The Square ID of the [Location](#type-location) where the related quantity of items are being tracked.
      * @var string
      */
    protected $location_id;
    /**
      * $catalog_object_id The Square generated ID of the [CatalogObject](#type-catalogobject) being tracked.
      * @var string
      */
    protected $catalog_object_id;
    /**
      * $catalog_object_type The [CatalogObjectType](#type-catalogobjecttype) of the [CatalogObject](#type-catalogobject) being tracked. Tracking is only supported for the `ITEM_VARIATION` type.
      * @var string
      */
    protected $catalog_object_type;
    /**
      * $quantity The number of items affected by the adjustment as a decimal string. Fractional quantities are not supported.
      * @var string
      */
    protected $quantity;
    /**
      * $total_price_money The read-only total price paid for goods associated with the adjustment. Present if and only if `to_state` is `SOLD`. Always non-negative.
      * @var \SquareConnect\Model\Money
      */
    protected $total_price_money;
    /**
      * $occurred_at A client-generated timestamp in RFC 3339 format that indicates when the adjustment took place. For write actions, the `occurred_at` timestamp cannot be older than 24 hours or in the future relative to the time of the request.
      * @var string
      */
    protected $occurred_at;
    /**
      * $created_at A read-only timestamp in RFC 3339 format that indicates when Square received the adjustment.
      * @var string
      */
    protected $created_at;
    /**
      * $source Read-only information about the application that caused the inventory adjustment.
      * @var \SquareConnect\Model\SourceApplication
      */
    protected $source;
    /**
      * $employee_id The Square ID of the [Employee](#type-employee) responsible for the inventory adjustment.
      * @var string
      */
    protected $employee_id;
    /**
      * $transaction_id The read-only Square ID of the [Transaction][#type-transaction] that caused the adjustment. Only relevant for payment-related state transitions.
      * @var string
      */
    protected $transaction_id;
    /**
      * $refund_id The read-only Square ID of the [Refund][#type-refund] that caused the adjustment. Only relevant for refund-related state transitions.
      * @var string
      */
    protected $refund_id;
    /**
      * $purchase_order_id The read-only Square ID of the purchase order that caused the adjustment. Only relevant for state transitions from the Square for Retail app.
      * @var string
      */
    protected $purchase_order_id;
    /**
      * $goods_receipt_id The read-only Square ID of the Square goods receipt that caused the adjustment. Only relevant for state transitions from the Square for Retail app.
      * @var string
      */
    protected $goods_receipt_id;

    /**
     * Constructor
     * @param mixed[] $data Associated array of property value initializing the model
     */
    public function __construct(array $data = null)
    {
        if ($data != null) {
            if (isset($data["id"])) {
              $this->id = $data["id"];
            } else {
              $this->id = null;
            }
            if (isset($data["reference_id"])) {
              $this->reference_id = $data["reference_id"];
            } else {
              $this->reference_id = null;
            }
            if (isset($data["from_state"])) {
              $this->from_state = $data["from_state"];
            } else {
              $this->from_state = null;
            }
            if (isset($data["to_state"])) {
              $this->to_state = $data["to_state"];
            } else {
              $this->to_state = null;
            }
            if (isset($data["location_id"])) {
              $this->location_id = $data["location_id"];
            } else {
              $this->location_id = null;
            }
            if (isset($data["catalog_object_id"])) {
              $this->catalog_object_id = $data["catalog_object_id"];
            } else {
              $this->catalog_object_id = null;
            }
            if (isset($data["catalog_object_type"])) {
              $this->catalog_object_type = $data["catalog_object_type"];
            } else {
              $this->catalog_object_type = null;
            }
            if (isset($data["quantity"])) {
              $this->quantity = $data["quantity"];
            } else {
              $this->quantity = null;
            }
            if (isset($data["total_price_money"])) {
              $this->total_price_money = $data["total_price_money"];
            } else {
              $this->total_price_money = null;
            }
            if (isset($data["occurred_at"])) {
              $this->occurred_at = $data["occurred_at"];
            } else {
              $this->occurred_at = null;
            }
            if (isset($data["created_at"])) {
              $this->created_at = $data["created_at"];
            } else {
              $this->created_at = null;
            }
            if (isset($data["source"])) {
              $this->source = $data["source"];
            } else {
              $this->source = null;
            }
            if (isset($data["employee_id"])) {
              $this->employee_id = $data["employee_id"];
            } else {
              $this->employee_id = null;
            }
            if (isset($data["transaction_id"])) {
              $this->transaction_id = $data["transaction_id"];
            } else {
              $this->transaction_id = null;
            }
            if (isset($data["refund_id"])) {
              $this->refund_id = $data["refund_id"];
            } else {
              $this->refund_id = null;
            }
            if (isset($data["purchase_order_id"])) {
              $this->purchase_order_id = $data["purchase_order_id"];
            } else {
              $this->purchase_order_id = null;
            }
            if (isset($data["goods_receipt_id"])) {
              $this->goods_receipt_id = $data["goods_receipt_id"];
            } else {
              $this->goods_receipt_id = null;
            }
        }
    }
    /**
     * Gets id
     * @return string
     */
    public function getId()
    {
        return $this->id;
    }
  
    /**
     * Sets id
     * @param string $id A unique ID generated by Square for the [InventoryAdjustment](#type-inventoryadjustment).
     * @return $this
     */
    public function setId($id)
    {
        $this->id = $id;
        return $this;
    }
    /**
     * Gets reference_id
     * @return string
     */
    public function getReferenceId()
    {
        return $this->reference_id;
    }
  
    /**
     * Sets reference_id
     * @param string $reference_id An optional ID provided by the application to tie the [InventoryAdjustment](#type-inventoryadjustment) to an external system.
     * @return $this
     */
    public function setReferenceId($reference_id)
    {
        $this->reference_id = $reference_id;
        return $this;
    }
    /**
     * Gets from_state
     * @return string
     */
    public function getFromState()
    {
        return $this->from_state;
    }
  
    /**
     * Sets from_state
     * @param string $from_state The [InventoryState](#type-inventorystate) of the related quantity of items before the adjustment.
     * @return $this
     */
    public function setFromState($from_state)
    {
        $this->from_state = $from_state;
        return $this;
    }
    /**
     * Gets to_state
     * @return string
     */
    public function getToState()
    {
        return $this->to_state;
    }
  
    /**
     * Sets to_state
     * @param string $to_state The [InventoryState](#type-inventorystate) of the related quantity of items after the adjustment.
     * @return $this
     */
    public function setToState($to_state)
    {
        $this->to_state = $to_state;
        return $this;
    }
    /**
     * Gets location_id
     * @return string
     */
    public function getLocationId()
    {
        return $this->location_id;
    }
  
    /**
     * Sets location_id
     * @param string $location_id The Square ID of the [Location](#type-location) where the related quantity of items are being tracked.
     * @return $this
     */
    public function setLocationId($location_id)
    {
        $this->location_id = $location_id;
        return $this;
    }
    /**
     * Gets catalog_object_id
     * @return string
     */
    public function getCatalogObjectId()
    {
        return $this->catalog_object_id;
    }
  
    /**
     * Sets catalog_object_id
     * @param string $catalog_object_id The Square generated ID of the [CatalogObject](#type-catalogobject) being tracked.
     * @return $this
     */
    public function setCatalogObjectId($catalog_object_id)
    {
        $this->catalog_object_id = $catalog_object_id;
        return $this;
    }
    /**
     * Gets catalog_object_type
     * @return string
     */
    public function getCatalogObjectType()
    {
        return $this->catalog_object_type;
    }
  
    /**
     * Sets catalog_object_type
     * @param string $catalog_object_type The [CatalogObjectType](#type-catalogobjecttype) of the [CatalogObject](#type-catalogobject) being tracked. Tracking is only supported for the `ITEM_VARIATION` type.
     * @return $this
     */
    public function setCatalogObjectType($catalog_object_type)
    {
        $this->catalog_object_type = $catalog_object_type;
        return $this;
    }
    /**
     * Gets quantity
     * @return string
     */
    public function getQuantity()
    {
        return $this->quantity;
    }
  
    /**
     * Sets quantity
     * @param string $quantity The number of items affected by the adjustment as a decimal string. Fractional quantities are not supported.
     * @return $this
     */
    public function setQuantity($quantity)
    {
        $this->quantity = $quantity;
        return $this;
    }
    /**
     * Gets total_price_money
     * @return \SquareConnect\Model\Money
     */
    public function getTotalPriceMoney()
    {
        return $this->total_price_money;
    }
  
    /**
     * Sets total_price_money
     * @param \SquareConnect\Model\Money $total_price_money The read-only total price paid for goods associated with the adjustment. Present if and only if `to_state` is `SOLD`. Always non-negative.
     * @return $this
     */
    public function setTotalPriceMoney($total_price_money)
    {
        $this->total_price_money = $total_price_money;
        return $this;
    }
    /**
     * Gets occurred_at
     * @return string
     */
    public function getOccurredAt()
    {
        return $this->occurred_at;
    }
  
    /**
     * Sets occurred_at
     * @param string $occurred_at A client-generated timestamp in RFC 3339 format that indicates when the adjustment took place. For write actions, the `occurred_at` timestamp cannot be older than 24 hours or in the future relative to the time of the request.
     * @return $this
     */
    public function setOccurredAt($occurred_at)
    {
        $this->occurred_at = $occurred_at;
        return $this;
    }
    /**
     * Gets created_at
     * @return string
     */
    public function getCreatedAt()
    {
        return $this->created_at;
    }
  
    /**
     * Sets created_at
     * @param string $created_at A read-only timestamp in RFC 3339 format that indicates when Square received the adjustment.
     * @return $this
     */
    public function setCreatedAt($created_at)
    {
        $this->created_at = $created_at;
        return $this;
    }
    /**
     * Gets source
     * @return \SquareConnect\Model\SourceApplication
     */
    public function getSource()
    {
        return $this->source;
    }
  
    /**
     * Sets source
     * @param \SquareConnect\Model\SourceApplication $source Read-only information about the application that caused the inventory adjustment.
     * @return $this
     */
    public function setSource($source)
    {
        $this->source = $source;
        return $this;
    }
    /**
     * Gets employee_id
     * @return string
     */
    public function getEmployeeId()
    {
        return $this->employee_id;
    }
  
    /**
     * Sets employee_id
     * @param string $employee_id The Square ID of the [Employee](#type-employee) responsible for the inventory adjustment.
     * @return $this
     */
    public function setEmployeeId($employee_id)
    {
        $this->employee_id = $employee_id;
        return $this;
    }
    /**
     * Gets transaction_id
     * @return string
     */
    public function getTransactionId()
    {
        return $this->transaction_id;
    }
  
    /**
     * Sets transaction_id
     * @param string $transaction_id The read-only Square ID of the [Transaction][#type-transaction] that caused the adjustment. Only relevant for payment-related state transitions.
     * @return $this
     */
    public function setTransactionId($transaction_id)
    {
        $this->transaction_id = $transaction_id;
        return $this;
    }
    /**
     * Gets refund_id
     * @return string
     */
    public function getRefundId()
    {
        return $this->refund_id;
    }
  
    /**
     * Sets refund_id
     * @param string $refund_id The read-only Square ID of the [Refund][#type-refund] that caused the adjustment. Only relevant for refund-related state transitions.
     * @return $this
     */
    public function setRefundId($refund_id)
    {
        $this->refund_id = $refund_id;
        return $this;
    }
    /**
     * Gets purchase_order_id
     * @return string
     */
    public function getPurchaseOrderId()
    {
        return $this->purchase_order_id;
    }
  
    /**
     * Sets purchase_order_id
     * @param string $purchase_order_id The read-only Square ID of the purchase order that caused the adjustment. Only relevant for state transitions from the Square for Retail app.
     * @return $this
     */
    public function setPurchaseOrderId($purchase_order_id)
    {
        $this->purchase_order_id = $purchase_order_id;
        return $this;
    }
    /**
     * Gets goods_receipt_id
     * @return string
     */
    public function getGoodsReceiptId()
    {
        return $this->goods_receipt_id;
    }
  
    /**
     * Sets goods_receipt_id
     * @param string $goods_receipt_id The read-only Square ID of the Square goods receipt that caused the adjustment. Only relevant for state transitions from the Square for Retail app.
     * @return $this
     */
    public function setGoodsReceiptId($goods_receipt_id)
    {
        $this->goods_receipt_id = $goods_receipt_id;
        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     * @param  integer $offset Offset 
     * @return boolean
     */
    public function offsetExists($offset)
    {
        return isset($this->$offset);
    }
  
    /**
     * Gets offset.
     * @param  integer $offset Offset 
     * @return mixed 
     */
    public function offsetGet($offset)
    {
        return $this->$offset;
    }
  
    /**
     * Sets value based on offset.
     * @param  integer $offset Offset 
     * @param  mixed   $value  Value to be set
     * @return void
     */
    public function offsetSet($offset, $value)
    {
        $this->$offset = $value;
    }
  
    /**
     * Unsets offset.
     * @param  integer $offset Offset 
     * @return void
     */
    public function offsetUnset($offset)
    {
        unset($this->$offset);
    }
  
    /**
     * Gets the string presentation of the object
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) {
            return json_encode(\SquareConnect\ObjectSerializer::sanitizeForSerialization($this), JSON_PRETTY_PRINT);
        } else {
            return json_encode(\SquareConnect\ObjectSerializer::sanitizeForSerialization($this));
        }
    }
}
