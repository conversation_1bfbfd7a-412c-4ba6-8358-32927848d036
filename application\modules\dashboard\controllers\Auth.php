<?php
defined('BASEPATH') or exit('No direct script access allowed');

use Dompdf\Dompdf;

class Auth extends MX_Controller
{

	public function __construct()
	{
		parent::__construct();

		$this->load->model(array(
			'auth_model',
			'wa_model'
		));
		$this->db->query('SET SESSION sql_mode = ""');
		$this->load->helper(array(
			'captcha',
			'star_sender',
			'pdf',
			'formatting'
		));
	}


	public function index()
	{

		if ($this->session->userdata('isLogIn')) {
			if ($this->session->userdata('activeRoleId') == 4) {
				redirect('ordermanage/order/pos_invoice');
			} else {
				redirect('dashboard/home');
			}
		}


		$data['title']    = display('login');
		#-------------------------------------#

		$this->form_validation->set_rules('email', display('email'), 'required|valid_email|max_length[100]|trim');
		$this->form_validation->set_rules('password', display('password'), 'required|max_length[32]|md5|trim');
		// $this->form_validation->set_rules('captcha', display('captcha'),  array('matches[captcha]', function($captcha){ 
		//         	$oldCaptcha = $this->session->userdata('captcha');
		//         	if ($captcha == $oldCaptcha) {
		//         		return true;
		//         	}
		//         }
		//     )
		// );

		#-------------------------------------#
		$data['user'] = (object)$userData = array(
			'email' 	 => $this->input->post('email', true),
			'password'   => $this->input->post('password', true),
		);
		#-------------------------------------#
		if ($this->form_validation->run()) {
			$this->session->unset_userdata('captcha');

			$user = $this->auth_model->checkUser($userData);

			if ($user->num_rows() > 0) {
				$userId = $user->row()->id;
				$chef = $this->db->select('emp_his_id,employee_id,pos_id')->where('emp_his_id', $user->row()->id)->get('employee_history')->row();
				$chefid = '';
				if (!empty($chef)) {
					$shiftcheck = true;
					$shiftmangment = $this->db->where('directory', 'shiftmangment')->where('status', 1)->get('module')->num_rows();

					if ($shiftmangment == 1) {
						$shiftcheck = $this->checkshift($chef->employee_id);
					}


					if ($shiftcheck == true) {
						if ($chef->pos_id == 1) {
							$chefid = $chef->emp_his_id;
						}
					} else {

						$this->session->set_flashdata('exception', display('not_your_working_time'));
						redirect('login');
					}
				}

				$checkPermission = $this->auth_model->userPermission2($user->row()->id);
				if ($checkPermission != NULL) {
					$permission = array();
					$permission1 = array();
					if (!empty($checkPermission)) {
						foreach ($checkPermission as $value) {
							$permission[$value->module] = array(
								'create' => $value->create,
								'read'   => $value->read,
								'update' => $value->update,
								'delete' => $value->delete
							);

							$permission1[$value->menu_title] = array(
								'create' => $value->create,
								'read'   => $value->read,
								'update' => $value->update,
								'delete' => $value->delete
							);
						}
					}
				}

				if ($user->row()->is_admin == 2) {
					$row = $this->db->select('client_id,client_email')->where('client_email', $user->row()->email)->get('setup_client_tbl')->row();
				}

				// assign fk_role_id
				$activeRoleId = null;
				$activeRoleId = $this->db->select('fk_role_id')
					->from('sec_user_access_tbl')
					->where('fk_user_id', $userId)
					->get()
					->row()
					->fk_role_id ?? null;

				$sData = array(
					'isLogIn' 	  => true,
					'isAdmin' 	  => (($user->row()->is_admin == 1) ? true : false),
					'user_type'   => $user->row()->is_admin,
					'id' 		  => $user->row()->id,
					'client_id'   => @$row->client_id,
					'fullname'	  => $user->row()->fullname,
					'user_level'  => $user->row()->user_level,
					'email' 	  => $user->row()->email,
					'image' 	  => $user->row()->image,
					'last_login'  => $user->row()->last_login,
					'last_logout' => $user->row()->last_logout,
					'ip_address'  => $user->row()->ip_address,
					'permission'  => json_encode(@$permission),
					'label_permission'  => json_encode(@$permission1),
					'activeRoleId' => $activeRoleId
				);

				//store date to session 
				$this->session->set_userdata($sData);
				//update database status
				$this->auth_model->last_login();
				//welcome message
				$this->session->set_flashdata('message', display('welcome_back') . ' ' . $user->row()->fullname);

				if (!empty($chefid)) {
					redirect('ordermanage/order/allkitchen');
				} else if ($user->row()->counter == 1) {
					redirect('ordermanage/order/counterboard');
				} else if ($activeRoleId != null && $activeRoleId == 4) {
					redirect('ordermanage/order/pos_invoice');
				} else {
					redirect('dashboard/home');
				}
			} else {
				$this->session->set_flashdata('exception', display('incorrect_email_or_password'));
				redirect('login');
			}
		} else {

			// $captcha = create_captcha(array(
			//     'img_path'      => './assets/img/captcha/',
			//     'img_url'       => base_url('assets/img/captcha/'),
			//     'font_path'     => './assets/fonts/themify.ttf',
			//     'img_width'     => '328',
			//     'img_height'    => 64,
			//     'expiration'    => 600, //5 min
			//     'word_length'   => 4,
			//     'font_size'     => 40,
			//     'img_id'        => 'Imageid',
			//     'pool'          => '23456789abcdefghijkmnpqrstuvwxyz',

			//     // White background and border, black text and red grid
			//     'colors'        => array(
			//             'background' => array(255, 255, 255),
			//             'border' => array(228, 229, 231),
			//             'text' => array(49, 141, 1),
			//             'grid' => array(241, 243, 246)
			//     )
			// ));
			// $data['captcha_word'] = $captcha['word'];
			// $data['captcha_image'] = $captcha['image'];
			// $this->session->set_userdata('captcha', $captcha['word']);

			echo Modules::run('template/login', $data);
		}
	}

	public function logout()
	{
		//update database status
		$this->auth_model->last_logout();
		//destroy session
		$this->session->sess_destroy();
		redirect('login');
	}

	public function checkshift($id)
	{
		$this->db->select('shift.*');
		$this->db->from('shift_user as shiftuser');
		$this->db->join('shifts as shift', 'shiftuser.shift_id=shift.id', 'left');
		$this->db->where('shiftuser.emp_id', $id);
		$shift = $this->db->get()->row();
		$timezone = $this->db->select('timezone')->get('setting')->row();
		$tz_obj = new DateTimeZone($timezone->timezone);
		$today = new DateTime("now", $tz_obj);
		$today_formatted = $today->format('H:i:s');


		if ($today_formatted >= $shift->start_Time && $today_formatted <= $shift->end_Time) {

			return true;
		} else {

			return false;
		}
	}

	public function testWa()
	{
		// set today
		$start_date = date('Y-m-d 00:00:00');  // today at 00:00
		$end_date   = date('Y-m-d 23:59:59');  // today at 23:59

		$start_date = '2025-05-04 00:00:00';
		$end_date   = '2025-05-04 23:59:59';

		// set yesterday
		$yesterday_start = date('Y-m-d 00:00:00', strtotime($start_date . ' -1 day'));
		$yesterday_end   = date('Y-m-d 23:59:59', strtotime($end_date . ' -1 day'));

		// Set Indonesian locale
		setlocale(LC_TIME, 'id_ID.utf8');
		$tanggal = strftime("%A, %d %B %Y", strtotime($start_date));
		$tanggal_kemarin = strftime('%A, %d %B %Y', strtotime($yesterday_start));

		// Format top 5 minuman
		$top5minuman = $this->wa_model->get_top5_menu_items_by_category($start_date, $end_date, 'minuman');
		$top5minumanText = "🥤 *Top 5 Minuman Terlaris:*\n";
		$i = 1;
		foreach ($top5minuman as $value) {
			$top5minumanText .= "{$i}. {$value->item_name} - {$value->total_qty} pcs\n";
			$i++;
		}
		$top5minumanText .= "━━━━━━━━━━━━━━━━━━━━━━━\n\n";

		// Format top 5 prasmanan
		$top5prasmanan = $this->wa_model->get_top5_menu_items_by_category($start_date, $end_date, 'prasmanan');
		$top5prasmananText = "🍱 *Top 5 Prasmanan Terlaris:*\n";
		$i = 1;
		foreach ($top5prasmanan as $value) {
			$top5prasmananText .= "{$i}. {$value->item_name} - {$value->total_qty} pcs\n";
			$i++;
		}
		$top5prasmananText .= "━━━━━━━━━━━━━━━━━━━━━━━\n\n";

		// Format top 5 alacarte
		$top5alacarte = $this->wa_model->get_top5_menu_items_by_category($start_date, $end_date, 'alacarte');
		$top5alacarteText = "🍽️ *Top 5 Alacarte Terlaris:*\n";
		$i = 1;
		foreach ($top5alacarte as $value) {
			$top5alacarteText .= "{$i}. {$value->item_name} - {$value->total_qty} pcs\n";
			$i++;
		}
		$top5alacarteText .= "━━━━━━━━━━━━━━━━━━━━━━━\n\n";

		// Format top 5 customers
		$customers = $this->wa_model->get_top5_customers($start_date, $end_date);
		$customerText = "🙋‍♂️ *Top 5 Customer (berdasarkan nilai transaksi):*\n";
		$i = 1;
		foreach ($customers as $cust) {
			$nominal = number_format($cust->total_sales, 0, ',', '.');
			$customerText .= "{$i}. {$cust->customer_name} (Rp {$nominal})\n";
			$i++;
		}
		$customerText .= "━━━━━━━━━━━━━━━━━━━━━━━\n\n";

		// payment recap
		$payments = $this->wa_model->get_payment_method_recap($start_date, $end_date);
		$paymentText = "💳 *Metode Pembayaran:*\n";
		if (!empty($payments)) {
			$i = 1;
			foreach ($payments as $pay) {
				$amount = helperFormatNumber([
					'number' => $pay->totalamount,
					'code' => 'thousandSeparator',
					'decimalPlace' => 0
				]);
				$paymentText .= "{$i}️⃣ {$pay->payment_method} : Rp {$amount}\n";
				$i++;
			}
		} else {
			$paymentText .= "Tidak ada data pembayaran.\n";
		}
		$paymentText .= "━━━━━━━━━━━━━━━━━━━━━━━\n\n";

		// part analisa
		$total_today = $this->wa_model->get_total_payment($start_date, $end_date);
		$total_yesterday = $this->wa_model->get_total_payment($yesterday_start, $yesterday_end);

		// Avoid division by zero
		if ($total_yesterday > 0) {
			$diff = $total_today - $total_yesterday;
			$percent = round(abs($diff / $total_yesterday * 100));
			$trend = $diff >= 0 ? 'meningkat' : 'menurun';
		} else {
			$trend = 'stabil';
			$percent = 0;
		}

		// Build message line
		$analisa_line = "Penjualan hari ini *$trend $percent%* dibandingkan hari $tanggal_kemarin.\n";
		// $analisa_line .= "Penjualan hari ini: $total_today Penjualan kemarin: $total_yesterday \n";

		// ringkasan penjualan
		$formatted_total_today = helperFormatNumber([
			'number' => $total_today,
			'code' => 'thousandSeparator',
			'decimalPlace' => 0
		]);

		$total_transaksi = $this->wa_model->get_total_transaksi($start_date, $end_date);
		$formatted_total_transaksi = helperFormatNumber([
			'number' => $total_transaksi,
			'code' => 'thousandSeparator',
			'decimalPlace' => 0
		]); 

		$busket_size = $total_today / $total_transaksi; 
		$formatted_busket_size = helperFormatNumber([
			'number' => $busket_size,
			'code' => 'thousandSeparator',
			'decimalPlace' => 0
		]); 

		$total_pelanggan = $this->wa_model->get_total_pelanggan($start_date, $end_date) ?? 0; 
		$formatted_total_pelanggan = helperFormatNumber([
			'number' => $total_pelanggan,
			'code' => 'thousandSeparator',
			'decimalPlace' => 0
		]);

		// rekap jenis pelanggan
		$recapCustomerTypes = $this->wa_model->get_recap_customer_type($start_date, $end_date); 
		$recapCustomerTypeText = "🧾 *Rekap Jenis Transaksi:*\n";
		$i = 1;
		foreach ($recapCustomerTypes as $recapCustomerType) {
			$recapCustomerTypeText .= "{$i}. {$recapCustomerType->name_type} - {$recapCustomerType->total_type} transaksi\n";
			$i++;
		}
		$recapCustomerTypeText .= "━━━━━━━━━━━━━━━━━━━━━━━\n\n";

		// Compose full message
		$formattedMessage = "📊 *LAPORAN PENJUALAN HARIAN*\n"
			. "*Tudung Saji Signature*\n"
			. "🗓️ $tanggal\n\n"
			. "📈 *Analisa Penjualan:*\n"
			. $analisa_line
			. "━━━━━━━━━━━━━━━━━━━━━━━\n"
			. "💰 *Ringkasan Penjualan*\n"
			. "🔸 Total Penjualan     : Rp $formatted_total_today\n"
			. "🔸 Jumlah Transaksi    : $formatted_total_transaksi\n"
			. "🔸 Jumlah Pelanggan    : $formatted_total_pelanggan\n"
			. "🔸 Basket Size         : Rp $formatted_busket_size\n"
			. "━━━━━━━━━━━━━━━━━━━━━━━\n\n"
			. $recapCustomerTypeText
			. $paymentText
			. $customerText
			. $top5minumanText
			. $top5prasmananText
			. $top5alacarteText
			. "📦 *Detail Penjualan per Item:*\n"
			. "Terlampir dalam laporan PDF berikut.\n"
			. "━━━━━━━━━━━━━━━━━━━━━━━"
		;
		// die("formattedMessage:\n" . $formattedMessage);

		// generate pdf file
		$payload_generate_pdf['start_date'] = $start_date;
		$payload_generate_pdf['end_date'] = $end_date;
		$payload_generate_pdf['pdf_name'] = 'Laporan_Penjualan_Item_' . date('Y-m-d_H-i-s') . '.pdf';
		$this->generate_pdf($payload_generate_pdf);
		$fileUrl = base_url('assets/custom/'. $payload_generate_pdf['pdf_name']);

		// Send text
		$result1 = starSender_send([
			'to'      => 'ilham',
			'message' => $formattedMessage
		]);
		if (!$result1) die('❌ Failed to send text message.');

		// Send file
		$result2 = starSender_send([
			'to'      => 'ilham',
			'fileUrl' => $fileUrl
		]);
		if (!$result2) die('❌ Failed to send file.');

		return $this->response->setJSON($result2);
	}

	public function generate_pdf($params = [])
	{
		$start_date = $params['start_date'] ?? '2025-05-01 00:00:00';
		$end_date   = $params['end_date'] ?? '2025-05-01 23:59:59';

		// Load data
		$menus = $this->wa_model->get_sell_items($start_date, $end_date);
		if (!is_array($menus)) die('❌ Failed to fetch top menu data.');

		// Construct HTML for PDF
		$html = '
        <h1>Laporan Penjualan Item</h1>
        <p>Periode: ' . date('d M Y', strtotime($start_date)) . ' - ' . date('d M Y', strtotime($end_date)) . '</p>

        <table style="width:100%; border-collapse:collapse;">
            <thead>
                <tr>
                    <th style="border:1px solid #000; padding:5px;">No</th>
                    <th style="border:1px solid #000; padding:5px;">Nama Item</th>
                    <th style="border:1px solid #000; padding:5px;">Jumlah Terjual</th>
					<th style="border:1px solid #000; padding:5px;">Harga Satuan</th>
					<th style="border:1px solid #000; padding:5px;">Total Harga Jual</th>
                </tr>
            </thead>
            <tbody>';

		foreach ($menus as $index => $menu) {
			$total_qty = helperFormatNumber([
				'number' => $menu->total_qty,
				'code' => 'thousandSeparator',
				'decimalPlace' => 0
			]);

			$total_price = helperFormatNumber([
				'number' => $menu->total_sales,
				'code' => 'thousandSeparator',
				'decimalPlace' => 0
			]);

			$single_price = helperFormatNumber([
				'number' => $menu->single_price,
				'code' => 'thousandSeparator',
				'decimalPlace' => 0
			]);

			$html .= '
                <tr>
                    <td style="border:1px solid #000; padding:5px;">' . ($index + 1) . '</td>
                    <td style="border:1px solid #000; padding:5px;">' . htmlspecialchars($menu->item_name) . '</td>
                    <td style="border:1px solid #000; padding:5px; text-align:center;">' . $total_qty . '</td>
					<td style="border:1px solid #000; padding:5px; text-align:right;">Rp. ' . $single_price . '</td>
					<td style="border:1px solid #000; padding:5px; text-align:right;">Rp. ' . $total_price . '</td>
                </tr>';
		}

		$html .= '
            </tbody>
        </table>';

		// Generate PDF
		$pdfName = $params['pdf_name'] ?? 'laporan_penjualan_item_uji_coba.pdf';
		$pdfUrl = generate_pdf_file($html, $pdfName);

		return true;
	}

	public function test_pdf($params = [])
	{
		$start_date = $params['start_date'] ?? '2025-05-01 00:00:00';
		$end_date   = $params['end_date'] ?? '2025-05-01 23:59:59';

		// Load data
		$menus = $this->wa_model->get_menu_items($start_date, $end_date);
		if (!is_array($menus)) die('❌ Failed to fetch top menu data.');

		// Construct HTML for PDF
		$html = '
        <h1>Laporan Penjualan Item</h1>
        <p>Periode: ' . date('d M Y', strtotime($start_date)) . ' - ' . date('d M Y', strtotime($end_date)) . '</p>

        <table style="width:100%; border-collapse:collapse;">
            <thead>
                <tr>
                    <th style="border:1px solid #000; padding:5px;">No</th>
                    <th style="border:1px solid #000; padding:5px;">Nama Item</th>
                    <th style="border:1px solid #000; padding:5px;">Jumlah Terjual</th>
                </tr>
            </thead>
            <tbody>';

		foreach ($menus as $index => $menu) {
			$html .= '
                <tr>
                    <td style="border:1px solid #000; padding:5px;">' . ($index + 1) . '</td>
                    <td style="border:1px solid #000; padding:5px;">' . htmlspecialchars($menu->item_name) . '</td>
                    <td style="border:1px solid #000; padding:5px; text-align:center;">' . (int)$menu->total_qty . '</td>
                </tr>';
		}

		$html .= '
            </tbody>
        </table>';

		// Generate PDF
		$pdfName = $params['pdf_name'] ?? 'laporan_penjualan_item_uji_coba.pdf';
		$pdfUrl = generate_pdf_file($html, $pdfName);

		echo 'PDF berhasil disimpan: <a href="' . $pdfUrl . '" target="_blank">Download Laporan Penjualan</a>';
		// return true;
	}
}
