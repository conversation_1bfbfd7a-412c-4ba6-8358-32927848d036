<?php
/**
 * NOTE: This class is auto generated by the swagger code generator program. 
 * https://github.com/swagger-api/swagger-codegen 
 * Do not edit the class manually.
 */

namespace SquareConnect\Api;

use \SquareConnect\Configuration;
use \SquareConnect\ApiClient;
use \SquareConnect\ApiException;
use \SquareConnect\ObjectSerializer;

/**
 * V1EmployeesApiTest Class Doc Comment
 *
 * @category Class
 * @package  SquareConnect
 * <AUTHOR> Inc.
 * @license  http://www.apache.org/licenses/LICENSE-2.0 Apache Licene v2
 * @link     https://squareup.com/developers
 */
class V1EmployeesApiTest extends \PHPUnit_Framework_TestCase
{

    /**
     * Setup before running each test case
     */
    public static function setUpBeforeClass() {

    }

    /**
     * Clean up after running each test case
     */
    public static function tearDownAfterClass() {

    }

    /**
     * Test case for createEmployee
     *
     * Creates an employee for a business.
     *
     */
    public function test_createEmployee() {

    }
    /**
     * Test case for createEmployeeRole
     *
     * Creates an employee role you can then assign to employees.
     *
     */
    public function test_createEmployeeRole() {

    }
    /**
     * Test case for createTimecard
     *
     * Creates a timecard for an employee. Each timecard corresponds to a single shift.
     *
     */
    public function test_createTimecard() {

    }
    /**
     * Test case for deleteTimecard
     *
     * Deletes a timecard. Deleted timecards are still accessible from Connect API endpoints, but the value of their deleted field is set to true. See Handling deleted timecards for more information.
     *
     */
    public function test_deleteTimecard() {

    }
    /**
     * Test case for listCashDrawerShifts
     *
     * Provides the details for all of a location's cash drawer shifts during a date range. The date range you specify cannot exceed 90 days.
     *
     */
    public function test_listCashDrawerShifts() {

    }
    /**
     * Test case for listEmployeeRoles
     *
     * Provides summary information for all of a business's employee roles.
     *
     */
    public function test_listEmployeeRoles() {

    }
    /**
     * Test case for listEmployees
     *
     * Provides summary information for all of a business's employees.
     *
     */
    public function test_listEmployees() {

    }
    /**
     * Test case for listTimecardEvents
     *
     * Provides summary information for all events associated with a particular timecard.
     *
     */
    public function test_listTimecardEvents() {

    }
    /**
     * Test case for listTimecards
     *
     * Provides summary information for all of a business's employee timecards.
     *
     */
    public function test_listTimecards() {

    }
    /**
     * Test case for retrieveCashDrawerShift
     *
     * Provides the details for a single cash drawer shift, including all events that occurred during the shift.
     *
     */
    public function test_retrieveCashDrawerShift() {

    }
    /**
     * Test case for retrieveEmployee
     *
     * Provides the details for a single employee.
     *
     */
    public function test_retrieveEmployee() {

    }
    /**
     * Test case for retrieveEmployeeRole
     *
     * Provides the details for a single employee role.
     *
     */
    public function test_retrieveEmployeeRole() {

    }
    /**
     * Test case for retrieveTimecard
     *
     * Provides the details for a single timecard.
     *
     */
    public function test_retrieveTimecard() {

    }
    /**
     * Test case for updateEmployee
     *
     * V1 UpdateEmployee
     *
     */
    public function test_updateEmployee() {

    }
    /**
     * Test case for updateEmployeeRole
     *
     * Modifies the details of an employee role.
     *
     */
    public function test_updateEmployeeRole() {

    }
    /**
     * Test case for updateTimecard
     *
     * Modifies a timecard's details. This creates an API_EDIT event for the timecard. You can view a timecard's event history with the List Timecard Events endpoint.
     *
     */
    public function test_updateTimecard() {

    }
}
