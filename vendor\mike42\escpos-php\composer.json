{"name": "mike42/escpos-php", "type": "library", "description": "PHP receipt printer library for use with ESC/POS-compatible thermal and impact printers", "homepage": "https://github.com/mike42/escpos-php", "keywords": ["barcode", "epson", "receipt-printer", "printer", "escpos"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "config": {"platform": {"php": "7.0.0"}}, "require": {"php": ">=7.0.0", "ext-json": "*", "ext-intl": "*", "ext-zlib": "*", "mike42/gfx-php": "^0.6"}, "suggest": {"ext-imagick": "Will be used for image printing if present. Required for PDF printing or use of custom fonts.", "ext-gd": "Used for image printing if present."}, "require-dev": {"phpunit/phpunit": "^6.5", "squizlabs/php_codesniffer": "^3.3"}, "autoload": {"psr-4": {"Mike42\\": "src/Mike42"}}}