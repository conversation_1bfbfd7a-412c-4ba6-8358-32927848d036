@import url(http://fonts.googleapis.com/css?family=Roboto:400,300,500,700);

body {
    color: #797979;
    background-color: #f5f5f5 !important;
    font-family: 'Roboto', sans-serif;
    padding: 0px !important;
    margin: 0px !important;
    font-size: 14px;
}
html {
    overflow-x: hidden;
    position: relative;
    min-height: 100%;
}
.wrapper {
    text-align: center;
    width:340px;
    margin:0 auto;
    margin-top: 100px;
}
.checkmark {
    width: 160px;
    margin: 0 auto;
    padding-top: 40px;
}
.path {
    stroke-dasharray: 1000;
    stroke-dashoffset: 0;
    animation: dash 2s ease-in-out;
    -webkit-animation: dash 2s ease-in-out;
}

.spin {
    animation: spin 2s;
    -webkit-animation: spin 2s;
    transform-origin: 50% 50%;
    -webkit-transform-origin: 50% 50%;
}

p {
    font-family: sans-serif;
    font-size: 30px;
    font-weight: bold;
    margin: 20px auto;
    text-align: center;
    animation: text .5s linear .4s;
    -webkit-animation: text .4s linear .3s;
}

@-webkit-keyframes dash {
    0% {
        stroke-dashoffset: 1000;
    }
    100% {
        stroke-dashoffset: 0;
    }
}

@keyframes dash {
    0% {
        stroke-dashoffset: 1000;
    }
    100% {
        stroke-dashoffset: 0;
    }
}

@-webkit-keyframes spin {
    0% {
        -webkit-transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
    }
}

@keyframes spin {
    0% {
        -webkit-transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
    }
}

@-webkit-keyframes text {
    0% {
        opacity: 0; }
    100% {
        opacity: 1;
    }
}

@keyframes text {
    0% {
        opacity: 0; }
    100% {
        opacity: 1;
    }
}

.row {
    margin-right: -10px;
    margin-left: -10px;
}

.col-lg-1, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg-2, .col-lg-3, .col-lg-4, 
.col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-md-1, .col-md-10, 
.col-md-11, .col-md-12, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, 
.col-md-7, .col-md-8, .col-md-9, .col-sm-1, .col-sm-10, .col-sm-11, .col-sm-12, 
.col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, 
.col-sm-9, .col-xs-1, .col-xs-10, .col-xs-11, .col-xs-12, .col-xs-2, .col-xs-3, 
.col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9 {
    padding-left: 10px;
    padding-right: 10px;
}
a{
    color: #333b4d;
    cursor: pointer;
    outline: none !important;
}
a:hover,a:focus{
    color: #2A3542;
    text-decoration: none;
}

ul, ol {
    margin-top: 0;
    margin-bottom: 0px;
}

.list-group{
    margin-bottom: 0px;
}
.popover {
    border-radius: 3px;
}
.lead .badge{
    top: -2px;
    position: relative;
}
.lead small.link{
    font-size: 12px;
    margin-left: 10px;
    position: relative;
    top: -2px;
}
/*panel*/
.panel{
    padding: 20px 30px;
    border: none;
    border: 1px solid rgba(0,0,0,0.08);
    margin-bottom: 20px;
    box-shadow: none;
}
.panel .panel-body{
    padding: 0px;
    padding-top: 20px;
}
.panel .panel-body p{
    margin: 0px;
}
.panel .panel-body p+p {
    margin-top: 15px;
}
.panel .panel-body p.lead2{
    margin-bottom: 15px;
    margin-top: 0;
}
.panel-default > .panel-heading {
    background-color: #FFFFFF;
    border-color: #DDDDDD;
    color: #797979;
}
.panel-heading {
    border-color:#eff2f7 ;
    font-size: 16px;
    padding: 0;
    padding-bottom: 15px;
}
.panel-title {
    font-size: 18px;
    margin-bottom: 0;
    margin-top: 0;
}
.panel-footer {
    margin: 0px -30px -30px;
    background: #eee;
    border-top: 0px;
}
.panel-group .panel .panel-heading {
    padding-bottom: 0;
    border-bottom: 0;
}
.panel-group .panel {
    margin-bottom: 0;
    border-radius: 0;
}

/*label*/

.label {
    padding: 0.4em .8em;
}

.label-default {
    background-color: #a1a1a1;
}

.label-primary {
    background-color: #086fd1;
}

.label-success {
    background-color: #2eb398;
}

.label-info {
    background-color: #5bc0de;
}

.label-warning {
    background-color: #f4984e;
}

.label-danger {
    background-color: #FF6C60;
}

.label-inverse {
    background-color: #344860;
}
.label-purple{
    background-color: #7266ba;
}
.label-pink{
    background-color: #f13c6e;
}



.badge {
    display: inline-block;
    min-width: 10px;
    padding: 3px 6px;
    font-size: 11px !important;
    font-weight: normal;
    color: #ffffff;
    line-height: 1;
    vertical-align: baseline;
    white-space: nowrap;
    text-align: center;
    background-color: #777777;
    border-color: #777777;
    border-radius: 12px;
}
.badge-danger{
    background-color: #cb2a2a;
}
.badge-info{
    background-color: #1ca8dd;
}
.btn{
    border-radius: 2px;
    opacity: 0.9;
}
.btn:hover{
    opacity: 1;
}
.btn-primary,.btn-success,.btn-info,.btn-warning,.btn-danger,.btn-inverse,.btn-purple,.btn-pink{
    color: #FFFFFF !important;
}
.btn-default:hover,.btn-default:focus,.btn-default:active{
    background-color: #e6eaed;
}

.btn-primary,.btn-primary:hover,.btn-primary:focus,.btn-primary:active,
.btn-primary.active, .btn-primary.focus, .btn-primary:active, .btn-primary:focus, .btn-primary:hover, .open>.dropdown-toggle.btn-primary{
    background-color: #086fd1;
    border: 1px solid #086fd1;
}
.btn-success,.btn-success:hover,.btn-success:focus,.btn-success:active,
.btn-success.active, .btn-success.focus, .btn-success:active, .btn-success:focus, .btn-success:hover, .open>.dropdown-toggle.btn-success{
    background-color: #33b86c;
    border: 1px solid #33b86c;
}
.btn-info,.btn-info:hover,.btn-info:focus,.btn-info:active,
.btn-info.active, .btn-info.focus, .btn-info:active, .btn-info:focus, .btn-info:hover, .open>.dropdown-toggle.btn-info{
    background-color: #1ca8dd;
    border: 1px solid #1ca8dd;
}
.btn-warning,.btn-warning:hover,.btn-warning:focus,.btn-warning:active,
.btn-warning.active, .btn-warning.focus, .btn-warning:active, .btn-warning:focus, .btn-warning:hover, .open>.dropdown-toggle.btn-warning{
    background-color: #ebc142;
    border: 1px solid #ebc142;
}
.btn-danger,.btn-danger:active,.btn-danger:focus,.btn-danger:hover,
.btn-danger.active, .btn-danger.focus, .btn-danger:active, .btn-danger:focus, .btn-danger:hover, .open>.dropdown-toggle.btn-danger{
    background-color: #cb2a2a;
    border: 1px solid #cb2a2a;
}
.btn-inverse,.btn-inverse:hover,.btn-inverse:focus,.btn-inverse:active,
.btn-inverse.active, .btn-inverse.focus, .btn-inverse:active, .btn-inverse:focus, .btn-inverse:hover, .open>.dropdown-toggle.btn-inverse{
    background-color: #14082d;
    border: 1px solid #14082d;
    color: #FFFFFF;
}
.btn-purple,.btn-purple:hover,.btn-purple:focus,.btn-purple:active{
    background-color: #551A8B;
    border: 1px solid #551A8B;
    color: #FFFFFF;
}
.btn-pink,.btn-pink:hover,.btn-pink:focus,.btn-pink:active{
    background-color: #f13c6e;
    border: 1px solid #f13c6e;
    color: #FFFFFF;
}
/*text color*/
.text-white {
    color: #ffffff;
}

.text-danger {
    color: #cb2a2a;
}

.text-muted {
    color: #98a6ad;
}

.text-primary {
    color: #086fd1;
}

.text-warning {
    color: #ebc142;
}

.text-success {
    color: #33b86c;
}

.text-info {
    color: #1ca8dd;
}

.text-inverse {
    color: #14082d;
}

.text-pink {
    color: #F13C6E;
}
.text-purple {
    color: #551A8B;
}
/* text-color */
.text-dark {
    color: #797979;
}
/*modal*/
.modal .modal-dialog .modal-content {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    border-color: #DDDDDD;
    padding: 30px;
    border-radius: 2px;
}
.modal .modal-dialog .modal-content .modal-header {
    margin: 0;
    padding: 0;
    border-bottom-width: 2px;
    padding-bottom: 15px;
}
.modal .modal-dialog .modal-content .modal-body {
    padding: 20px 0;
}
.modal .modal-dialog .modal-content .modal-footer {
    padding: 0;
    padding-top: 15px;
}
.modal-full {
    width: 98%;
}

/*text input*/

.form-control {
    background-color: #fafafa;
    color: rgba(0,0,0,0.6);
    font-size: 14px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
    -webkit-box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
    -moz-box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
    box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
    border: 1px solid #003567;
    box-shadow: none;
}
.form-control:focus {
    border: 1px solid #0055A0;
    background: #FFF;
    box-shadow: none;
}



input, textarea, select, button {
    outline: none !important;
}
textarea.form-control {
    height: auto;
    min-height: 100px;
}
.input-group-addon {
    border: 1px solid #eee;
    border-radius: 2px;
}
/*list*/




/*dropdown select bg*/
.dropdown-menu {
    border-radius: 2px;
    -webkit-box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    margin-top: 0px;
}
.dropdown-menu > li > a:hover, .dropdown-menu > li > a:focus {
    background-color: #f1f1f1 !important;
    color: #141719;
    text-decoration: none;
    outline: none;
}
.dropdown-menu .divider {
    margin: 6px 0;
}

/*split dropdown btn*/

.btn-white {
    background-clip: padding-box;
    background-color: #FFFFFF;
    border-color: rgba(150, 160, 180, 0.3);
    box-shadow: 0 -1px 1px rgba(0, 0, 0, 0.05) inset;
}

/*breadcrumbs*/

.breadcrumb {
    background-color: transparent;
}


/*tab*/

.nav-tabs > li > a {
    margin-right: 1px;
}



/*nav inverse*/

.navbar-inverse {
    background-color: #7087A3;
    border-color: #7087A3;
}

.navbar-inverse .navbar-nav > .active > a, .navbar-inverse .navbar-nav > .active > a:hover, .navbar-inverse .navbar-nav > .active > a:focus,
.navbar-inverse .navbar-nav > .open > a, .navbar-inverse .navbar-nav > .open > a:focus{
    background-color: #61748d;
}

.navbar-inverse .navbar-nav > li a:hover {
    color: #2A3542;
}

.navbar-inverse .navbar-nav > li > ul > li a:hover {
    color: #fff;
}

.navbar-inverse .navbar-brand {
    color: #FFFFFF;
}

.navbar-inverse .navbar-nav > li > a {
    color: #fff;
}

.navbar-inverse .navbar-nav > .dropdown > a .caret {
    border-bottom-color: #fff;
    border-top-color: #fff;
}

.navbar-inverse .navbar-nav .open .dropdown-menu > li > a {
    color: #000;
}
.navbar-inverse .navbar-nav .open .dropdown-menu > li > a:hover {
    color: #fff;
}

/*nav justified*/

.nav-justified li:last-child > a:hover, .nav-justified li.active:last-child > a {
    border-radius: 0 4px 0 0 !important;
    -webkit-border-radius: 0 4px 0 0 !important;
}

/*list group*/

.list-group-item.active, .list-group-item.active:hover, .list-group-item.active:focus {
    background-color: #ddd;
    border-color: #ddd;
    color: #444;
    z-index: 2;
}

.list-group-item,.list-group-item:first-child ,.list-group-item:last-child  {
    border-radius: 0px;
    padding: 12px 20px;
}

.list-group-item-heading {
    font-weight: 300;
}
.list-group-item.active>.badge, .nav-pills>.active>a>.badge {
    color: #086fd1;
}
.list-group-item.active .list-group-item-text, .list-group-item.active:focus .list-group-item-text, .list-group-item.active:hover .list-group-item-text {
    color: #086fd1;
}
/*progress*/

.progress {
    box-shadow: none;
    background: #f0f2f7;
}

/*alert*/

.alert-success, .alert-danger, .alert-info, .alert-warning {
    border: none;
}

/*table*/

.table thead > tr > th, .table tbody > tr > th, .table tfoot > tr > th, .table thead > tr > td, .table tbody > tr > td, .table tfoot > tr > td {
    padding: 10px;
}

.bg-primary{
    background-color: rgba(110, 140, 215, 0.8);
}
.bg-success{
    background-color: rgba(51, 184, 108, 0.8);
}
.bg-info{
    background-color: rgba(28, 168, 221, 0.8);
}
.bg-warning{
    background-color: rgba(235, 193, 66, 0.8);
}
.bg-danger{
    background-color: rgba(203, 42, 42, 0.8);
}
.bg-muted {
    background-color: #d0d0d0;
}
.bg-inverse {
    background-color: rgba(20, 8, 45, 0.8);
}
.bg-purple {
    background-color: rgba(85, 26, 139, 0.8) !important;
}
.bg-pink {
    background-color: rgba(241, 60, 110, 0.8);
}
.white-bg{
    background-color: #ffffff;
}
.bg-fb{
    background-color: #3b5998;
}
.bg-gp{
    background-color: #dd4b39;
}
.bg-tw{
    background-color: #00aced;
}
.bg-dribbble{
    background-color: #ea4c89;
}




/* ------ btn-custom -----*/
.btn-custom{
    background: transparent;
    -moz-border-radius: 2px;
    -webkit-border-radius: 2px;
    border-radius: 2px;
    border-width: 1px;
    -webkit-transition: all 400ms ease-in-out;
    -moz-transition: all 400ms ease-in-out;
    -o-transition: all 400ms ease-in-out;
    transition: all 400ms ease-in-out;
}
.btn-custom.btn-default:hover,.btn-custom.btn-default:active,.btn-custom.btn-default:focus{
    color: #333 !important;
}
.btn-custom.btn-primary{
    color: #086fd1 !important;
}
.btn-custom.btn-success{
    color: #33b86c !important;
}
.btn-custom.btn-info{
    color: #1ca8dd !important;
}
.btn-custom.btn-warning{
    color: #ebc142 !important;
}
.btn-custom.btn-danger{
    color: #cb2a2a !important;
}
.btn-custom.btn-inverse{
    color: #14082d !important;
}
.btn-custom.btn-purple{
    color: #551A8B !important;
}
.btn-custom.btn-pink{
    color: #f13c6e !important;
}
.btn-custom:hover,.btn-custom:focus{
    color: #FFFFFF !important;
}

.btn-rounded {
    border-radius: 2em;
}

/* Table */
.table {
    margin-bottom: 0px;
}

.panel.panel-color .panel-heading {
    margin-top: -20px;
    margin-left: -30px;
    margin-right: -30px;
    padding: 20px 30px;
    border-bottom: 0;
}
.panel-group {
    margin-bottom: 30px;
}
.panel-group >.panel.panel-color .panel-heading {
    margin-top: -20px;
    margin-left: -30px;
    margin-right: -30px;
    padding: 20px 30px;
    border-bottom: 0;
    margin-bottom: -20px !important;
    border-radius: 0px !important;
}

.panel.panel-primary .panel-heading {
    background-color: #086fd1;
    color: #fff;
}

.panel.panel-success .panel-heading {
    background-color: #33b86c;
    color: #fff;
}

.panel.panel-info .panel-heading {
    background-color: #1ca8dd;
    color: #fff;
}

.panel.panel-warning .panel-heading {
    background-color: #ebc142;
    color: #fff;
}

.panel.panel-danger .panel-heading {
    background-color: #cb2a2a;
    color: #fff;
}

.panel.panel-inverse .panel-heading {
    background-color: #14082d;
    color: #fff;
}

.panel.panel-purple .panel-heading {
    background-color: #551A8B;
    color: #fff;
}

.panel.panel-pink .panel-heading {
    background-color: #f13c6e;
    color: #fff;
}



.progress {
    overflow: hidden;
    margin-bottom: 18px;
    background-color: #f5f5f5;
    border-radius: 0px;
    -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1) !important;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1) !important;
    height: 10px;
}
.progress-bar{
    font-size: 8px;
    line-height: 12px;
    font-weight: 600;
}
.progress.progress-md {
    height: 15px !important;
}
.progress.progress-lg {
    height: 20px !important;
}
.progress.progress-md .progress-bar {
    font-size: 10.8px;
    line-height: 14.4px;
}
.progress.progress-lg .progress-bar {
    font-size: 12px;
    line-height: 20px;
}
.progress-bar-primary {
    background-color: #086fd1;
}

.progress-bar-success {
    background-color: #33b86c;
}

.progress-bar-info {
    background-color: #1ca8dd;
}

.progress-bar-warning {
    background-color: #ebc142;
}

.progress-bar-danger {
    background-color: #cb2a2a;
}

.progress-bar-inverse {
    background-color: #14082d;
}

.progress-bar-purple {
    background-color: #551A8B;
}

.progress-bar-pink {
    background-color: #f13c6e;
}

.pagination>li>a, .pagination>li>span {
    color: #373e4a;
    background-color: #fff;
    border: 1px solid #ddd;
}
.pagination>.active>a, .pagination>.active>span, .pagination>.active>a:hover, .pagination>.active>span:hover, .pagination>.active>a:focus, .pagination>.active>span:focus {
    background-color: #1c2b36;
    border-color: #1c2b36;
}

.m-t-100 {
    margin-top: 100px !important;
}

.m-t-20 {
    margin-top: 20px !important;
}

.content {
    margin: 20px auto;
    width: 900px;
}

.content > .container-fluid {
    padding-left: 20px;
    padding-right: 20px;
}


.table>thead>tr>td.middle-align, .table>tbody>tr>td.middle-align, .table>tfood>tr>td.middle-align, .table>thead>tr>th.middle-align, .table>tbody>tr>th.middle-align, .table>tfood>tr>th.middle-align {
    vertical-align: middle;
}

table.focus-on tbody tr.focused th, table.focus-on tbody tr.focused td {
    background-color: #086fd1;
    color: #ffffff;
}

.portlet {
    margin-bottom: 20px;
    background: #fff;
    transition: all 0.4s;
    -moz-transition: all 0.4s;
    -webkit-transition: all 0.4s;
    -o-transition: all 0.4s;
    border: 1px solid rgba(0,0,0,0.08);
}

.portlet .portlet-heading {
    padding: 20px 30px;
    line-height: 38px;
    min-height: 39px;
    border-radius: 3px;
    color: #fff;
}

.portlet .portlet-heading .portlet-title {
    float: left;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 0;
    margin-top: 0;
}

.portlet .portlet-heading .portlet-widgets {
    position: relative;
    text-align: right;
    float: right;
    padding-left: 15px;
    display: inline-block;
    font-size: 15px;
    line-height: 0px;
}

.portlet .portlet-body {
    background: #fff;
    padding: 15px;
    -webkit-border-bottom-right-radius: 5px;
    -webkit-border-bottom-left-radius: 5px;
    -moz-border-radius-bottomright: 5px;
    -moz-border-radius-bottomleft: 5px;
    border-bottom-right-radius: 5px;
    border-bottom-left-radius: 5px;
}

.portlet .portlet-heading .portlet-widgets .divider {
    margin: 0 5px;
}

.portlet .portlet-heading.bg-purple a, .portlet .portlet-heading.bg-info a, .portlet .portlet-heading.bg-success a, .portlet .portlet-heading.bg-primary a, .portlet .portlet-heading.bg-danger a, .portlet .portlet-heading.bg-warning a, .portlet .portlet-heading.bg-inverse a,.portlet .portlet-heading.bg-pink a {
    color: #fff;
}

.portlet .portlet-heading a {
    color: #999;
}

.panel-disabled {
    position: absolute;
    left: 15px;
    right: 15px;
    top: 0;
    bottom: 15px;
    background: rgba(243,242,241,0.5);
}