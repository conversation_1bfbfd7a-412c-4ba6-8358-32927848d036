build:
    environment:
        mysql: false
        postgresql: false
        redis: false
        rabbitmq: false
        php:
            version: 5.6
tools:
    php_sim: true
    php_pdepend: true
    php_analyzer: true
    php_changetracking: true
    php_code_sniffer:
        config:
            standard: "PSR2"
    php_mess_detector: true
checks:
    php:
        code_rating: true
        duplication: true
        argument_type_checks: true
        assignment_of_null_return: true
        avoid_conflicting_incrementers: true
        avoid_useless_overridden_methods: true
        catch_class_exists: true
        closure_use_modifiable: true
        closure_use_not_conflicting: true
        deprecated_code_usage: true
        method_calls_on_non_object: true
        missing_arguments: true
        no_duplicate_arguments: true
        no_non_implemented_abstract_methods: true
        no_property_on_interface: true
        parameter_non_unique: true
        precedence_in_conditions: true
        precedence_mistakes: true
        require_php_tag_first: true
        security_vulnerabilities: true
        sql_injection_vulnerabilities: true
        too_many_arguments: true
        unreachable_code: true
        unused_methods: true
        unused_parameters: true
        unused_properties: true
        unused_variables: true
        use_statement_alias_conflict: true
        useless_calls: true
        variable_existence: true
        verify_access_scope_valid: true
        verify_argument_usable_as_reference: true
        verify_property_names: true

filter:
    excluded_paths:
        - test/*
