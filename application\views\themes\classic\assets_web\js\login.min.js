"use strict";function logincustomer(){var email=$("#user_email").val(),pass=$("#u_pass").val(),errormessage="";if(""==email)return errormessage=errormessage+"<span>"+lang.please_enter_your_email+"</span>",alert(lang.please_enter_your_email),!1;if(""==pass)return errormessage=errormessage+"<span>"+lang.please_enter_your_email+"</span>",alert(lang.please_enter_your_email),!1;var dataString="email="+email+"&pass1="+pass+"&csrf_test_name="+basicinfo.csrftokeng;$.ajax({type:"POST",url:basicinfo.baseurl+"hungry/userlogin",data:dataString,success:function(data){"404"==data?alert(lang.failed_login_msg):window.location.href=basicinfo.baseurl+"menu"}})}function lostpassword(){var email=$("#user_email2").val(),errormessage="";if(""==email)return errormessage=errormessage+"<span>"+lang.enter_your_phone_or_email+"</span>",alert(lang.please_enter_your_email),!1;var dataString="email="+email+"&csrf_test_name="+basicinfo.csrftokeng;$.ajax({type:"POST",url:basicinfo.baseurl+"hungry/passwordrecovery",data:dataString,success:function(data){"404"==data?alert(lang.email_not_registered_msg):(alert(lang.have_been_sent_email+" "+email+" "+lang.check_your_new_password),window.location.href=basicinfo.baseurl+"checkout")}})}