{"name": "@fancyapps/fancybox", "description": "Touch enabled, responsive and fully customizable jQuery lightbox script", "version": "3.2.10", "homepage": "http://fancyapps.com/fancybox/", "main": "./dist/jquery.fancybox.js", "author": "fancyApps", "license": "GPL-3.0", "repository": {"type": "git", "url": "git+https://github.com/fancyapps/fancybox.git"}, "peerDependencies": {"jquery": ">=1.9.0"}, "devDependencies": {"del": "^2.2.2", "gulp": "^3.9.1", "gulp-autoprefixer": "^3.1.1", "gulp-concat": "^2.6.1", "gulp-cssnano": "^2.1.2", "gulp-header": "^1.8.8", "gulp-jshint": "^2.0.4", "gulp-livereload": "^3.8.1", "gulp-notify": "^2.2.0", "gulp-rename": "^1.2.2", "gulp-replace": "^0.5.4", "gulp-sass": "^3.0.0", "gulp-uglify": "^2.0.0", "gulp-util": "^3.0.8", "jshint": "^2.9.4"}, "keywords": ["touch", "responsive", "lightbox", "fancybox", "gallery", "j<PERSON><PERSON><PERSON>", "plugin"], "bugs": {"url": "https://github.com/fancyapps/fancybox/issues"}, "directories": {"doc": "docs"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}}