<?php
/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace SquareConnect\Model;

use \ArrayAccess;
/**
 * V1ModifierOption Class Doc Comment
 *
 * @category Class
 * @package  SquareConnect
 * <AUTHOR> Inc.
 * @license  http://www.apache.org/licenses/LICENSE-2.0 Apache License v2
 * @link     https://squareup.com/developers
 */
class V1ModifierOption implements ArrayAccess
{
    /**
      * Array of property to type mappings. Used for (de)serialization 
      * @var string[]
      */
    static $swaggerTypes = array(
        'id' => 'string',
        'name' => 'string',
        'price_money' => '\SquareConnect\Model\V1Money',
        'on_by_default' => 'bool',
        'ordinal' => 'int',
        'modifier_list_id' => 'string'
    );
  
    /** 
      * Array of attributes where the key is the local name, and the value is the original name
      * @var string[] 
      */
    static $attributeMap = array(
        'id' => 'id',
        'name' => 'name',
        'price_money' => 'price_money',
        'on_by_default' => 'on_by_default',
        'ordinal' => 'ordinal',
        'modifier_list_id' => 'modifier_list_id'
    );
  
    /**
      * Array of attributes to setter functions (for deserialization of responses)
      * @var string[]
      */
    static $setters = array(
        'id' => 'setId',
        'name' => 'setName',
        'price_money' => 'setPriceMoney',
        'on_by_default' => 'setOnByDefault',
        'ordinal' => 'setOrdinal',
        'modifier_list_id' => 'setModifierListId'
    );
  
    /**
      * Array of attributes to getter functions (for serialization of requests)
      * @var string[]
      */
    static $getters = array(
        'id' => 'getId',
        'name' => 'getName',
        'price_money' => 'getPriceMoney',
        'on_by_default' => 'getOnByDefault',
        'ordinal' => 'getOrdinal',
        'modifier_list_id' => 'getModifierListId'
    );
  
    /**
      * $id The modifier option's unique ID.
      * @var string
      */
    protected $id;
    /**
      * $name The modifier option's name.
      * @var string
      */
    protected $name;
    /**
      * $price_money The modifier option's price.
      * @var \SquareConnect\Model\V1Money
      */
    protected $price_money;
    /**
      * $on_by_default If true, the modifier option is the default option in a modifier list for which selection_type is SINGLE.
      * @var bool
      */
    protected $on_by_default;
    /**
      * $ordinal Indicates the modifier option's list position when displayed in Square Register and the merchant dashboard. If more than one modifier option in the same modifier list has the same ordinal value, those options are displayed in alphabetical order.
      * @var int
      */
    protected $ordinal;
    /**
      * $modifier_list_id The ID of the modifier list the option belongs to.
      * @var string
      */
    protected $modifier_list_id;

    /**
     * Constructor
     * @param mixed[] $data Associated array of property value initializing the model
     */
    public function __construct(array $data = null)
    {
        if ($data != null) {
            if (isset($data["id"])) {
              $this->id = $data["id"];
            } else {
              $this->id = null;
            }
            if (isset($data["name"])) {
              $this->name = $data["name"];
            } else {
              $this->name = null;
            }
            if (isset($data["price_money"])) {
              $this->price_money = $data["price_money"];
            } else {
              $this->price_money = null;
            }
            if (isset($data["on_by_default"])) {
              $this->on_by_default = $data["on_by_default"];
            } else {
              $this->on_by_default = null;
            }
            if (isset($data["ordinal"])) {
              $this->ordinal = $data["ordinal"];
            } else {
              $this->ordinal = null;
            }
            if (isset($data["modifier_list_id"])) {
              $this->modifier_list_id = $data["modifier_list_id"];
            } else {
              $this->modifier_list_id = null;
            }
        }
    }
    /**
     * Gets id
     * @return string
     */
    public function getId()
    {
        return $this->id;
    }
  
    /**
     * Sets id
     * @param string $id The modifier option's unique ID.
     * @return $this
     */
    public function setId($id)
    {
        $this->id = $id;
        return $this;
    }
    /**
     * Gets name
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }
  
    /**
     * Sets name
     * @param string $name The modifier option's name.
     * @return $this
     */
    public function setName($name)
    {
        $this->name = $name;
        return $this;
    }
    /**
     * Gets price_money
     * @return \SquareConnect\Model\V1Money
     */
    public function getPriceMoney()
    {
        return $this->price_money;
    }
  
    /**
     * Sets price_money
     * @param \SquareConnect\Model\V1Money $price_money The modifier option's price.
     * @return $this
     */
    public function setPriceMoney($price_money)
    {
        $this->price_money = $price_money;
        return $this;
    }
    /**
     * Gets on_by_default
     * @return bool
     */
    public function getOnByDefault()
    {
        return $this->on_by_default;
    }
  
    /**
     * Sets on_by_default
     * @param bool $on_by_default If true, the modifier option is the default option in a modifier list for which selection_type is SINGLE.
     * @return $this
     */
    public function setOnByDefault($on_by_default)
    {
        $this->on_by_default = $on_by_default;
        return $this;
    }
    /**
     * Gets ordinal
     * @return int
     */
    public function getOrdinal()
    {
        return $this->ordinal;
    }
  
    /**
     * Sets ordinal
     * @param int $ordinal Indicates the modifier option's list position when displayed in Square Register and the merchant dashboard. If more than one modifier option in the same modifier list has the same ordinal value, those options are displayed in alphabetical order.
     * @return $this
     */
    public function setOrdinal($ordinal)
    {
        $this->ordinal = $ordinal;
        return $this;
    }
    /**
     * Gets modifier_list_id
     * @return string
     */
    public function getModifierListId()
    {
        return $this->modifier_list_id;
    }
  
    /**
     * Sets modifier_list_id
     * @param string $modifier_list_id The ID of the modifier list the option belongs to.
     * @return $this
     */
    public function setModifierListId($modifier_list_id)
    {
        $this->modifier_list_id = $modifier_list_id;
        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     * @param  integer $offset Offset 
     * @return boolean
     */
    public function offsetExists($offset)
    {
        return isset($this->$offset);
    }
  
    /**
     * Gets offset.
     * @param  integer $offset Offset 
     * @return mixed 
     */
    public function offsetGet($offset)
    {
        return $this->$offset;
    }
  
    /**
     * Sets value based on offset.
     * @param  integer $offset Offset 
     * @param  mixed   $value  Value to be set
     * @return void
     */
    public function offsetSet($offset, $value)
    {
        $this->$offset = $value;
    }
  
    /**
     * Unsets offset.
     * @param  integer $offset Offset 
     * @return void
     */
    public function offsetUnset($offset)
    {
        unset($this->$offset);
    }
  
    /**
     * Gets the string presentation of the object
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) {
            return json_encode(\SquareConnect\ObjectSerializer::sanitizeForSerialization($this), JSON_PRETTY_PRINT);
        } else {
            return json_encode(\SquareConnect\ObjectSerializer::sanitizeForSerialization($this));
        }
    }
}
