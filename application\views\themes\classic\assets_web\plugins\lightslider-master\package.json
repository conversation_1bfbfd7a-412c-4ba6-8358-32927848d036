{"name": "lightslider", "description": "jQuery lightSlider is a lightweight responsive content slider - carousel with animated thumbnails navigation", "version": "1.1.6", "keywords": ["slider", "contentslider", "textslider", "slideshow", "gallery", "responsive", "carousel", "animation", "j<PERSON><PERSON><PERSON>", "video", "image", "CSS3", "touch", "swipe", "thumbnail"], "main": "dist/js/lightslider.js", "author": {"name": "Sachin N", "email": "<EMAIL>", "url": "https://github.com/sachinchoolur"}, "homepage": "https://github.com/sachinchoolur/lightslider", "repository": {"type": "git", "url": "https://github.com/sachinchoolur/lightslider.git"}, "bugs": {"url": "https://github.com/sachinchoolur/lightslider/issues"}, "license": {"type": "MIT", "url": "http://opensource.org/licenses/MIT"}, "dependencies": {"jquery": ">= 1.7.0"}, "scripts": {"test": "grunt"}, "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-concat": "^0.5.0", "grunt-contrib-connect": "^0.9.0", "grunt-contrib-copy": "^0.8.0", "grunt-contrib-cssmin": "^0.12.1", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-qunit": "^0.5.1", "grunt-contrib-sass": "^0.9.2", "grunt-contrib-uglify": "^0.7.0", "grunt-contrib-watch": "^0.6.1", "jshint-stylish": "^1.0.0", "load-grunt-tasks": "^2.0.0", "time-grunt": "^1.0.0"}}