# CatalogV1Id

## Properties
Name | Getter | Setter | Type | Description | Notes
------------ | ------------- | ------------- | ------------- | ------------- | -------------
**catalog_v1_id** | getCatalogV1Id() | setCatalogV1Id($value) | **string** | The ID for an object in Connect V1, if different from its Connect V2 ID. | [optional] 
**location_id** | getLocationId() | setLocationId($value) | **string** | The ID of the [location](#type-location) this Connect V1 ID is associated with. | [optional] 

Note: All properties are protected and only accessed via getters and setters.

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)

