# V1Page

## Properties
Name | Getter | Setter | Type | Description | Notes
------------ | ------------- | ------------- | ------------- | ------------- | -------------
**id** | getId() | setId($value) | **string** | The page&#39;s unique identifier. | [optional] 
**name** | getName() | setName($value) | **string** | The page&#39;s name, if any. | [optional] 
**page_index** | getPageIndex() | setPageIndex($value) | **int** | The page&#39;s position in the merchant&#39;s list of pages. Always an integer between 0 and 6, inclusive. | [optional] 
**cells** | getCells() | setCells($value) | [**\SquareConnect\Model\V1PageCell[]**](V1PageCell.md) | The cells included on the page. | [optional] 

Note: All properties are protected and only accessed via getters and setters.

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)

