function itemlist(){var myurl=$("#url").val(),dataString="id="+$("#catid").val()+"&csrf_test_name="+$("#csrfhashresarvation").val();$.ajax({type:"POST",url:myurl,data:dataString,success:function(data){$(".iteminfo").html(data),$("#items").modal("show")}})}function addfoodtocart(pid,id){var geturl=$("#carturl").val(),itemname=$("#itemname_"+id).val(),sizeid=$("#sizeid_"+id).val(),varientname=$("#size_"+id).val(),qty=$("#itemqty_"+id).val(),price=$("#itemprice_"+id).val(),catid=$("#catid").val(),updateid=$("#updateid").val(),csrf=$("#csrfhashresarvation").val(),myurl=geturl;if(""==updateid)var dataString="pid="+pid+"&itemname="+itemname+"&varientname="+varientname+"&qty="+qty+"&price="+price+"&catid="+catid+"&sizeid="+sizeid+"&csrf_test_name="+csrf;else dataString="pid="+pid+"&itemname="+itemname+"&varientname="+varientname+"&qty="+qty+"&price="+price+"&catid="+catid+"&sizeid="+sizeid+"&orderid="+(updateid=$("#uidupdateid").val())+"&csrf_test_name="+csrf;$.ajax({type:"POST",url:myurl,data:dataString,success:function(data){if(""==updateid){$("#cartlist").html(data);var tax=$("#tvat").val(),discount=$("#tdiscount").val(),tgtotal=$("#tgtotal").val();$("#calvat").text(tax),$("#invoice_discount").val(discount);var sc=$("#sc").val();$("#service_charge").val(sc),1==basicinfo.isvatinclusive?$("#caltotal").text(tgtotal-tax):$("#caltotal").text(tgtotal),$("#grandtotal").val(tgtotal),$("#orggrandTotal").val(tgtotal)}else{$("#updatetlist").html(data);tax=$("#tvat").val(),discount=$("#tdiscount").val(),tgtotal=$("#tgtotal").val();$("#calvat").text(tax),$("#invoice_discount").val(discount);sc=$("#sc").val();$("#service_charge").val(sc),1==basicinfo.isvatinclusive?$("#caltotal").text(tgtotal-tax):$("#caltotal").text(tgtotal),$("#grandtotal").val(tgtotal),$("#orggrandTotal").val(tgtotal)}}})}function updatecart(id,qty,status){if("del"==status&&0==qty)return!1;var geturl=$("#cartupdateturl").val(),dataString="CartID="+id+"&qty="+qty+"&Udstatus="+status+"&csrf_test_name="+$("#csrfhashresarvation").val();$.ajax({type:"POST",url:geturl,data:dataString,success:function(data){$("#cartlist").html(data);var tax=$("#tvat").val(),discount=$("#tdiscount").val(),tgtotal=$("#tgtotal").val();$("#calvat").text(tax),$("#invoice_discount").val(discount);var sc=$("#sc").val();$("#service_charge").val(sc),1==basicinfo.isvatinclusive?$("#caltotal").text(tgtotal-tax):$("#caltotal").text(tgtotal),$("#grandtotal").val(tgtotal),$("#orggrandTotal").val(tgtotal)}})}function posupdatecart(id,pid,vid,qty,status){if("del"==status&&0==qty)return!1;if("add"==status&&1==$("#production_setting").val()&&0==checkproduction(pid,vid,qty+1))return!1;var csrf=$("#csrfhashresarvation").val(),mysound=baseurl+"assets/";new Audio(mysound+"beep-08b.mp3").play();var geturl=$("#cartupdateturl").val(),dataString="CartID="+id+"&qty="+qty+"&Udstatus="+status+"&csrf_test_name="+csrf;$.ajax({type:"POST",url:geturl,data:dataString,success:function(data){$("#addfoodlist").html(data);$("#grtotal").val();var totalitem=$("#totalitem").val();$("#item-number").text(totalitem),$("#getitemp").val(totalitem);var tax=$("#tvat").val(),discount=$("#tdiscount").val(),tgtotal=$("#tgtotal").val();$("#calvat").text(tax),$("#vat").val(tax),$("#invoice_discount").val(discount);var sc=$("#sc").val();$("#service_charge").val(sc),1==basicinfo.isvatinclusive?$("#caltotal").text(tgtotal-tax):$("#caltotal").text(tgtotal),$("#grandtotal").val(tgtotal),$("#orggrandTotal").val(tgtotal),$("#orginattotal").val(tgtotal)}})}function removecart(rid){var geturl=$("#removeurl").val(),dataString="rowid="+rid+"&csrf_test_name="+$("#csrfhashresarvation").val(),mysound=baseurl+"assets/";new Audio(mysound+"beep-08b.mp3").play(),$.ajax({type:"POST",url:geturl,data:dataString,success:function(data){$("#addfoodlist").html(data);$("#grtotal").val();var totalitem=$("#totalitem").val();$("#item-number").text(totalitem),$("#getitemp").val(totalitem);var tax=$("#tvat").val(),discount=$("#tdiscount").val(),tgtotal=$("#tgtotal").val();$("#calvat").text(tax),$("#vat").val(tax),$("#invoice_discount").val(discount);var sc=$("#sc").val();$("#service_charge").val(sc),1==basicinfo.isvatinclusive?$("#caltotal").text(tgtotal-tax):$("#caltotal").text(tgtotal),$("#grandtotal").val(tgtotal),$("#orggrandTotal").val(tgtotal)}})}function addonsitem(id,sid){var myurl=$("#addonsurl").val()+"/"+id,dataString="pid="+id+"&sid="+sid+"&csrf_test_name="+$("#csrfhashresarvation").val();$.ajax({type:"POST",url:myurl,data:dataString,success:function(data){$(".addonsinfo").html(data),$("#edit").modal("show")}})}function posaddonsitem(id,sid){var myurl=$("#addonsurl").val()+"/"+id,dataString="pid="+id+"&sid="+sid+"&csrf_test_name="+$("#csrfhashresarvation").val();$.ajax({type:"POST",url:myurl,data:dataString,success:function(data){$(".addonsinfo").html(data),$("#edit").modal("show")}})}function addonsfoodtocart(pid,id){var addons=[],adonsqty=[],allprice=0,adonsprice=[],adonsname=[];$('input[name="addons"]:checked').each((function(){var adnsid=$(this).val(),adsqty=$("#addonqty_"+adnsid).val();adonsqty.push(adsqty),addons.push($(this).val()),allprice+=parseFloat($(this).attr("role"))*parseInt(adsqty),adonsprice.push($(this).attr("role")),adonsname.push($(this).attr("title"))}));var catid=$("#catid").val(),csrf=$("#csrfhashresarvation").val(),itemname=$("#itemname_"+id).val(),sizeid=$("#sizeid_"+id).val(),varientname=$("#size_"+id).val(),qty=$("#itemqty_"+id).val(),price=$("#itemprice_"+id).val();if(""==(updateid=$("#updateid").val()))var myurl=$("#carturl").val(),dataString="pid="+pid+"&itemname="+itemname+"&varientname="+varientname+"&qty="+qty+"&price="+price+"&catid="+catid+"&sizeid="+sizeid+"&addonsid="+addons+"&allprice="+allprice+"&adonsunitprice="+adonsprice+"&adonsqty="+adonsqty+"&adonsname="+adonsname+"&csrf_test_name="+csrf;else{var updateid=$("#uidupdateid").val();myurl=$("#updatecarturl").val(),dataString="pid="+pid+"&itemname="+itemname+"&varientname="+varientname+"&qty="+qty+"&price="+price+"&catid="+catid+"&sizeid="+sizeid+"&addonsid="+addons+"&allprice="+allprice+"&adonsunitprice="+adonsprice+"&adonsqty="+adonsqty+"&adonsname="+adonsname+"&orderid="+updateid+"&csrf_test_name="+csrf}$.ajax({type:"POST",url:myurl,data:dataString,success:function(data){if(""==updateid){$("#cartlist").html(data),$("#edit").modal("hide");var tax=$("#tvat").val(),discount=$("#tdiscount").val(),tgtotal=$("#tgtotal").val();$("#calvat").text(tax),$("#invoice_discount").val(discount);var sc=$("#sc").val();$("#service_charge").val(sc),1==basicinfo.isvatinclusive?$("#caltotal").text(tgtotal-tax):$("#caltotal").text(tgtotal),$("#grandtotal").val(tgtotal),$("#orggrandTotal").val(tgtotal)}else{$("#updatetlist").html(data),$("#edit").modal("hide");tax=$("#tvat").val(),discount=$("#tdiscount").val(),sc=$("#sc").val();$("#service_charge").val(sc);tgtotal=$("#tgtotal").val();$("#calvat").text(tax),$("#invoice_discount").val(discount),1==basicinfo.isvatinclusive?$("#caltotal").text(tgtotal-tax):$("#caltotal").text(tgtotal),$("#grandtotal").val(tgtotal),$("#orggrandTotal").val(tgtotal),$("#orginattotal").val(tgtotal)}}})}function posaddonsfoodtocart(pid,id,more=null){var addons=[],adonsqty=[],allprice=0,adonsprice=[],adonsname=[],csrf=$("#csrfhashresarvation").val();$('input[name="addons"]:checked').each((function(){var adnsid=$(this).val(),adsqty=$("#addonqty_"+adnsid).val();adonsqty.push(adsqty),addons.push($(this).val()),allprice+=parseFloat($(this).attr("role"))*parseInt(adsqty),adonsprice.push($(this).attr("role")),adonsname.push($(this).attr("title"))}));var geturl=$("#carturl").val(),catid=$("#catid").val(),totalvarient=$("#totalvarient").val(),customqty=$("#customqty").val(),itemname=$("#itemname_"+id).val(),sizeid=$("#sizeid_"+id).val(),varientname=$("#size_"+id).val(),qty=$("#itemqty_"+id).val(),price=$("#itemprice_"+id).val(),isgroup=$("#isgroup").val(),updateid=$("#uidupdateid").val(),myurl=geturl,mysound=baseurl+"assets/";if(new Audio(mysound+"beep-08b.mp3").play(),void 0===updateid){if(1==$("#production_setting").val()){if(1==$("#productionsetting-"+pid+"-"+sizeid).length)var checkqty=parseInt($("#productionsetting-"+pid+"-"+sizeid).text())+qty;else checkqty=qty;if(0==checkproduction(pid,sizeid,checkqty))return!1}myurl=geturl=$("#carturl").val();var dataString="pid="+pid+"&itemname="+itemname+"&varientname="+varientname+"&qty="+qty+"&price="+price+"&catid="+catid+"&sizeid="+sizeid+"&addonsid="+addons+"&allprice="+allprice+"&adonsunitprice="+adonsprice+"&adonsqty="+adonsqty+"&adonsname="+adonsname+"&isgroup="+isgroup+"&totalvarient="+totalvarient+"&customqty="+customqty+"&csrf_test_name="+csrf}else{if(1==$("#production_setting").val()){if(1==$("#productionsetting-update-"+pid+"-"+sizeid).length)checkqty=parseInt($("#productionsetting-update-"+pid+"-"+sizeid).text())+qty;else checkqty=qty;if(0==checkproduction(pid,sizeid,checkqty))return!1}myurl=geturl=$("#updatecarturl").val(),dataString="pid="+pid+"&itemname="+itemname+"&varientname="+varientname+"&qty="+qty+"&price="+price+"&catid="+catid+"&sizeid="+sizeid+"&addonsid="+addons+"&allprice="+allprice+"&adonsunitprice="+adonsprice+"&adonsqty="+adonsqty+"&adonsname="+adonsname+"&orderid="+updateid+"&isgroup="+isgroup+"&totalvarient="+totalvarient+"&customqty="+customqty+"&csrf_test_name="+csrf}$.ajax({type:"POST",url:myurl,data:dataString,success:function(data){void 0===updateid?$("#addfoodlist").html(data):$("#updatefoodlist").html(data);$("#grtotal").val();var totalitem=$("#totalitem").val();$("#item-number").text(totalitem);var tax=$("#tvat").val(),discount=$("#tdiscount").val(),tgtotal=$("#tgtotal").val();$("#calvat").text(tax),$("#vat").val(tax),$("#invoice_discount").val(discount);var sc=$("#sc").val();$("#service_charge").val(sc),1==basicinfo.isvatinclusive?$("#caltotal").text(tgtotal-tax):$("#caltotal").text(tgtotal),$("#grandtotal").val(tgtotal),$("#orggrandTotal").val(tgtotal),$("#orginattotal").val(tgtotal),1!=more&&$("#edit").modal("hide")}})}function deletecart(id,orderid,pid,vid,qty){var geturl=$("#delurl").val(),dataString="mid="+id+"&orderid="+orderid+"&pid="+pid+"&vid="+vid+"&qty="+qty+"&csrf_test_name="+$("#csrfhashresarvation").val();$.ajax({type:"POST",url:geturl,data:dataString,success:function(data){$("#updatefoodlist").html(data),$("#uvchange").val(1),$(".maindashboard").addClass("disabled"),$("#fhome").addClass("disabled"),$("#kitchenorder").addClass("disabled"),$("#todayqrorder").addClass("disabled"),$("#todayonlieorder").addClass("disabled"),$("#todayorder").addClass("disabled"),$("#ongoingorder").addClass("disabled")}})}function expand(id){var classes=$("#expandcol_"+id).attr("class").split(" ")[1];$("#expandcol_"+id).hasClass("hasaddons")?$("."+classes).removeClass("hasaddons"):$("."+classes).addClass("hasaddons")}function calculatetotal(){var subtotal,inv_dis=0,ser_chg=0,vat=0;$("#orggrandTotal").val(),subtotal=$("#subtotal").val(),""==(inv_dis=$("#invoice_discount").val())&&(inv_dis=0),""==(ser_chg=$("#service_charge").val())&&(ser_chg=0),""==(vat=$("#vat").val())&&(vat=0),distype=$("#distype").val(),1==distype&&(inv_dis=parseFloat(subtotal)*parseFloat(inv_dis)/100),sdtype=$("#sdtype").val(),1==sdtype&&(ser_chg=parseFloat(subtotal)*parseFloat(ser_chg)/100);var totalamount=parseFloat(subtotal)+parseFloat(vat),sum=(sum=parseFloat(totalamount)+parseFloat(ser_chg)-parseFloat(inv_dis)).toFixed(2);if($("#grandtotal").val(sum),$("#orginattotal").val(sum),$("#caltotal").text(sum),1==basicinfo.isvatinclusive){$("#caltotal").text(sum-vat)}else{$("#caltotal").text(sum)}}function sumcalculation(id=null){var inv_dis=0,ser_chg=0,subtotal=0,vat=0;""!=id?($("#orginattotal_update").val(),inv_dis=$("#invoice_discount_update").val()):($("#orginattotal").val(),inv_dis=$("#discount").val()),""==inv_dis&&(inv_dis=0),""==(ser_chg=""!=id?$("#service_charge_update").val():$("#scharge").val())&&(ser_chg=0),""==(subtotal=""!=id?$("#subtotal_update").val():$("#subtotal").val())&&(subtotal=0),""==(vat=""!=id?$("#vat_update").val():$("#vat").val())&&(vat=0),distype=""!=id?$("#distype_update").val():$("#distype").val(),1==distype&&(inv_dis=parseFloat(subtotal)*parseFloat(inv_dis)/100),sdtype=""!=id?$("#sdtype_update").val():$("#sdtype").val(),1==sdtype&&(ser_chg=parseFloat(subtotal)*parseFloat(ser_chg)/100);var totalamount=parseFloat(subtotal)+parseFloat(vat),sum=(sum=parseFloat(totalamount)+parseFloat(ser_chg)-parseFloat(inv_dis)).toFixed(2);if(""!=id)if($("#grandtotal_update").val(sum),$("#orginattotal_update").val(sum),$("#gtotal_update").text(sum),1==basicinfo.isvatinclusive){$("#gtotal_update").text(sum-vat)}else{$("#gtotal_update").text(sum)}else if($("#grandtotal").val(sum),$("#orginattotal").val(sum),$("#gtotal").text(sum),1==basicinfo.isvatinclusive){$("#gtotal").text(sum-vat)}else{$("#gtotal").text(sum)}}function getAjaxModal(url,callback=!1,ajaxclass="#modal-ajaxview",modalclass="#payprint_marge",data="",method="get"){var fulldata=data+"&csrf_test_name="+$("#csrfhashresarvation").val();$.ajax({url:url,type:method,data:fulldata,beforeSend:function(xhr){},success:function(result){$(modalclass).modal("show"),callback?callback(result):($(ajaxclass).html(result),$("#add_new_payment").empty())}})}function checkproduction(foodid,vid,servingqty){var myurl=$("#production_url").val(),dataString="foodid="+foodid+"&vid="+vid+"&qty="+servingqty+"&csrf_test_name="+$("#csrfhashresarvation").val(),check=!0;return $.ajax({type:"POST",url:myurl,async:!1,global:!1,data:dataString,success:function(data){1!=data&&(alert(data),check=!1)}}),check}$(document).on("change","#varientinfo",(function(){var id=$("#varientinfo").val(),name=$("#varientinfo option:selected").data("title"),price=$("#varientinfo option:selected").data("price");$("#sizeid_1").val(id),$("#size_1").val(name),$("#itemprice_1").val(price),$("#vprice").text(price)})),$("body").on("click",".update_select_product",(function(e){e.preventDefault();var panel=$(this),pid=panel.find(".panel-body input[name=update_select_product_id]").val(),totalvarient=panel.find(".panel-body input[name=update_select_totalvarient]").val(),customqty=panel.find(".panel-body input[name=update_select_iscustomeqty]").val(),sizeid=panel.find(".panel-body input[name=update_select_product_size]").val(),isgroup=panel.find(".panel-body input[name=update_select_product_isgroup]").val(),catid=panel.find(".panel-body input[name=update_select_product_cat]").val(),itemname=panel.find(".panel-body input[name=update_select_product_name]").val(),varientname=panel.find(".panel-body input[name=update_select_varient_name]").val(),qty=1,price=panel.find(".panel-body input[name=update_select_product_price]").val(),hasaddons=panel.find(".panel-body input[name=update_select_addons]").val(),existqty=$("#select_qty_"+pid+"_"+sizeid).val(),csrf=$("#csrfhashresarvation").val();if(void 0===existqty)existqty=0;else existqty=$("#select_qty_"+pid+"_"+sizeid).val();qty=parseInt(existqty)+parseInt(qty);var updateid=$("#saleinvoice").val();if(0==hasaddons&&1==totalvarient&&0==customqty){if(1==$("#production_setting").val()){if(1==$("#productionsetting-update-"+pid+"-"+sizeid).length)var checkqty=parseInt($("#productionsetting-update-"+pid+"-"+sizeid).text())+qty;else checkqty=qty;if(0==checkproduction(pid,sizeid,checkqty))return!1}var mysound=baseurl+"assets/";new Audio(mysound+"beep-08b.mp3").play();var myurl=$("#updatecarturl").val(),dataString="pid="+pid+"&itemname="+itemname+"&varientname="+varientname+"&qty="+qty+"&price="+price+"&catid="+catid+"&sizeid="+sizeid+"&isgroup="+isgroup+"&orderid="+updateid+"&totalvarient="+totalvarient+"&customqty="+customqty+"&csrf_test_name="+csrf;$.ajax({type:"POST",url:myurl,data:dataString,success:function(data){$("#updatefoodlist").html(data);$("#grtotal").val();var totalitem=$("#totalitem").val();$("#item-number").text(totalitem),$("#getitemp").val(totalitem);var tax=$("#tvat").val(),discount=$("#tdiscount").val(),tgtotal=$("#tgtotal").val();$("#calvat").text(tax),$("#invoice_discount").val(discount);var sc=$("#sc").val();$("#service_charge").val(sc),1==basicinfo.isvatinclusive?$("#caltotal").text(tgtotal-tax):$("#caltotal").text(tgtotal),$("#grandtotal").val(tgtotal),$("#orggrandTotal").val(tgtotal),$("#orginattotal").val(tgtotal),$("#uvchange").val(1),$(".maindashboard").addClass("disabled"),$("#fhome").addClass("disabled"),$("#kitchenorder").addClass("disabled"),$("#todayqrorder").addClass("disabled"),$("#todayonlieorder").addClass("disabled"),$("#todayorder").addClass("disabled"),$("#ongoingorder").addClass("disabled")}})}else{var geturl=$("#addonexsurl").val();myurl=geturl+"/"+pid,dataString="pid="+pid+"&sid="+sizeid+"&id="+catid+"&totalvarient="+totalvarient+"&customqty="+customqty+"&csrf_test_name="+csrf;$.ajax({type:"POST",url:geturl,data:dataString,success:function(data){$(".addonsinfo").html(data),$("#edit").modal("show");var tax=$("#tvat").val(),discount=$("#tdiscount").val(),tgtotal=$("#tgtotal").val();$("#vat").val(tax),$("#calvat").text(tax),$("#invoice_discount").val(discount),1==basicinfo.isvatinclusive?$("#caltotal").text(tgtotal-tax):$("#caltotal").text(tgtotal),$("#grandtotal").val(tgtotal),$("#orggrandTotal").val(tgtotal),$("#orginattotal").val(tgtotal),$("#uvchange").val(1),$(".maindashboard").addClass("disabled"),$("#fhome").addClass("disabled"),$("#kitchenorder").addClass("disabled"),$("#todayqrorder").addClass("disabled"),$("#todayonlieorder").addClass("disabled"),$("#todayorder").addClass("disabled"),$("#ongoingorder").addClass("disabled")}})}}));