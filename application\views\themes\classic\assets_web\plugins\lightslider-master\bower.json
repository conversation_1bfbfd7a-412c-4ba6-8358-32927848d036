{"name": "lightslider", "version": "1.1.6", "homepage": "https://github.com/sachinchoolur/lightslider", "authors": ["Sachin N <<EMAIL>>"], "description": "jQuery lightSlider is a lightweight responsive content slider - carousel with animated thumbnails navigation", "main": ["dist/js/lightslider.min.js", "dist/css/lightslider.min.css", "dist/img/controls.png"], "keywords": ["slider", "contentslider", "textslider", "slideshow", "gallery", "responsive", "carousel", "animation", "j<PERSON><PERSON><PERSON>", "video", "image", "CSS3", "touch", "swipe", "thumbnail"], "license": "MLT", "ignore": ["demo", "test", "**/.*", "contributing.md", "Gruntfile.js", "README.md"], "devDependencies": {"qunit": "~1.12.0", "jquery": "~1.7.0"}}