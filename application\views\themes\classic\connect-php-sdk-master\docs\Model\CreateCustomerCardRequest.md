# CreateCustomerCardRequest

## Properties
Name | Getter | Setter | Type | Description | Notes
------------ | ------------- | ------------- | ------------- | ------------- | -------------
**card_nonce** | getCardNonce() | setCardNonce($value) | **string** | A card nonce representing the credit card to link to the customer.  Card nonces are generated by the &#x60;SqPaymentForm&#x60; that buyers enter their card information into. See [Embedding the payment form](/payments/sqpaymentform/overview) for more information.  __Note:__ Card nonces generated by digital wallets (e.g., Apple Pay) cannot be used to create a customer card. | 
**billing_address** | getBillingAddress() | setBillingAddress($value) | [**\SquareConnect\Model\Address**](Address.md) | Address information for the card on file. Only the &#x60;postal_code&#x60; field is required for payments in the US and Canada. | [optional] 
**cardholder_name** | getCardholderName() | setCardholderName($value) | **string** | The cardholder&#39;s name. | [optional] 

Note: All properties are protected and only accessed via getters and setters.

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)

