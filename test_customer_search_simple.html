<!DOCTYPE html>
<html>
<head>
    <title>Customer Search Test</title>
    <script src="http://localhost/viera_kasir/assets/js/jquery-1.12.4.min.js"></script>
</head>
<body>
    <h2>Customer Search Test</h2>
    <p>This page will test the customer search functionality directly.</p>
    
    <div>
        <label>Search Term:</label>
        <input type="text" id="search_term" value="upin" />
        <button onclick="testSearch()">Test Search</button>
    </div>
    
    <div id="results" style="margin-top: 20px;"></div>
    
    <script>
    function testSearch() {
        var searchTerm = $('#search_term').val();
        $('#results').html('Testing search for: ' + searchTerm + '...');
        
        // First, get the customer page to obtain CSRF token
        $.get('http://localhost/viera_kasir/customermanage/customer/index', function(data) {
            // Extract CSRF token from the page
            var csrfMatch = data.match(/id="csrf_test_name"[^>]*value="([^"]+)"/);
            if (csrfMatch) {
                var csrfToken = csrfMatch[1];
                console.log('CSRF Token found:', csrfToken);
                
                // Now make the search request
                var postData = {
                    search_term: searchTerm,
                    csrf_test_name: csrfToken
                };
                
                $.ajax({
                    url: 'http://localhost/viera_kasir/customermanage/customer/search_customers',
                    type: 'POST',
                    data: postData,
                    dataType: 'json',
                    success: function(response) {
                        console.log('Search Success:', response);
                        $('#results').html('<h3>Success!</h3><pre>' + JSON.stringify(response, null, 2) + '</pre>');
                    },
                    error: function(xhr, status, error) {
                        console.log('Search Error:', xhr, status, error);
                        $('#results').html('<h3>Error!</h3><p>Status: ' + status + '</p><p>Error: ' + error + '</p><p>Response: ' + xhr.responseText + '</p>');
                    }
                });
            } else {
                $('#results').html('<h3>Error!</h3><p>Could not find CSRF token in customer page</p>');
            }
        }).fail(function() {
            $('#results').html('<h3>Error!</h3><p>Could not access customer page</p>');
        });
    }
    
    // Auto-run test on page load
    $(document).ready(function() {
        setTimeout(testSearch, 1000);
    });
    </script>
</body>
</html>
