<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class RawbtPrinter {
    public function generateTextReceipt($params = []) {
        $dataHeader = $params['dataHeader'] ?? [];
        $detailTransaksi = $params['detailTransaksi'] ?? [];
        $detailBayar = $params['detailBayar'] ?? [];

        $receipt = "           Cocodream\n\n";
        $receipt .= ($dataHeader['entitas_address'] ?? "Alamat Tidak Tersedia") . "\n";
        $receipt .= "Kota Pekanbaru, Riau 28289\n";
        $receipt .= "Telepon: +62 " . ($dataHeader['entitas_phone'] ?? "-") . "\n";
        $receipt .= "-------------------------------\n";
        $receipt .= "Invoice: " . ($dataHeader['no_invoice'] ?? "-") . "\n";
        $receipt .= "Waktu: " . ($dataHeader['transaction_date'] ?? date("Y-m-d H:i:s")) . "\n";
        $receipt .= "-------------------------------\n";

        foreach ($detailTransaksi as $item) {
            $receipt .= $item['nama_item'] . "\n";
            $receipt .= "  {$item['quantity']} {$item['nama_satuan']} x Rp. " . number_format($item['subtotal'], 0, ',', '.') . "\n";
        }

        $receipt .= "-------------------------------\n";
        $receipt .= "Total: Rp. " . number_format($detailBayar[0]['nominal_awal'] ?? 0, 0, ',', '.') . "\n";
        $receipt .= "Diskon: Rp. " . number_format($detailBayar[0]['diskon_tambahan'] ?? 0, 0, ',', '.') . "\n";
        $receipt .= "Tunai: Rp. " . number_format($detailBayar[0]['nominal_bayar'] ?? 0, 0, ',', '.') . "\n";
        $receipt .= "Kembali: Rp. " . number_format($detailBayar[0]['nominal_kembalian'] ?? 0, 0, ',', '.') . "\n";
        $receipt .= "-------------------------------\n";
        $receipt .= "Terima kasih atas kunjungan Anda\n";
        $receipt .= "Tiktok: cocodream_pku\n";
        $receipt .= "Instagram: almeiradeganjelly\n\n";

        return $receipt;
    }

    private $lineLength = 48; // Max characters per line for 80mm

    public function printExample() {
        $receipt = $this->centerText("PT VIERA ANUGERAH PERTAMA");
        $receipt .= str_repeat("-", $this->lineLength) . "\n";
        $receipt .= $this->alignText("Total", "Rp. 48.000");
        $receipt .= $this->alignText("Tunai", "Rp. 50.000");
        $receipt .= $this->alignText("Kembali", "Rp. 2.000");

        return $this->printReceipt($receipt);
    }

    public function printReceipt($receiptText) {
        if (empty($receiptText)) {
            return ['status' => 'error', 'message' => 'Receipt content is empty.'];
        }

        // Encode receipt content to avoid URI issues
        $encodedText = rawurlencode($receiptText);
        $script = "<script>
            try {
                window.location.href = 'intent:' + '$encodedText' + '#Intent;scheme=rawbt;package=ru.a402d.rawbtprinter;end;';
            } catch (error) {
                alert('Error printing: ' + error.message);
            }
        </script>";

        echo $script;
        return ['status' => 'success', 'message' => 'Print command sent.'];
    }

    private function alignText($label, $value) {
        $spaceCount = $this->lineLength - (strlen($label) + strlen($value));
        $spaces = str_repeat(" ", max($spaceCount, 1));
        return $label . $spaces . $value . "\n";
    }

    private function centerText($text) {
        $padding = floor(($this->lineLength - strlen($text)) / 2);
        $spaces = str_repeat(" ", max($padding, 0));
        return $spaces . $text . "\n";
    }
}
