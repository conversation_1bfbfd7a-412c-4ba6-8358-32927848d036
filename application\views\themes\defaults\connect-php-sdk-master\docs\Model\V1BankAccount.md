# V1BankAccount

## Properties
Name | Getter | Setter | Type | Description | Notes
------------ | ------------- | ------------- | ------------- | ------------- | -------------
**id** | getId() | setId($value) | **string** | The bank account&#39;s Square-issued ID. | [optional] 
**merchant_id** | getMerchantId() | setMerchantId($value) | **string** | The Square-issued ID of the merchant associated with the bank account. | [optional] 
**bank_name** | getBankName() | setBankName($value) | **string** | The name of the bank that manages the account. | [optional] 
**name** | getName() | setName($value) | **string** | The name associated with the bank account. | [optional] 
**routing_number** | getRoutingNumber() | setRoutingNumber($value) | **string** | The bank account&#39;s routing number. | [optional] 
**account_number_suffix** | getAccountNumberSuffix() | setAccountNumberSuffix($value) | **string** | The last few digits of the bank account number. | [optional] 
**currency_code** | getCurrencyCode() | setCurrencyCode($value) | **string** | The currency code of the currency associated with the bank account, in ISO 4217 format. For example, the currency code for US dollars is USD. | [optional] 
**type** | getType() | setType($value) | **string** | The bank account&#39;s type (for example, savings or checking). | [optional] 

Note: All properties are protected and only accessed via getters and setters.

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)

