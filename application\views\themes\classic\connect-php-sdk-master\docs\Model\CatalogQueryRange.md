# CatalogQueryRange

## Properties
Name | Getter | Setter | Type | Description | Notes
------------ | ------------- | ------------- | ------------- | ------------- | -------------
**attribute_name** | getAttributeName() | setAttributeName($value) | **string** | The name of the attribute to be searched. | 
**attribute_min_value** | getAttributeMinValue() | setAttributeMinValue($value) | **int** | The desired minimum value for the search attribute (inclusive). | [optional] 
**attribute_max_value** | getAttributeMaxValue() | setAttributeMaxValue($value) | **int** | The desired maximum value for the search attribute (inclusive). | [optional] 

Note: All properties are protected and only accessed via getters and setters.

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)

