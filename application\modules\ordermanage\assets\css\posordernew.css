@charset "utf-8";
.tabsection {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: fixed;
  z-index: 1000;
  background: #f5f7fa;
  left: 0;
  right: 0;
  padding: 12px 30px;
  top: 0;
  transition: transform 0.3s;
}
.sidebar-collapse .tabsection {
  left: 0;
}

.slimScrollDiv {
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%;
  padding-right: 5px;
}

.slimScrollDiv::-webkit-scrollbar {
  width: 4px;
}

.slimScrollDiv::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgb(210, 208, 208);
  border-radius: 10px;
}

.slimScrollDiv::-webkit-scrollbar-thumb {
  background: rgb(108, 227, 122);
  border-radius: 10px;
}

.leftSidebarPos {
  position: relative;
  overflow: hidden;
  width: auto;
  height: calc(-230px + 100vh);
}
.update-height {
  height: calc(-210px + 100vh) !important;
}

.leftSidebarPosMain {
  position: relative;
  overflow: hidden;
  width: auto;
  height: calc(-197px + 100vh);
}

@media (max-width: 991px) {
  .leftSidebarPos {
    height: 100%;
  }

  .leftSidebarPosMain {
    height: 100%;
  }
  .nav > li > a {
    margin-bottom: 5px;
  }
  .pb-sm-0 {
    padding-bottom: 0px !important;
  }
}

.rightSidebarPos {
  position: fixed;
  top: 56px;
  right: 5%;
  width: 33.3333%; /* Adjust the width based on the Bootstrap column size */
  padding-top: 20px;
}

.tab-content-xs {
  top: 60px;
  position: relative;
  padding: 0 0 40px;
}

.tgbar {
  min-width: 150px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.tgbar a {
  padding: 0 0px;
  margin: 0px 4px;
  position: relative;
  border-radius: 8px;
}

.tgbar > a > i {
  border: none;
  padding: 10px 10px;
  text-align: center;
  color: #374767;
  background-color: transparent;
  font-size: 25px;
  width: auto;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tgbar .sidebar-toggle {
  float: left;
  background-color: #f5f5f5;
  background-image: none;
  padding: 6px 6px 3px;
  font-family: fontAwesome;
  color: #374767;
  font-size: 26px;
  line-height: 26px;
}

.popover {
  min-width: 50em !important;
}

.wrapper.pos {
  height: 100vh;
}

@keyframes anim_opa {
  50% {
    opacity: 0.2;
  }
}
@media (min-width: 576px) {
  .smallview {
    display: none;
  }
}
@media (min-width: 768px) {
  .nav > li.mobiletag {
    display: none;
  }
}

@media (max-width: 896px) {
  .tab-content-xs {
    top: 115px;
  }
}
@media (max-width: 991px) {
  .listcat {
    display: inline-block;
    padding: 5px 15px;
  }
  .tgbar #fullscreen {
    display: none;
  }
  .person {
    display: none;
  }
  .listcat2 {
    padding: 0;
    text-align: center !important;
  }
}
@media (max-width: 767px) {
  .tabsection {
    left: 0;
    padding: 20px 17px 0;
  }
  .tgbar {
    display: none;
  }
  .cusbtn {
    font-size: 16px;
    padding: 8px 12px;
    margin-right: 4px !important;
  }
  .sidebar-mini.sidebar-collapse .fixedclasspos {
    margin-left: 0;
  }
  .leftview {
    padding: 0;
  }
  .productlist {
    margin-bottom: 200px;
  }
}

@media (max-width: 575px) {
  .tabsection {
    left: 0;
    padding: 20px 17px 0;
  }
  .tab-content-xs {
    top: 68px;
  }
  .responsiveview {
    display: none;
  }
}

@media (max-width: 492px) {
  .tab-content-xs {
    top: 115px;
  }
}

.lang_box {
  line-height: 36px;
  color: #374767;
}

.lang_options {
  min-width: 90px;
}

.dropdown-menu.lang_options {
  position: absolute;
  right: 0;
  left: auto;
}

listcat.listcat {
  color: #fff;
  background: #37a000;
  box-shadow: inset 0 0 0 0 rgba(0, 0, 0, 0.4), -2px -3px 5px -2px rgba(0, 0, 0, 0.4);
  cursor: pointer;
  padding: 5px;
}
.sidebar-mini.sidebar-collapse .content-wrapper.ml-0 {
  margin-left: 0 !important;
}
.sidebar-mini.sidebar-collapse .fixedclasspos {
  margin-left: 15px;
}
.nav-tabs > li > a.maindashboard {
  background: #00a653;
  color: #fff;
  border-radius: 8px;
  padding: 12px 15px;
  margin-right: 6px;
  line-height: 18px;
  display: flex;
  align-items: center;
  gap: 7px;
}
.justify-content-end {
  justify-content: end;
}
.pb-0 {
  padding-bottom: 0px;
}
.mb-0 {
  margin-bottom: 0px;
}
@media (min-width: 992px) {
  .update-order-height {
    height: calc(100vh - 90px);
    overflow-y: hidden;
  }
}
.nav-tabs > li {
  margin-bottom: 0px !important;
}
.order-notify {
  display: inline !important;
  position: absolute !important;
  float: right !important;
  top: -8px !important;
  right: 0px !important;
  background: none !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: 50%;
  padding: 0px !important;
}
.order-label {
  font-weight: 700;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  border-radius: 50%;
  border: 0px !important;
}
.new-order-tab {
  box-shadow: none !important;
  background-color: #2b3674 !important;
  border-radius: 8px !important;
  color: #fff !important;
  line-height: 23px !important;
  margin-right: 5px !important;
}
.ongo-order-tab {
  box-shadow: none !important;
  background-color: #219653 !important;
  border-radius: 8px !important;
  color: #fff !important;
  line-height: 23px !important;
  margin-right: 5px !important;
}
.kitchen-order-tab {
  box-shadow: none !important;
  background-color: #9052f5 !important;
  border-radius: 8px !important;
  color: #fff !important;
  line-height: 23px !important;
  margin-right: 5px !important;
}
.online-order-tab {
  box-shadow: none !important;
  background-color: #f2b80a !important;
  border-radius: 8px !important;
  color: #fff !important;
  line-height: 23px !important;
  margin-right: 5px !important;
}
.today-order-tab {
  box-shadow: none !important;
  background-color: #2972ff !important;
  border-radius: 8px !important;
  color: #fff !important;
  line-height: 23px !important;
  margin-right: 5px !important;
}
.search-custom .select2-container--default .select2-selection--single .select2-selection__rendered {
  line-height: 40px;
}
.search-custom .select2-container .select2-selection--single .select2-selection__rendered {
  padding-left: 41px;
}
.search-custom .select2-container .select2-selection--single {
  height: 42px;
  border-radius: 10px;
}
.search-custom .select2-container--default .select2-selection--single .select2-selection__arrow {
  top: 0;
  left: 0;
  height: 41px;
  width: 34px;
}
.search-custom .select2-container--default .select2-selection--single .select2-selection__arrow b:before {
  content: "";
  background-image: url("data:image/svg+xml,%3Csvg width='21' height='20' viewBox='0 0 21 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M8.80094 16.6667C7.07978 16.6667 5.39728 16.178 3.9662 15.2623C2.53511 14.3466 1.41972 13.045 0.761061 11.5224C0.102403 9.99967 -0.0699324 8.32404 0.265851 6.70758C0.601635 5.09108 1.43044 3.60622 2.64748 2.44078C3.86452 1.27534 5.41512 0.481675 7.10319 0.160129C8.79123 -0.161418 10.541 0.00361018 12.1312 0.634342C13.7213 1.26507 15.0805 2.33318 16.0367 3.70359C16.9929 5.07399 17.5032 6.68516 17.5032 8.33334C17.5032 9.42763 17.2781 10.5113 16.8408 11.5224C16.4035 12.5334 15.7624 13.4521 14.9544 14.2259C14.1463 14.9997 13.187 15.6135 12.1312 16.0323C11.0753 16.4511 9.94368 16.6667 8.80094 16.6667ZM8.80094 1.67109C7.4295 1.67109 6.08886 2.06054 4.94855 2.79016C3.80825 3.51978 2.91948 4.55683 2.39466 5.77014C1.86983 6.98346 1.73252 8.31851 2.00007 9.60658C2.26762 10.8946 2.92803 12.0778 3.89778 13.0064C4.86754 13.935 6.10306 14.5675 7.44815 14.8237C8.79319 15.0799 10.1874 14.9484 11.4545 14.4459C12.7215 13.9432 13.8045 13.0922 14.5664 12.0002C15.3283 10.9083 15.735 9.62451 15.735 8.31121C15.735 6.55013 15.0045 4.8612 13.704 3.61594C12.4037 2.37067 10.64 1.67109 8.80094 1.67109Z' fill='%23E3E3E3'/%3E%3Cpath d='M19.2869 20C19.1694 20.0005 19.0528 19.9777 18.9442 19.9328C18.8357 19.888 18.7371 19.8219 18.6545 19.7386L13.727 14.8319C13.5689 14.6629 13.4829 14.4395 13.487 14.2086C13.491 13.9778 13.585 13.7576 13.749 13.5943C13.9129 13.431 14.1341 13.3375 14.3659 13.3335C14.5977 13.3294 14.822 13.4151 14.9917 13.5725L19.9192 18.4793C20.0868 18.6463 20.1809 18.8728 20.1809 19.1089C20.1809 19.345 20.0868 19.5715 19.9192 19.7386C19.8367 19.8219 19.7381 19.888 19.6296 19.9328C19.521 19.9777 19.4044 20.0005 19.2869 20Z' fill='%23E3E3E3'/%3E%3C/svg%3E");
  background-size: auto;
  background-repeat: no-repeat;
  background-position: center;
  display: inline-block;
  width: 20px;
  height: 18px;
}
.search-custom .select2-container--default.select2-container--focus .select2-selection--single {
  box-shadow: 0 0 1px #00a653;
  border-color: #00a653;
}
.search-custom .select2-container--default .select2-selection--single:hover {
  box-shadow: 0 0 1px #00a653;
  border-color: #00a653;
}
.search-custom .select2-container--default.select2-container--open .select2-selection--single {
  box-shadow: 0 0 1px #00a653;
  border-color: #00a653;
}

.custom-select .select2-container--default .select2-selection--single .select2-selection__rendered {
  line-height: 40px;
}
.custom-select .select2-container .select2-selection--single .select2-selection__rendered {
  padding-left: 30px;
  padding-right: 3px;
}
.custom-select .select2-container .select2-selection--single {
  height: 42px;
  border-radius: 6px;
}
.custom-select .select2-container--default .select2-selection--single .select2-selection__arrow {
  top: 0;
  left: 0;
  height: 41px;
  width: 34px;
}
.custom-select .select2-container--default.select2-container--focus .select2-selection--single {
  box-shadow: 0 0 1px #00a653;
  border-color: #00a653;
}
.custom-select .select2-container--default .select2-selection--single:hover {
  box-shadow: 0 0 1px #00a653;
  border-color: #00a653;
}
.custom-select .select2-container--default.select2-container--open .select2-selection--single {
  box-shadow: 0 0 1px #00a653;
  border-color: #00a653;
}
.custom-form-control {
  height: 42px;
  border-radius: 6px !important;
  padding: 5px 20px 5px 10px !important;
}
.ui-widget.ui-widget-content {
  z-index: 999 !important;
}
.bg-alice-blue {
  background-color: #f5f7fa;
}
.m-3 {
  margin: 3px;
}
.p-6 {
  padding: 6px;
}
.p-10 {
  padding: 10px;
}
.h-100 {
  height: 100%;
}
.pos-img-wrap {
  background-color: #f5f7fa;
  padding: 10px;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.pos-img-wrap img {
  border-bottom: 0px !important;
  max-height: 130px !important;
  width: auto !important;
  margin: auto;
}
.product-h {
  min-height: 222px;
}
.pt-12 {
  padding-top: 12px;
}
.pos-category {
  color: #1e1e1e !important;
  background: #ffffff !important;
  padding: 7px 7px !important;
  margin-bottom: 7px !important;
  border: 1px solid rgb(207 207 207) !important;
  box-shadow: none !important;
  font-weight: 600 !important;
  border-radius: 6px;
}
.pos-category-sub {
  color: #484141 !important;
  background: #ffffff !important;
  border: none !important;
  box-shadow: none !important;
}
@media (min-width: 992px) {
  .cat-nav .dropdown-menu,
  .cat-nav2 .dropdown-menucat {
    background-color: #e5e5e5 !important;
    margin-top: 5px !important;
  }
  .cat-nav .dropdown-menu > li > a,
  .cat-nav2 .dropdown-menucat > li > a {
    color: #1f201e !important;
  }
}
.border-bottom-white {
  border-bottom: 1px solid #fff;
}
.py-10 {
  padding-top: 10px;
  padding-bottom: 10px;
}
.pr-0 {
  padding-right: 0px !important;
}

.item-table {
  position: relative;
}
.item-table thead {
  position: sticky;
  top: 0;
}
.item-table thead th {
  background: #219653;
  color: white;
  font-weight: bold;
}
.item-table tr:nth-of-type(odd) {
  background: #fafafa;
}

.item-table td,
th {
  padding: 12px 7px !important;
  border: 0px !important;
  text-align: left !important;
  font-size: 15px;
  font-weight: 500;
}

.custom-table thead th,
.custom-table thead td {
  background: #f1f9f5;
  color: rgb(31, 31, 31);
  font-weight: bold;
}
.custom-table tr:nth-of-type(even) {
  background: #f5f7fa;
}
.custom-table tr:nth-of-type(odd) {
  background: #ffffff;
}

.custom-table td,
th {
  padding: 12px 7px !important;
  border: 0px !important;
  text-align: left !important;
  font-size: 15px;
  font-weight: 500;
}

.btn-incriment {
  border: 0px !important;
  padding: 5px 8px !important;
  background-color: #00a653 !important;
}
.btn-dicriment {
  border: 0px !important;
  padding: 5px 8px !important;
  background-color: #c8c8c8 !important;
}
.fixedclasspos {
  bottom: 0;
  background: #fff;
  z-index: 999;
  padding: 5px 15px;
  position: absolute !important;
  left: 0px;
  margin-left: 0px !important;
  right: 0;
}
@media (min-width: 992px) {
  .height-cal {
    height: calc(-90px + 100vh);
    overflow: hidden;
  }
  .leftSidebarPosMain {
    height: calc(-90px + 100vh) !important;
  }
}

.summary-table {
  border: 0px !important;
}
.summary-table tr td {
  border: 0px !important;
}
.text-end {
  text-align: right !important;
}
.float-right {
  float: right;
}
.fw-700 {
  font-weight: 700 !important;
}
.fs-20 {
  font-size: 20px !important;
}
.fs-17 {
  font-size: 17px !important;
}
.text-summary {
  color: #374767;
}
.summary-bg {
  background-color: #f5f7fa;
}
.bg-transparent {
  background: transparent;
}
.bt-none {
  border-top: 0px !important;
}
.btn-navy {
  background-color: #313f6b;
  border: 0px;
  border-radius: 6px;
  color: #fff;
  font-size: 15px !important;
  padding: 10px 10px !important;
}
.btn-navy:hover {
  color: #fff;
}
.btn-red {
  background-color: #d43407;
  border: 0px;
  border-radius: 6px;
  color: #fff;
  font-size: 15px !important;
  padding: 10px 10px !important;
}
.btn-red:hover {
  color: #fff;
}
.btn-blue {
  background-color: #2972ff;
  border: 0px;
  border-radius: 6px;
  color: #fff;
  font-size: 15px !important;
  padding: 10px 10px !important;
}
.btn-blue:hover {
  color: #fff;
}
.btn-green {
  background-color: #00a653;
  border: 0px;
  border-radius: 6px;
  color: #fff;
  font-size: 15px !important;
  padding: 10px 10px !important;
}
.btn-green:hover {
  color: #fff;
}
.btn-ash {
  background-color: #5a5a5a;
  border: 0px;
  border-radius: 6px;
  color: #fff;
  font-size: 15px !important;
}
.btn-ash:hover {
  color: #fff;
}
.btn-violet {
  background-color: #9052f5;
  border: 0px;
  border-radius: 6px;
  color: #fff;
  font-size: 15px !important;
}
.btn-violet:hover {
  color: #fff;
}
.btn-orange {
  background-color: #ff7b4e;
  border: 0px;
  border-radius: 6px;
  color: #fff;
  font-size: 15px !important;
}
.btn-orange:hover {
  color: #fff;
}
@media (min-width: 992px) {
  .product-table-height {
    height: calc(-444px + 100vh) !important;
  }
}
.product-table .slimScrollDiv {
  height: 100% !important;
}
.h-40 {
  height: 40px;
}
.pb-60 {
  padding-bottom: 60px;
}
.pb-20 {
  padding-bottom: 20px;
}
.tabsection .active .newtab {
  background-color: #ffffff !important;
  color: #000 !important;
}
.position-fixed {
  position: fixed !important;
}
.my-6 {
  margin-top: 6px;
  margin-bottom: 6px;
}
.pl-5 {
  padding-left: 5px;
}
.payment-method-total {
  background-color: #fdfdfd;
  border: 1px solid #e6e6e6;
  padding: 10px;
  border-radius: 8px;
  margin-bottom: 10px;
}
.payment-method-calculator {
  background-color: #f3f3f3 !important;
  border-radius: 8px !important;
  padding: 10px !important;
  border: 3px solid #f3f3f3 !important;
}
.payment-method-calculator .grid-item {
  background-color: rgb(255 255 255) !important;
  border: 4px solid #f3f3f3 !important;
  padding: 10px !important;
  font-size: 30px !important;
  text-align: center !important;
  border-radius: 10px !important;
}
