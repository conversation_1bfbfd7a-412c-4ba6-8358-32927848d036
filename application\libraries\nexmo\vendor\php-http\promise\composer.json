{"name": "php-http/promise", "description": "Promise used for asynchronous HTTP requests", "license": "MIT", "keywords": ["promise"], "homepage": "http://httplug.io", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require-dev": {"phpspec/phpspec": "^2.4", "henrikbjorn/phpspec-code-coverage": "^1.0"}, "autoload": {"psr-4": {"Http\\Promise\\": "src/"}}, "scripts": {"test": "vendor/bin/phpspec run", "test-ci": "vendor/bin/phpspec run -c phpspec.yml.ci"}, "extra": {"branch-alias": {"dev-master": "1.1-dev"}}}