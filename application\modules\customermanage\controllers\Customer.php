<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Customer extends MX_Controller
{

    public function __construct()
    {
        parent::__construct();
        $this->db->query('SET SESSION sql_mode = ""');
        $this->load->model(array(
            'Customer_model'
        ));

        // Check if user is logged in
        if (!$this->session->userdata('isAdmin')) {
            redirect('login');
        }

        // Check permission
        $this->permission->method('customermanage', 'read')->redirect();
    }

    // Display customer list
    public function index()
    {
        $data['title'] = display('customer_list');

        // Pagination configuration
        $config["base_url"] = base_url('customermanage/customer/index');
        $config["total_rows"] = $this->Customer_model->count_customers();
        $config["per_page"] = 25;
        $config["uri_segment"] = 4;
        $config["last_link"] = "Last";
        $config["first_link"] = "First";
        $config['next_link'] = 'Next';
        $config['prev_link'] = 'Prev';
        $config['full_tag_open'] = "<ul class='pagination col-xs pull-right'>";
        $config['full_tag_close'] = "</ul>";
        $config['num_tag_open'] = '<li>';
        $config['num_tag_close'] = '</li>';
        $config['cur_tag_open'] = "<li class='disabled'><li class='active'><a href='#'>";
        $config['cur_tag_close'] = "<span class='sr-only'></span></a></li>";
        $config['next_tag_open'] = "<li>";
        $config['next_tag_close'] = "</li>";
        $config['prev_tag_open'] = "<li>";
        $config['prev_tagl_close'] = "</li>";
        $config['first_tag_open'] = "<li>";
        $config['first_tagl_close'] = "</li>";
        $config['last_tag_open'] = "<li>";
        $config['last_tagl_close'] = "</li>";

        $this->pagination->initialize($config);
        $page = ($this->uri->segment(4)) ? $this->uri->segment(4) : 0;

        $data["customers"] = $this->Customer_model->get_customers($config["per_page"], $page);
        $data["links"] = $this->pagination->create_links();

        $data['module'] = "customermanage";
        $data['page'] = "customer_list";
        echo Modules::run('template/layout', $data);
    }

    // Display customer details
    public function customerdetails($customer_id = null)
    {
        if ($customer_id == null) {
            redirect('customermanage/customer/index');
        }

        $data['title'] = display('customer_details');

        // Get customer information
        $data['customer'] = $this->Customer_model->get_customer_by_id($customer_id);

        if (!$data['customer']) {
            $this->session->set_flashdata('exception', 'Customer not found');
            redirect('customermanage/customer/index');
        }

        // Get customer statistics
        $data['statistics'] = $this->Customer_model->get_customer_statistics($customer_id);

        // Pagination for orders
        $config["base_url"] = base_url('customermanage/customer/customerdetails/' . $customer_id);
        $config["total_rows"] = $this->Customer_model->count_customer_orders($customer_id);
        $config["per_page"] = 10;
        $config["uri_segment"] = 5;
        $config["last_link"] = "Last";
        $config["first_link"] = "First";
        $config['next_link'] = 'Next';
        $config['prev_link'] = 'Prev';
        $config['full_tag_open'] = "<ul class='pagination col-xs pull-right'>";
        $config['full_tag_close'] = "</ul>";
        $config['num_tag_open'] = '<li>';
        $config['num_tag_close'] = '</li>';
        $config['cur_tag_open'] = "<li class='disabled'><li class='active'><a href='#'>";
        $config['cur_tag_close'] = "<span class='sr-only'></span></a></li>";
        $config['next_tag_open'] = "<li>";
        $config['next_tag_close'] = "</li>";
        $config['prev_tag_open'] = "<li>";
        $config['prev_tagl_close'] = "</li>";
        $config['first_tag_open'] = "<li>";
        $config['first_tagl_close'] = "</li>";
        $config['last_tag_open'] = "<li>";
        $config['last_tagl_close'] = "</li>";

        $this->pagination->initialize($config);
        $page = ($this->uri->segment(5)) ? $this->uri->segment(5) : 0;

        // Get customer orders
        $data['orders'] = $this->Customer_model->get_customer_orders($customer_id, $config["per_page"], $page);
        $data["links"] = $this->pagination->create_links();

        $data['module'] = "customermanage";
        $data['page'] = "customer_details";
        echo Modules::run('template/layout', $data);
    }

    // Get order details via AJAX
    public function get_order_details()
    {
        // Set proper headers for JSON response
        header('Content-Type: application/json');

        try {
            $order_id = $this->input->post('order_id');

            if ($order_id) {
                $order_details = $this->Customer_model->get_order_details($order_id);

                if ($order_details) {
                    $response = array('status' => 'success', 'data' => $order_details);
                } else {
                    $response = array('status' => 'error', 'message' => 'Order details not found');
                }
            } else {
                $response = array('status' => 'error', 'message' => 'Invalid order ID');
            }
        } catch (Exception $e) {
            $response = array('status' => 'error', 'message' => 'Database error: ' . $e->getMessage());
        }

        // Clean output buffer and send JSON response
        ob_clean();
        echo json_encode($response);
        exit;
    }

    // Search customers via AJAX
    public function search_customers()
    {
        // Set proper headers for JSON response
        header('Content-Type: application/json');

        try {
            $search_term = $this->input->post('search_term');

            if ($search_term) {
                $customers = $this->Customer_model->search_customers($search_term, 20);

                if ($customers) {
                    $response = array('status' => 'success', 'data' => $customers);
                } else {
                    $response = array('status' => 'error', 'message' => 'No customers found');
                }
            } else {
                $response = array('status' => 'error', 'message' => 'Search term is required');
            }
        } catch (Exception $e) {
            $response = array('status' => 'error', 'message' => 'Database error: ' . $e->getMessage());
        }

        // Clean output buffer and send JSON response
        ob_clean();
        echo json_encode($response);
        exit;
    }

    // Display customer ranking
    public function ranking()
    {
        $data['title'] = display('customer_ranking');

        // Get ranking type from URL parameter (default: spent)
        $ranking_type = $this->uri->segment(4) ? $this->uri->segment(4) : 'spent';

        if ($ranking_type == 'orders') {
            $data['top_customers'] = $this->Customer_model->get_top_customers_by_orders(20);
            $data['ranking_type'] = 'orders';
            $data['ranking_title'] = display('top_customers_by_orders');
        } else {
            $data['top_customers'] = $this->Customer_model->get_top_customers(20);
            $data['ranking_type'] = 'spent';
            $data['ranking_title'] = display('top_customers_by_spent');
        }

        $data['module'] = "customermanage";
        $data['page'] = "customer_ranking";
        echo Modules::run('template/layout', $data);
    }
}
