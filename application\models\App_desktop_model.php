<?php

class App_desktop_model extends CI_Model
{

    public function authenticate_user($table, $data)
    {
        $Type     = $data['email'];
        $Password = $data['password'];
        $this->db->select("*");
        $this->db->where('email', $data['email']);
        $this->db->where("(password = '" . $Password . "' OR password =  '" . md5($Password) . "')", null, true);
        $query    = $this->db->get($table)->row();
        $num_rows = $this->db->count_all_results();

        if ($num_rows > 0) {
            return $query;
        } else {
            return false;
        }

    }

    public function checkEmailOrPhoneIsRegistered($table, $data)
    {
        $this->db->select('email, password');
        $this->db->where('email', $data['email']);
        $query    = $this->db->get($table)->row();
        $num_rows = $this->db->count_all_results();

        if ($num_rows > 0) {
            return $query;
        } else {
            return false;
        }

    }

    public function check_user($data)
    {

        $this->db->where('UserUUID', $data['UserUUID']);
        $this->db->where('Session', $data['Session']);
        $query = $this->db->get('tbluser');

        if ($query->num_rows() > 0) {
            return true;
        } else {
            return false;
        }

    }

    public function insert_data($table, $data)
    {

        $this->db->insert($table, $data);

        return $this->db->insert_id();
    }

    public function update_date($table, $data, $field_name, $field_value)
    {
        $this->db->where($field_name, $field_value);
        $this->db->update($table, $data);
        return $this->db->affected_rows();
    }

    public function read($select_items, $table, $where_array)
    {
        $this->db->select($select_items);
        $this->db->from($table);

        foreach ($where_array as $field => $value) {
            $this->db->where($field, $value);
        }

        return $this->db->get()->row();
    }

    public function readnum($select_items, $table, $where_array)
    {
        $this->db->select($select_items);
        $this->db->from($table);

        foreach ($where_array as $field => $value) {
            $this->db->where($field, $value);
        }

        $query = $this->db->get();
        return $query->num_rows();
    }

    public function read_all($select_items, $table, $field_name, $field_value, $order_by_name = null, $order_by = null)
    {
        $this->db->select($select_items);
        $this->db->from($table);
        $this->db->where($field_name, $field_value);

        if ($order_by_name != null && $order_by != null) {
            $this->db->order_by($order_by_name, $order_by);
        }

        return $this->db->get()->result();
    }

    public function read_allapi($select_items, $table, $orderby, $delitem = "", $stype = "", $val = "")
    {
        $this->db->select($select_items);
        $this->db->from($table);

        if ($delitem != "") {
            $this->db->where($delitem, 0);
        }

        if ($stype != "") {
            $this->db->where($stype, $val);
        }

        $this->db->order_by($orderby, 'DESC');
        return $this->db->get()->result();
    }

    public function categorylist()
    {
        $this->db->select('*');
        $this->db->from('item_category');
        $query        = $this->db->get();
        $categorylist = $query->result();
        return $categorylist;
    }

    public function foodlist()
    {
        $this->db->select('*');
        $this->db->from('item_foods');
        $query    = $this->db->get();
        $itemlist = $query->result();
        return $itemlist;
    }

    public function verientlist()
    {
        $this->db->select('*');
        $this->db->from('variant');
        $query    = $this->db->get();
        $itemlist = $query->result();
        return $itemlist;
    }

    public function addonslist()
    {
        $this->db->select('*');
        $this->db->from('add_ons');
        $query  = $this->db->get();
        $addons = $query->result();
        return $addons;
    }

    public function addonsassignlist()
    {
        $this->db->select('*');
        $this->db->from('menu_add_on');
        $query  = $this->db->get();
        $addons = $query->result();
        return $addons;
    }

    public function availablelist()
    {
        $this->db->select('*');
        $this->db->from('foodvariable');
        $query  = $this->db->get();
        $addons = $query->result();
        return $addons;
    }

    public function orderitem($orderid, $customerid)
    {
        $saveid  = $customerid;
        $bill    = 1;
        $cid     = $customerid;
        $newdate = date('Y-m-d');
        $lastid  = $this->db->select("*")->from('customer_order')->order_by('order_id', 'desc')->get()->row();

        $sl = $lastid->order_id;

        if (empty($sl)) {
            $sl = 1;
        } else {
            $sl = $sl + 1;
        }

        $si_length = strlen((int) $sl);

        $str    = '0000';
        $str2   = '0000';
        $cutstr = substr($str, $si_length);
        $sino   = $cutstr . $sl;

        $orderid   = $orderid;
        $json      = $this->input->post('CartData', true);
        $cartArray = json_decode($json);

        foreach ($cartArray as $item) {
            $addonsqty = '';
            $addonsid  = '';

            if ($item->addons == 1) {

                foreach ($item->addonsinfo as $addonsitem) {
                    $addonsqty .= $addonsitem->count . ",";
                    $addonsid .= $addonsitem->addonsid . ",";
                }

                $addonsqty = trim($addonsqty, ',');
                $addonsid  = trim($addonsid, ',');
            }

            $data3 = ['order_id' => $orderid,
                'menu_id'            => $item->ProductsID,
                'menuqty'            => $item->count,
                'varientid'          => $item->variantid,
                'add_on_id'          => $addonsid,
                'addonsqty'          => $addonsqty,
            ];
            $this->db->insert('order_menu', $data3);
        }

        if ($bill == 1) {
            $payment = $this->input->post('Pay_type');

            if (!empty($payment)) {
                $discount = $this->input->post('invoice_discount');
                $scharge  = $this->input->post('service_charge');
                $vat      = $this->input->post('vat');

                if ($vat == '') {
                    $vat = 0;
                }

                if ($discount == '') {
                    $discount = 0;
                }

                if ($scharge == '') {
                    $scharge = 0;
                }

                $billstatus = 0;

                if ($payment == 5) {
                    $billstatus = 0;
                } else

                if ($payment == 3) {
                    $billstatus = 0;
                } else

                if ($payment == 2) {
                    $billstatus = 0;
                }

                $billinfo = [
                    'customer_id'       => $cid,
                    'order_id'          => $orderid,
                    'total_amount'      => $this->input->post('SubtotalTotal'),
                    'discount'          => $this->input->post('invoice_discount'),
                    'service_charge'    => $this->input->post('service_charge'),
                    'shipping_type'     => $this->input->post('shippingtype'),
                    'delivarydate'      => $this->input->post('shippingdate'),
                    'VAT'               => $this->input->post('vat'),
                    'bill_amount'       => $this->input->post('grandtotal'),
                    'bill_date'         => $newdate,
                    'bill_time'         => date('H:i:s'),
                    'bill_status'       => $billstatus,
                    'payment_method_id' => $this->input->post('Pay_type'),
                    'create_by'         => $saveid,
                    'create_date'       => date('Y-m-d'),
                ];

                $this->db->insert('bill', $billinfo);

                $updatetData = ['order_status' => 1];
                $this->db->where('order_id', $orderid);
                $this->db->update('customer_order', $updatetData);
            }

        }

        return $orderid;
    }

    public function customerlist()
    {
        $this->db->select('*');
        $this->db->from('customer_info');
        $query  = $this->db->get();
        $addons = $query->result();
        return $addons;
    }

    public function tablelist()
    {
        $this->db->select('*');
        $this->db->from('rest_table');
        $query  = $this->db->get();
        $addons = $query->result();
        return $addons;
    }

    public function ctypelist()
    {
        $this->db->select('*');
        $this->db->from('customer_type');
        $query  = $this->db->get();
        $addons = $query->result();
        return $addons;
    }

    public function waiterlist()
    {
        return $data = $this->db->select("*")
            ->from('employee_history')
            ->where('pos_id', 6)
            ->get()
            ->result();
    }

    public function foodavailablelist()
    {
        $this->db->select('*');
        $this->db->from('foodvariable');
        $query         = $this->db->get();
        $foodavailable = $query->result();
        return $foodavailable;
    }

    public function allthirdpartylist()
    {
        $this->db->select('*');
        $this->db->from('tbl_thirdparty_customer');
        $query         = $this->db->get();
        $allthirdparty = $query->result();
        return $allthirdparty;
    }

    public function paymentmethod()
    {
        $this->db->select('*');
        $this->db->from('payment_method');
        $query  = $this->db->get();
        $pments = $query->result();
        return $pments;
    }

    public function allbank()
    {
        $this->db->select('*');
        $this->db->from('tbl_bank');
        $query = $this->db->get();
        $bank  = $query->result();
        return $bank;
    }

    public function allcardterminal()
    {
        $this->db->select('*');
        $this->db->from('tbl_card_terminal');
        $query        = $this->db->get();
        $cardterminal = $query->result();
        return $cardterminal;
    }

    public function allanguage()
    {
        $this->db->select('*');
        $this->db->from('language');
        $query    = $this->db->get();
        $language = $query->result();

        return $language;
    }

    public function resseting()
    {
        $this->db->select('setting.*,currency.*');
        $this->db->from('setting');
        $this->db->join('currency', 'currency.currencyid=setting.currency', 'left');
        $query       = $this->db->get();
        $settinginfo = $query->result();
        return $settinginfo;
    }

    public function counterlist()
    {
        $this->db->select('*');
        $this->db->from('tbl_cashcounter');
        $query    = $this->db->get();
        $itemlist = $query->result();
        return $itemlist;
    }

    public function customerorderkitchen($id, $kitchen)
    {
        $this->db->select('order_menu.*,item_foods.ProductName,item_foods.kitchenid,item_foods.cookedtime,variant.variantid,variant.variantName,variant.price');
        $this->db->from('order_menu');
        $this->db->join('item_foods', 'order_menu.menu_id=item_foods.ProductsID', 'left');
        $this->db->join('variant', 'order_menu.varientid=variant.variantid', 'left');
        $this->db->where('order_menu.order_id', $id);
        $this->db->where('item_foods.kitchenid', $kitchen);
        $query     = $this->db->get();
        $orderinfo = $query->result();
        return $orderinfo;
    }

    public function printerorder()
    {
        $sql = "SELECT * FROM customer_order Where invoiceprint=2 AND splitpay_status=0 AND customer_order.marge_order_id IS NULL UNION SELECT * FROM customer_order Where invoiceprint=2 AND splitpay_status=0 AND customer_order.marge_order_id IS NOT NULL GROUP BY customer_order.marge_order_id order by order_id ASC";

        $query = $this->db->query($sql);

        $orderdetails = $query->result();
        return $orderdetails;
    }

    public function customerorder($id)
    {
        $cond = "order_menu.order_id IN(" . $id . ")";
        $this->db->select('order_menu.*,item_foods.ProductName,item_foods.kitchenid,item_foods.cookedtime,variant.variantid,variant.variantName,variant.price as vprice');
        $this->db->from('order_menu');
        $this->db->join('item_foods', 'order_menu.menu_id=item_foods.ProductsID', 'left');
        $this->db->join('variant', 'order_menu.varientid=variant.variantid', 'left');
        $this->db->where($cond);
        $query     = $this->db->get();
        $orderinfo = $query->result();
        //echo $this->db->last_query();
        return $orderinfo;
    }

    public function updateSuborderDatalist($rowidarray)
    {
        $this->db->select('order_menu.*,item_foods.*,variant.variantName,variant.price');
        $this->db->from('order_menu');
        $this->db->join('item_foods', 'order_menu.menu_id=item_foods.ProductsID', 'left');
        $this->db->join('variant', 'order_menu.varientid=variant.variantid', 'left');
        $this->db->where_in('order_menu.row_id', $rowidarray);

        $query     = $this->db->get();
        $orderinfo = $query->result();
        //echo $this->db->last_query();
        return $orderinfo;
    }

}
