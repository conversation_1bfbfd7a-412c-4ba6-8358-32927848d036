# InventoryAdjustment

## Properties
Name | Getter | Setter | Type | Description | Notes
------------ | ------------- | ------------- | ------------- | ------------- | -------------
**id** | getId() | setId($value) | **string** | A unique ID generated by Square for the [InventoryAdjustment](#type-inventoryadjustment). | [optional] 
**reference_id** | getReferenceId() | setReferenceId($value) | **string** | An optional ID provided by the application to tie the [InventoryAdjustment](#type-inventoryadjustment) to an external system. | [optional] 
**from_state** | getFromState() | setFromState($value) | **string** | The [InventoryState](#type-inventorystate) of the related quantity of items before the adjustment. | [optional] 
**to_state** | getToState() | setToState($value) | **string** | The [InventoryState](#type-inventorystate) of the related quantity of items after the adjustment. | [optional] 
**location_id** | getLocationId() | setLocationId($value) | **string** | The Square ID of the [Location](#type-location) where the related quantity of items are being tracked. | [optional] 
**catalog_object_id** | getCatalogObjectId() | setCatalogObjectId($value) | **string** | The Square generated ID of the [CatalogObject](#type-catalogobject) being tracked. | [optional] 
**catalog_object_type** | getCatalogObjectType() | setCatalogObjectType($value) | **string** | The [CatalogObjectType](#type-catalogobjecttype) of the [CatalogObject](#type-catalogobject) being tracked. Tracking is only supported for the &#x60;ITEM_VARIATION&#x60; type. | [optional] 
**quantity** | getQuantity() | setQuantity($value) | **string** | The number of items affected by the adjustment as a decimal string. Fractional quantities are not supported. | [optional] 
**total_price_money** | getTotalPriceMoney() | setTotalPriceMoney($value) | [**\SquareConnect\Model\Money**](Money.md) | The read-only total price paid for goods associated with the adjustment. Present if and only if &#x60;to_state&#x60; is &#x60;SOLD&#x60;. Always non-negative. | [optional] 
**occurred_at** | getOccurredAt() | setOccurredAt($value) | **string** | A client-generated timestamp in RFC 3339 format that indicates when the adjustment took place. For write actions, the &#x60;occurred_at&#x60; timestamp cannot be older than 24 hours or in the future relative to the time of the request. | [optional] 
**created_at** | getCreatedAt() | setCreatedAt($value) | **string** | A read-only timestamp in RFC 3339 format that indicates when Square received the adjustment. | [optional] 
**source** | getSource() | setSource($value) | [**\SquareConnect\Model\SourceApplication**](SourceApplication.md) | Read-only information about the application that caused the inventory adjustment. | [optional] 
**employee_id** | getEmployeeId() | setEmployeeId($value) | **string** | The Square ID of the [Employee](#type-employee) responsible for the inventory adjustment. | [optional] 
**transaction_id** | getTransactionId() | setTransactionId($value) | **string** | The read-only Square ID of the [Transaction][#type-transaction] that caused the adjustment. Only relevant for payment-related state transitions. | [optional] 
**refund_id** | getRefundId() | setRefundId($value) | **string** | The read-only Square ID of the [Refund][#type-refund] that caused the adjustment. Only relevant for refund-related state transitions. | [optional] 
**purchase_order_id** | getPurchaseOrderId() | setPurchaseOrderId($value) | **string** | The read-only Square ID of the purchase order that caused the adjustment. Only relevant for state transitions from the Square for Retail app. | [optional] 
**goods_receipt_id** | getGoodsReceiptId() | setGoodsReceiptId($value) | **string** | The read-only Square ID of the Square goods receipt that caused the adjustment. Only relevant for state transitions from the Square for Retail app. | [optional] 

Note: All properties are protected and only accessed via getters and setters.

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)

