{"name": "laminas/laminas-validator", "description": "Validation classes for a wide range of domains, and the ability to chain validators to create complex validation criteria", "license": "BSD-3-<PERSON><PERSON>", "keywords": ["laminas", "validator"], "homepage": "https://laminas.dev", "support": {"docs": "https://docs.laminas.dev/laminas-validator/", "issues": "https://github.com/laminas/laminas-validator/issues", "source": "https://github.com/laminas/laminas-validator", "rss": "https://github.com/laminas/laminas-validator/releases.atom", "chat": "https://laminas.dev/chat", "forum": "https://discourse.laminas.dev"}, "config": {"sort-packages": true, "platform": {"php": "8.1.99"}, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}, "extra": {"laminas": {"component": "Laminas\\Validator", "config-provider": "Laminas\\Validator\\ConfigProvider"}}, "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "laminas/laminas-servicemanager": "^3.21.0", "laminas/laminas-stdlib": "^3.19", "psr/http-message": "^1.0.1 || ^2.0.0"}, "require-dev": {"laminas/laminas-coding-standard": "^2.5", "laminas/laminas-db": "^2.20", "laminas/laminas-filter": "^2.35.2", "laminas/laminas-i18n": "^2.26.0", "laminas/laminas-session": "^2.20", "laminas/laminas-uri": "^2.11.0", "phpunit/phpunit": "^10.5.20", "psalm/plugin-phpunit": "^0.19.0", "psr/http-client": "^1.0.3", "psr/http-factory": "^1.1.0", "vimeo/psalm": "^5.24.0"}, "suggest": {"laminas/laminas-db": "Laminas\\Db component, required by the (No)RecordExists validator", "laminas/laminas-filter": "Laminas\\Filter component, required by the Digits validator", "laminas/laminas-i18n": "Laminas\\I18n component to allow translation of validation error messages", "laminas/laminas-i18n-resources": "Translations of validator messages", "laminas/laminas-servicemanager": "Laminas\\ServiceManager component to allow using the ValidatorPluginManager and validator chains", "laminas/laminas-session": "Laminas\\Session component, ^2.8; required by the Csrf validator", "laminas/laminas-uri": "Laminas\\Uri component, required by the Uri and Sitemap\\Loc validators", "psr/http-message": "psr/http-message, required when validating PSR-7 UploadedFileInterface instances via the Upload and UploadFile validators"}, "autoload": {"psr-4": {"Laminas\\Validator\\": "src/"}}, "autoload-dev": {"psr-4": {"LaminasTest\\Validator\\": "test/"}}, "scripts": {"check": ["@cs-check", "@test"], "cs-check": "phpcs", "cs-fix": "phpcbf", "test": "phpunit --colors=always", "test-coverage": "phpunit --colors=always --coverage-clover clover.xml", "static-analysis": "psalm --shepherd --stats"}, "conflict": {"zendframework/zend-validator": "*"}}