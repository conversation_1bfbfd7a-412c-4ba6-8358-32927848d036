# CatalogModifierOverride

## Properties
Name | Getter | Setter | Type | Description | Notes
------------ | ------------- | ------------- | ------------- | ------------- | -------------
**modifier_id** | getModifierId() | setModifierId($value) | **string** | The ID of the [CatalogModifier](#type-catalogmodifier) whose default behavior is being overridden. | 
**on_by_default** | getOnByDefault() | setOnByDefault($value) | **bool** | If &#x60;true&#x60;, this [CatalogModifier](#type-catalogmodifier) should be selected by default for this [CatalogItem](#type-catalogitem). | [optional] 

Note: All properties are protected and only accessed via getters and setters.

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)

