# ObtainTokenRequest

## Properties
Name | Getter | Setter | Type | Description | Notes
------------ | ------------- | ------------- | ------------- | ------------- | -------------
**client_id** | getClientId() | setClientId($value) | **string** | Your application&#39;s ID, available from the [application dashboard](https://connect.squareup.com/apps). | [optional] 
**client_secret** | getClientSecret() | setClientSecret($value) | **string** | Your application&#39;s secret, available from the [application dashboard](https://connect.squareup.com/apps). | [optional] 
**code** | getCode() | setCode($value) | **string** | The authorization code to exchange. | [optional] 
**redirect_uri** | getRedirectUri() | setRedirectUri($value) | **string** | The redirect URL assigned in the [application dashboard](https://connect.squareup.com/apps). | [optional] 

Note: All properties are protected and only accessed via getters and setters.

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)

