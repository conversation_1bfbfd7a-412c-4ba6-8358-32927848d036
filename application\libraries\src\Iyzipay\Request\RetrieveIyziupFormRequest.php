<?php

namespace Iy<PERSON>pay\Request;

use <PERSON>yzipay\JsonBuilder;
use Iyzipay\Request;
use I<PERSON><PERSON>pay\RequestStringBuilder;

class RetrieveIyziupFormRequest extends Request
{
    private $token;

    public function getToken()
    {
        return $this->token;
    }

    public function setToken($token)
    {
        $this->token = $token;
    }

    public function getJsonObject()
    {
        return JsonBuilder::fromJsonObject(parent::getJsonObject())
            ->add("token", $this->getToken())
            ->getObject();
    }

    public function toPKIRequestString()
    {
        return RequestStringBuilder::create()
            ->appendSuper(parent::toPKIRequestString())
            ->append("token", $this->getToken())
            ->getRequestString();
    }
}