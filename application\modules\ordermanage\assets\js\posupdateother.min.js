function getslcategory_update(carid){var product_name=$("#update_product_name").val(),category_id=carid,myurl=$("#posurl_update").val(),csrf=$("#csrfhashresarvation").val();$.ajax({type:"post",async:!1,url:myurl,data:{product_name:product_name,category_id:category_id,isuptade:1,csrf_test_name:csrf},success:function(data){"420"==data?$("#product_search_update").html("Product not found !"):$("#product_search_update").html(data)},error:function(){alert(lang.req_failed)}})}function printRawHtml(view){printJS({printable:view,type:"raw-html"})}function postupdateorder_ajax(){var form=$("#insert_purchase"),url=form.attr("action"),data=form.serialize(),csrf=$("#csrfhashresarvation").val();$.ajax({url:url,type:"POST",data:data,dataType:"json",beforeSend:function(xhr){$("span.error").html("")},success:function(result){swal({title:result.msg,text:result.tokenmsg,type:"success",showCancelButton:!0,confirmButtonColor:"#28a745",confirmButtonText:lang.yes,cancelButtonText:lang.no,closeOnConfirm:!0,closeOnCancel:!0},(function(isConfirm){isConfirm?$.ajax({type:"GET",data:{csrf_test_name:csrf},url:basicinfo.baseurl+"ordermanage/order/postokengenerateupdate/"+result.orderid+"/1",success:function(data){printRawHtml(data)}}):$.ajax({type:"GET",data:{csrf_test_name:csrf},url:basicinfo.baseurl+"ordermanage/order/tokenupdate/"+result.orderid,success:function(data){console.log("done"),window.location.href=basicinfo.baseurl+"ordermanage/order/orderlist"}})})),setTimeout((function(){toastr.options={closeButton:!0,progressBar:!0,showMethod:"slideDown",timeOut:4e3},toastr.success(result.msg,"Success")}),300),console.log(result)},error:function(a){}})}function positemupdate(itemid,existqty,orderid,varientid,isgroup,auid,status){var dataString="itemid="+itemid+"&existqty="+existqty+"&orderid="+orderid+"&varientid="+varientid+"&auid="+auid+"&status="+status+"&isgroup="+isgroup+"&csrf_test_name="+$("#csrfhashresarvation").val(),myurl=basicinfo.baseurl+"ordermanage/order/itemqtyupdate";$.ajax({type:"POST",url:myurl,data:dataString,success:function(data){$("#updatefoodlist").html(data);$("#grtotal").val();var totalitem=$("#totalitem").val();$("#item-number").text(totalitem),$("#getitemp").val(totalitem);var tax=$("#tvat").val(),discount=$("#tdiscount").val(),tgtotal=$("#tgtotal").val();$("#calvat").text(tax),$("#invoice_discount_update").val(discount);var sc=$("#sc").val();$("#service_charge_update").val(sc),1==basicinfo.isvatinclusive?$("#gtotal_update").text(tgtotal-tax):$("#gtotal_update").text(tgtotal),$("#grandtotal").val(tgtotal),$("#orggrandTotal").val(tgtotal),$("#orginattotal").val(tgtotal)}})}function itemnote(rowid,notes,qty,isupdate,isgroup=null){$("#foodnote").val(notes),$("#foodqty").val(qty),$("#foodcartid").val(rowid),$("#foodgroup").val(isgroup),1==isupdate?($("#notesmbt").text("Update Note"),$("#notesmbt").attr("onclick","addnotetoupdate()")):($("#notesmbt").text("Update Note"),$("#notesmbt").attr("onclick","addnotetoitem()")),$("#vieworder").modal("show")}function addnotetoupdate(){var csrf=$("#csrfhashresarvation").val(),rowid=$("#foodcartid").val(),note=$("#foodnote").val(),orderid=$("#foodqty").val(),group=$("#foodgroup").val(),geturl=basicinfo.baseurl+"ordermanage/order/addnotetoupdate",dataString="foodnote="+note+"&rowid="+rowid+"&orderid="+orderid+"&group="+group+"&csrf_test_name="+csrf;$.ajax({type:"POST",url:geturl,data:dataString,success:function(data){setTimeout((function(){toastr.options={closeButton:!0,progressBar:!0,showMethod:"slideDown",timeOut:4e3},toastr.success("Note Added Successfully","Success"),$("#updatefoodlist").html(data),$("#vieworder").modal("hide")}),100)}})}$(window).load((function(){$(".sidebar-mini").addClass("sidebar-collapse")})),$(document).ready((function(){"use strict";$("select.form-control:not(.dont-select-me)").select2({placeholder:lang.sl_option,allowClear:!0}),$("#validate").validate(),$("#add_category").validate(),$("#customer_name").validate(),$(".productclist").slimScroll({size:"3px",height:"345px",allowPageScroll:!0,railVisible:!0}),$(".product-grid").slimScroll({size:"3px",height:"720px",allowPageScroll:!0,railVisible:!0}),$(".update_search-field").select2({placeholder:"Select Product",minimumInputLength:1,ajax:{url:basicinfo.baseurl+"ordermanage/order/getitemlistdroup",dataType:"json",delay:250,processResults:function(data){return{results:$.map(data,(function(item){return{text:item.text+"-"+item.variantName,id:item.id+"-"+item.variantid}}))}},cache:!0}})})),$(document).on("change","#update_product_name",(function(){var idvid=$(this).children("option:selected").val().split("-"),id=idvid[0],vid=idvid[1],csrf=$("#csrfhashresarvation").val(),updateid=$("#saleinvoice").val(),url=basicinfo.baseurl+"ordermanage/order/addtocartupdate_uniqe/"+id+"/"+updateid;if(1==$("#production_setting").val()){if(0==checkproduction(id,vid,1))return $("#update_product_name").html(""),!1}$.ajax({type:"GET",url:url,data:{csrf_test_name:csrf},success:function(data){var myurl=basicinfo.baseurl+"ordermanage/order/adonsproductadd/"+id;$.ajax({type:"GET",url:myurl,data:{csrf_test_name:csrf},success:function(data){$(".addonsinfo").html(data),$("#edit").modal("show");var tax=$("#tvat").val(),discount=$("#tdiscount").val(),tgtotal=$("#tgtotal").val();$("#vat").val(tax),$("#calvat").text(tax);var sc=$("#sc").val();$("#service_charge").val(sc),$("#invoice_discount").val(discount),1==basicinfo.isvatinclusive?$("#caltotal").text(tgtotal-tax):$("#caltotal").text(tgtotal),$("#grandtotal_update").val(tgtotal),$("#orggrandTotal").val(tgtotal),$("#orginattotal_update").val(tgtotal),$("#update_product_name").html("")}})}})})),$("body").on("click","#search_button",(function(){var product_name=$("#update_product_name").val(),category_id=$("#category_id").val(),myurl=$("#posurl_update").val(),csrf=$("#csrfhashresarvation").val();$.ajax({type:"post",async:!1,url:myurl,data:{product_name:product_name,category_id:category_id,csrf_test_name:csrf},success:function(data){"420"==data?$("#product_search_update").html("Product not found !"):$("#product_search_update").html(data)},error:function(){alert(lang.req_failed)}})})),$("body").on("click",".update_select_product",(function(e){e.preventDefault();var panel=$(this),pid=panel.find(".panel-body input[name=update_select_product_id]").val(),sizeid=panel.find(".panel-body input[name=update_select_product_size]").val(),totalvarient=panel.find(".panel-body input[name=update_select_totalvarient]").val(),customqty=panel.find(".panel-body input[name=update_select_iscustomeqty]").val(),isgroup=panel.find(".panel-body input[name=update_select_product_isgroup]").val(),catid=panel.find(".panel-body input[name=update_select_product_cat]").val(),itemname=panel.find(".panel-body input[name=update_select_product_name]").val(),varientname=panel.find(".panel-body input[name=update_select_varient_name]").val(),qty=1,price=panel.find(".panel-body input[name=update_select_product_price]").val(),hasaddons=panel.find(".panel-body input[name=update_select_addons]").val(),existqty=$("#select_qty_"+pid+"_"+sizeid).val(),csrf=$("#csrfhashresarvation").val();if(void 0===existqty)existqty=0;else existqty=$("#select_qty_"+pid+"_"+sizeid).val();qty=parseInt(existqty)+parseInt(qty);var updateid=$("#saleinvoice").val();if(0==hasaddons&&1==totalvarient&&0==customqty){if(1==$("#production_setting").val()){if(1==$("#productionsetting-update-"+pid+"-"+sizeid).length)var checkqty=parseInt($("#productionsetting-update-"+pid+"-"+sizeid).text())+qty;else checkqty=qty;if(0==checkproduction(pid,sizeid,checkqty))return!1}var mysound=baseurl+"assets/";new Audio(mysound+"beep-08b.mp3").play();var myurl=$("#updatecarturl").val(),dataString="pid="+pid+"&itemname="+itemname+"&varientname="+varientname+"&qty="+qty+"&price="+price+"&catid="+catid+"&sizeid="+sizeid+"&isgroup="+isgroup+"&orderid="+updateid+"&totalvarient="+totalvarient+"&customqty="+customqty+"&csrf_test_name="+csrf;$.ajax({type:"POST",url:myurl,data:dataString,success:function(data){console.log("---------------",data),$("#updatefoodlist").html(data);$("#grtotal").val();var totalitem=$("#totalitem").val();$("#item-number").text(totalitem),$("#getitemp").val(totalitem);var tax=$("#tvat").val(),discount=$("#tdiscount").val(),tgtotal=$("#tgtotal").val();$("#calvat").text(tax),$("#invoice_discount").val(discount);var sc=$("#sc").val();$("#service_charge").val(sc),1==basicinfo.isvatinclusive?$("#caltotal").text(tgtotal-tax):$("#caltotal").text(tgtotal),$("#grandtotal").val(tgtotal),$("#orggrandTotal").val(tgtotal),$("#orginattotal").val(tgtotal)}})}else{var geturl=$("#addonexsurl").val();myurl=geturl+"/"+pid,dataString="pid="+pid+"&sid="+sizeid+"&id="+catid+"&totalvarient="+totalvarient+"&customqty="+customqty+"&csrf_test_name="+csrf;$.ajax({type:"POST",url:geturl,data:dataString,success:function(data){$(".addonsinfo").html(data),$("#edit").modal("show");var tax=$("#tvat").val(),discount=$("#tdiscount").val(),tgtotal=$("#tgtotal").val();$("#vat").val(tax),$("#calvat").text(tax),$("#invoice_discount").val(discount),1==basicinfo.isvatinclusive?$("#caltotal").text(tgtotal-tax):$("#caltotal").text(tgtotal),$("#grandtotal").val(tgtotal),$("#orggrandTotal").val(tgtotal),$("#orginattotal").val(tgtotal)}})}})),$(document).ready((function(){orderinfo.isthirdparty>0?($("#nonthirdparty_update").hide(),$("#thirdparty_update").show(),$("#delivercom_update").prop("disabled",!1),$("#waiter_update").prop("disabled",!0),$("#tableid_update").prop("disabled",!0),$("#cardarea_update").show()):4==orderinfo.cutomertype||2==orderinfo.cutomertype?($("#nonthirdparty_update").show(),$("#thirdparty_update").hide(),$("#tblsec_update").hide(),$("#delivercom_update").prop("disabled",!0),$("#waiter_update").prop("disabled",!1),$("#tableid_update").prop("disabled",!0),$("#cardarea_update").hide()):($("#nonthirdparty_update").show(),$("#tblsec_update").show(),$("#thirdparty_update").hide(),$("#delivercom_update").prop("disabled",!0),$("#waiter_update").prop("disabled",!1),$("#tableid_update").prop("disabled",!1),$("#cardarea_update").hide()),$(".payment_button").click((function(){$(".payment_method").toggle(),$("select.form-control:not(.dont-select-me)").select2({placeholder:lang.sl_option,allowClear:!0})})),$("#card_typesl").on("change",(function(){var cardtype=$("#card_typesl").val();$("#card_type").val(cardtype),4==cardtype?($("#isonline").val(0),$("#cardarea").hide(),$("#assigncard_terminal").val(""),$("#assignbank").val(""),$("#assignlastdigit").val("")):1==cardtype?($("#isonline").val(0),$("#cardarea").show()):($("#isonline").val(1),$("#cardarea").hide(),$("#assigncard_terminal").val(""),$("#assignbank").val(""),$("#assignlastdigit").val(""))})),$("#ctypeid_update").on("change",(function(){var customertype=$("#ctypeid_update").val();3==customertype?($("#delivercom_update").prop("disabled",!1),$("#waiter_update").prop("disabled",!0),$("#tableid_update").prop("disabled",!0),$("#nonthirdparty_update").hide(),$("#thirdparty_update").show()):4==customertype?($("#nonthirdparty_update").show(),$("#thirdparty_update").hide(),$("#tblsec_update").hide(),$("#delivercom_update").prop("disabled",!0),$("#waiter_update").prop("disabled",!1),$("#tableid_update").prop("disabled",!0)):2==customertype?($("#nonthirdparty_update").show(),$("#tblsec_update").hide(),$("#thirdparty_update").hide(),$("#waiter_update").prop("disabled",!1),$("#tableid_update").prop("disabled",!1),$("#cookingtime_update").prop("disabled",!1),$("#delivercom_update").prop("disabled",!0)):($("#nonthirdparty_update").show(),$("#tblsec_update").show(),$("#thirdparty_update").hide(),$("#delivercom_update").prop("disabled",!0),$("#waiter_update").prop("disabled",!1),$("#tableid_update").prop("disabled",!1))}))}));