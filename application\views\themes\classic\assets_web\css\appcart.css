
.t-radio input[type="radio"] {
    display: none;
}

.t-radio label {
    cursor: pointer;
    position: relative;
    font-size: 14px;
    font-weight: 600;
    padding-left: 22px;
    margin-bottom: 0;
}

.t-radio label::before {
    content: "";
    position: absolute;
    width: 20px;
    height: 20px;
    background-color: transparent;
    border: 2px solid #04be51;
    border-radius: 50%;
    top: 46%;
    left: -3px;
    transform: translateY(-50%);
    transition: border-color 400ms ease;
}

.t-radio label::after {
    content: "";
    position: absolute;
    width: 20px;
    height: 20px;
    background-color: #04be51;
    border: 2px solid #04be51;
    border-radius: 50%;
    top: 46%;
    left: -3px;
    transform: translateY(-50%) scale(0);
    transition: transform 400ms ease;
}

.t-radio input[type="radio"]:checked+label::before {
    border-color: #04be51;
}

.t-radio input[type="radio"]:checked+label::after {
    transform: translateY(-50%) scale(0.55);
}


.appcart_dispaly{
    display:flex; align-items: center;
}
.app_cart_redius{
	border-radius: 0;
}
.appcart_pointer{
   cursor: pointer;
}


.app_cart_btn_bg{
	background:#686868;color:#fff;border-radius: 0;
}
.app_cart_h3{
font-size: 13px; margin-bottom: 0;
}

.appcart_order_check{
	padding-bottom: 65px !important;
}
.appcart_order_padding{
	padding:0 !important;
}
.appcart_b{
 border:none; height:50px;
}
