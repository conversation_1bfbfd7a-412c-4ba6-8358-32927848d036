@charset "utf-8";
.tabsection {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    position: fixed;
    z-index: 999;
    background: #fff;
    left: 250px;
    right: 0;
    padding: 20px 32px 0;
    top: 0;
transition: transform .3s;
}
.sidebar-collapse .tabsection{
left: 50px;
}


.tab-content-xs {
    top: 60px;
    position: relative;
    padding: 0 0 40px
}

.tgbar {
    min-width: 150px;
    display: flex;
    align-items: center;
    justify-content: flex-end
}

.tgbar a {
    padding: 0 5px;
    position: relative
}

.tgbar>a>i {
    border: 1px solid #f2f2f2;
    padding: 6px 3px;
    width: 36px;
    text-align: center;
    color: #374767;
    background-color: #f5f5f5;
    height: 36px;
    font-size: 25px
}

.tgbar .sidebar-toggle {
    float: left;
    background-color: #f5f5f5;
    background-image: none;
    padding: 6px 6px 3px;
    font-family: fontAwesome;
    color: #374767;
    font-size: 26px;
    line-height: 26px
}

.popover {
    min-width: 50em !important
}

.wrapper.pos {
    height: 100vh
}

@keyframes anim_opa {
    50% {
        opacity: .2
    }
}

@media(max-width:767px) {
    .tabsection {
        left: 0;
        padding: 20px 17px 0
    }
}

@media(max-width:575px) {
    .tabsection {
        left: 0;
        padding: 20px 17px 0
    }
    .tab-content-xs {
        top: 60px
    }
}

.lang_box {
    line-height: 36px;
    color: #374767
}

.lang_options {
    min-width: 90px
}

.dropdown-menu.lang_options {
    position: absolute;
    right: 0;
    left: auto
}

listcat.listcat {
    color: #fff;
    background: #37a000;
    box-shadow: inset 0 0 0 0 rgba(0, 0, 0, .4), -2px -3px 5px -2px rgba(0, 0, 0, .4);
    cursor: pointer;
    padding: 5px
}

