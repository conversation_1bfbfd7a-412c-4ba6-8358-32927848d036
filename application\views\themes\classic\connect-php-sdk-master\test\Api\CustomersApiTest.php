<?php
/**
 * NOTE: This class is auto generated by the swagger code generator program. 
 * https://github.com/swagger-api/swagger-codegen 
 * Do not edit the class manually.
 */

namespace SquareConnect\Api;

use \SquareConnect\Configuration;
use \SquareConnect\ApiClient;
use \SquareConnect\ApiException;
use \SquareConnect\ObjectSerializer;

/**
 * CustomersApiTest Class Doc Comment
 *
 * @category Class
 * @package  SquareConnect
 * <AUTHOR> Inc.
 * @license  http://www.apache.org/licenses/LICENSE-2.0 Apache Licene v2
 * @link     https://squareup.com/developers
 */
class CustomersApiTest extends \PHPUnit_Framework_TestCase
{

    /**
     * Setup before running each test case
     */
    public static function setUpBeforeClass() {

    }

    /**
     * Clean up after running each test case
     */
    public static function tearDownAfterClass() {

    }

    /**
     * Test case for createCustomer
     *
     * CreateCustomer
     *
     */
    public function test_createCustomer() {

    }
    /**
     * Test case for createCustomerCard
     *
     * CreateCustomerCard
     *
     */
    public function test_createCustomerCard() {

    }
    /**
     * Test case for deleteCustomer
     *
     * DeleteCustomer
     *
     */
    public function test_deleteCustomer() {

    }
    /**
     * Test case for deleteCustomerCard
     *
     * DeleteCustomerCard
     *
     */
    public function test_deleteCustomerCard() {

    }
    /**
     * Test case for listCustomers
     *
     * ListCustomers
     *
     */
    public function test_listCustomers() {

    }
    /**
     * Test case for retrieveCustomer
     *
     * RetrieveCustomer
     *
     */
    public function test_retrieveCustomer() {

    }
    /**
     * Test case for updateCustomer
     *
     * UpdateCustomer
     *
     */
    public function test_updateCustomer() {

    }
}
