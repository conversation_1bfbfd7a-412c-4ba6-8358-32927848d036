<?php
/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace SquareConnect\Model;

use \ArrayAccess;
/**
 * V1Order Class Doc Comment
 *
 * @category Class
 * @package  SquareConnect
 * <AUTHOR> Inc.
 * @license  http://www.apache.org/licenses/LICENSE-2.0 Apache License v2
 * @link     https://squareup.com/developers
 */
class V1Order implements ArrayAccess
{
    /**
      * Array of property to type mappings. Used for (de)serialization 
      * @var string[]
      */
    static $swaggerTypes = array(
        'errors' => '\SquareConnect\Model\Error[]',
        'id' => 'string',
        'buyer_email' => 'string',
        'recipient_name' => 'string',
        'recipient_phone_number' => 'string',
        'state' => 'string',
        'shipping_address' => '\SquareConnect\Model\Address',
        'subtotal_money' => '\SquareConnect\Model\V1Money',
        'total_shipping_money' => '\SquareConnect\Model\V1Money',
        'total_tax_money' => '\SquareConnect\Model\V1Money',
        'total_price_money' => '\SquareConnect\Model\V1Money',
        'total_discount_money' => '\SquareConnect\Model\V1Money',
        'created_at' => 'string',
        'updated_at' => 'string',
        'expires_at' => 'string',
        'payment_id' => 'string',
        'buyer_note' => 'string',
        'completed_note' => 'string',
        'refunded_note' => 'string',
        'canceled_note' => 'string',
        'tender' => '\SquareConnect\Model\V1Tender',
        'order_history' => '\SquareConnect\Model\V1OrderHistoryEntry[]',
        'promo_code' => 'string',
        'btc_receive_address' => 'string',
        'btc_price_satoshi' => 'float'
    );
  
    /** 
      * Array of attributes where the key is the local name, and the value is the original name
      * @var string[] 
      */
    static $attributeMap = array(
        'errors' => 'errors',
        'id' => 'id',
        'buyer_email' => 'buyer_email',
        'recipient_name' => 'recipient_name',
        'recipient_phone_number' => 'recipient_phone_number',
        'state' => 'state',
        'shipping_address' => 'shipping_address',
        'subtotal_money' => 'subtotal_money',
        'total_shipping_money' => 'total_shipping_money',
        'total_tax_money' => 'total_tax_money',
        'total_price_money' => 'total_price_money',
        'total_discount_money' => 'total_discount_money',
        'created_at' => 'created_at',
        'updated_at' => 'updated_at',
        'expires_at' => 'expires_at',
        'payment_id' => 'payment_id',
        'buyer_note' => 'buyer_note',
        'completed_note' => 'completed_note',
        'refunded_note' => 'refunded_note',
        'canceled_note' => 'canceled_note',
        'tender' => 'tender',
        'order_history' => 'order_history',
        'promo_code' => 'promo_code',
        'btc_receive_address' => 'btc_receive_address',
        'btc_price_satoshi' => 'btc_price_satoshi'
    );
  
    /**
      * Array of attributes to setter functions (for deserialization of responses)
      * @var string[]
      */
    static $setters = array(
        'errors' => 'setErrors',
        'id' => 'setId',
        'buyer_email' => 'setBuyerEmail',
        'recipient_name' => 'setRecipientName',
        'recipient_phone_number' => 'setRecipientPhoneNumber',
        'state' => 'setState',
        'shipping_address' => 'setShippingAddress',
        'subtotal_money' => 'setSubtotalMoney',
        'total_shipping_money' => 'setTotalShippingMoney',
        'total_tax_money' => 'setTotalTaxMoney',
        'total_price_money' => 'setTotalPriceMoney',
        'total_discount_money' => 'setTotalDiscountMoney',
        'created_at' => 'setCreatedAt',
        'updated_at' => 'setUpdatedAt',
        'expires_at' => 'setExpiresAt',
        'payment_id' => 'setPaymentId',
        'buyer_note' => 'setBuyerNote',
        'completed_note' => 'setCompletedNote',
        'refunded_note' => 'setRefundedNote',
        'canceled_note' => 'setCanceledNote',
        'tender' => 'setTender',
        'order_history' => 'setOrderHistory',
        'promo_code' => 'setPromoCode',
        'btc_receive_address' => 'setBtcReceiveAddress',
        'btc_price_satoshi' => 'setBtcPriceSatoshi'
    );
  
    /**
      * Array of attributes to getter functions (for serialization of requests)
      * @var string[]
      */
    static $getters = array(
        'errors' => 'getErrors',
        'id' => 'getId',
        'buyer_email' => 'getBuyerEmail',
        'recipient_name' => 'getRecipientName',
        'recipient_phone_number' => 'getRecipientPhoneNumber',
        'state' => 'getState',
        'shipping_address' => 'getShippingAddress',
        'subtotal_money' => 'getSubtotalMoney',
        'total_shipping_money' => 'getTotalShippingMoney',
        'total_tax_money' => 'getTotalTaxMoney',
        'total_price_money' => 'getTotalPriceMoney',
        'total_discount_money' => 'getTotalDiscountMoney',
        'created_at' => 'getCreatedAt',
        'updated_at' => 'getUpdatedAt',
        'expires_at' => 'getExpiresAt',
        'payment_id' => 'getPaymentId',
        'buyer_note' => 'getBuyerNote',
        'completed_note' => 'getCompletedNote',
        'refunded_note' => 'getRefundedNote',
        'canceled_note' => 'getCanceledNote',
        'tender' => 'getTender',
        'order_history' => 'getOrderHistory',
        'promo_code' => 'getPromoCode',
        'btc_receive_address' => 'getBtcReceiveAddress',
        'btc_price_satoshi' => 'getBtcPriceSatoshi'
    );
  
    /**
      * $errors Any errors that occurred during the request.
      * @var \SquareConnect\Model\Error[]
      */
    protected $errors;
    /**
      * $id The order's unique identifier.
      * @var string
      */
    protected $id;
    /**
      * $buyer_email The email address of the order's buyer.
      * @var string
      */
    protected $buyer_email;
    /**
      * $recipient_name The name of the order's buyer.
      * @var string
      */
    protected $recipient_name;
    /**
      * $recipient_phone_number The phone number to use for the order's delivery.
      * @var string
      */
    protected $recipient_phone_number;
    /**
      * $state Whether the tax is an ADDITIVE tax or an INCLUSIVE tax.
      * @var string
      */
    protected $state;
    /**
      * $shipping_address The address to ship the order to.
      * @var \SquareConnect\Model\Address
      */
    protected $shipping_address;
    /**
      * $subtotal_money The amount of all items purchased in the order, before taxes and shipping.
      * @var \SquareConnect\Model\V1Money
      */
    protected $subtotal_money;
    /**
      * $total_shipping_money The shipping cost for the order.
      * @var \SquareConnect\Model\V1Money
      */
    protected $total_shipping_money;
    /**
      * $total_tax_money The total of all taxes applied to the order.
      * @var \SquareConnect\Model\V1Money
      */
    protected $total_tax_money;
    /**
      * $total_price_money The total cost of the order.
      * @var \SquareConnect\Model\V1Money
      */
    protected $total_price_money;
    /**
      * $total_discount_money The total of all discounts applied to the order.
      * @var \SquareConnect\Model\V1Money
      */
    protected $total_discount_money;
    /**
      * $created_at The time when the order was created, in ISO 8601 format.
      * @var string
      */
    protected $created_at;
    /**
      * $updated_at The time when the order was last modified, in ISO 8601 format.
      * @var string
      */
    protected $updated_at;
    /**
      * $expires_at The time when the order expires if no action is taken, in ISO 8601 format.
      * @var string
      */
    protected $expires_at;
    /**
      * $payment_id The unique identifier of the payment associated with the order.
      * @var string
      */
    protected $payment_id;
    /**
      * $buyer_note A note provided by the buyer when the order was created, if any.
      * @var string
      */
    protected $buyer_note;
    /**
      * $completed_note A note provided by the merchant when the order's state was set to COMPLETED, if any
      * @var string
      */
    protected $completed_note;
    /**
      * $refunded_note A note provided by the merchant when the order's state was set to REFUNDED, if any.
      * @var string
      */
    protected $refunded_note;
    /**
      * $canceled_note A note provided by the merchant when the order's state was set to CANCELED, if any.
      * @var string
      */
    protected $canceled_note;
    /**
      * $tender The tender used to pay for the order.
      * @var \SquareConnect\Model\V1Tender
      */
    protected $tender;
    /**
      * $order_history The history of actions associated with the order.
      * @var \SquareConnect\Model\V1OrderHistoryEntry[]
      */
    protected $order_history;
    /**
      * $promo_code The promo code provided by the buyer, if any.
      * @var string
      */
    protected $promo_code;
    /**
      * $btc_receive_address For Bitcoin transactions, the address that the buyer sent Bitcoin to.
      * @var string
      */
    protected $btc_receive_address;
    /**
      * $btc_price_satoshi For Bitcoin transactions, the price of the buyer's order in satoshi (100 million satoshi equals 1 BTC).
      * @var float
      */
    protected $btc_price_satoshi;

    /**
     * Constructor
     * @param mixed[] $data Associated array of property value initializing the model
     */
    public function __construct(array $data = null)
    {
        if ($data != null) {
            if (isset($data["errors"])) {
              $this->errors = $data["errors"];
            } else {
              $this->errors = null;
            }
            if (isset($data["id"])) {
              $this->id = $data["id"];
            } else {
              $this->id = null;
            }
            if (isset($data["buyer_email"])) {
              $this->buyer_email = $data["buyer_email"];
            } else {
              $this->buyer_email = null;
            }
            if (isset($data["recipient_name"])) {
              $this->recipient_name = $data["recipient_name"];
            } else {
              $this->recipient_name = null;
            }
            if (isset($data["recipient_phone_number"])) {
              $this->recipient_phone_number = $data["recipient_phone_number"];
            } else {
              $this->recipient_phone_number = null;
            }
            if (isset($data["state"])) {
              $this->state = $data["state"];
            } else {
              $this->state = null;
            }
            if (isset($data["shipping_address"])) {
              $this->shipping_address = $data["shipping_address"];
            } else {
              $this->shipping_address = null;
            }
            if (isset($data["subtotal_money"])) {
              $this->subtotal_money = $data["subtotal_money"];
            } else {
              $this->subtotal_money = null;
            }
            if (isset($data["total_shipping_money"])) {
              $this->total_shipping_money = $data["total_shipping_money"];
            } else {
              $this->total_shipping_money = null;
            }
            if (isset($data["total_tax_money"])) {
              $this->total_tax_money = $data["total_tax_money"];
            } else {
              $this->total_tax_money = null;
            }
            if (isset($data["total_price_money"])) {
              $this->total_price_money = $data["total_price_money"];
            } else {
              $this->total_price_money = null;
            }
            if (isset($data["total_discount_money"])) {
              $this->total_discount_money = $data["total_discount_money"];
            } else {
              $this->total_discount_money = null;
            }
            if (isset($data["created_at"])) {
              $this->created_at = $data["created_at"];
            } else {
              $this->created_at = null;
            }
            if (isset($data["updated_at"])) {
              $this->updated_at = $data["updated_at"];
            } else {
              $this->updated_at = null;
            }
            if (isset($data["expires_at"])) {
              $this->expires_at = $data["expires_at"];
            } else {
              $this->expires_at = null;
            }
            if (isset($data["payment_id"])) {
              $this->payment_id = $data["payment_id"];
            } else {
              $this->payment_id = null;
            }
            if (isset($data["buyer_note"])) {
              $this->buyer_note = $data["buyer_note"];
            } else {
              $this->buyer_note = null;
            }
            if (isset($data["completed_note"])) {
              $this->completed_note = $data["completed_note"];
            } else {
              $this->completed_note = null;
            }
            if (isset($data["refunded_note"])) {
              $this->refunded_note = $data["refunded_note"];
            } else {
              $this->refunded_note = null;
            }
            if (isset($data["canceled_note"])) {
              $this->canceled_note = $data["canceled_note"];
            } else {
              $this->canceled_note = null;
            }
            if (isset($data["tender"])) {
              $this->tender = $data["tender"];
            } else {
              $this->tender = null;
            }
            if (isset($data["order_history"])) {
              $this->order_history = $data["order_history"];
            } else {
              $this->order_history = null;
            }
            if (isset($data["promo_code"])) {
              $this->promo_code = $data["promo_code"];
            } else {
              $this->promo_code = null;
            }
            if (isset($data["btc_receive_address"])) {
              $this->btc_receive_address = $data["btc_receive_address"];
            } else {
              $this->btc_receive_address = null;
            }
            if (isset($data["btc_price_satoshi"])) {
              $this->btc_price_satoshi = $data["btc_price_satoshi"];
            } else {
              $this->btc_price_satoshi = null;
            }
        }
    }
    /**
     * Gets errors
     * @return \SquareConnect\Model\Error[]
     */
    public function getErrors()
    {
        return $this->errors;
    }
  
    /**
     * Sets errors
     * @param \SquareConnect\Model\Error[] $errors Any errors that occurred during the request.
     * @return $this
     */
    public function setErrors($errors)
    {
        $this->errors = $errors;
        return $this;
    }
    /**
     * Gets id
     * @return string
     */
    public function getId()
    {
        return $this->id;
    }
  
    /**
     * Sets id
     * @param string $id The order's unique identifier.
     * @return $this
     */
    public function setId($id)
    {
        $this->id = $id;
        return $this;
    }
    /**
     * Gets buyer_email
     * @return string
     */
    public function getBuyerEmail()
    {
        return $this->buyer_email;
    }
  
    /**
     * Sets buyer_email
     * @param string $buyer_email The email address of the order's buyer.
     * @return $this
     */
    public function setBuyerEmail($buyer_email)
    {
        $this->buyer_email = $buyer_email;
        return $this;
    }
    /**
     * Gets recipient_name
     * @return string
     */
    public function getRecipientName()
    {
        return $this->recipient_name;
    }
  
    /**
     * Sets recipient_name
     * @param string $recipient_name The name of the order's buyer.
     * @return $this
     */
    public function setRecipientName($recipient_name)
    {
        $this->recipient_name = $recipient_name;
        return $this;
    }
    /**
     * Gets recipient_phone_number
     * @return string
     */
    public function getRecipientPhoneNumber()
    {
        return $this->recipient_phone_number;
    }
  
    /**
     * Sets recipient_phone_number
     * @param string $recipient_phone_number The phone number to use for the order's delivery.
     * @return $this
     */
    public function setRecipientPhoneNumber($recipient_phone_number)
    {
        $this->recipient_phone_number = $recipient_phone_number;
        return $this;
    }
    /**
     * Gets state
     * @return string
     */
    public function getState()
    {
        return $this->state;
    }
  
    /**
     * Sets state
     * @param string $state Whether the tax is an ADDITIVE tax or an INCLUSIVE tax.
     * @return $this
     */
    public function setState($state)
    {
        $this->state = $state;
        return $this;
    }
    /**
     * Gets shipping_address
     * @return \SquareConnect\Model\Address
     */
    public function getShippingAddress()
    {
        return $this->shipping_address;
    }
  
    /**
     * Sets shipping_address
     * @param \SquareConnect\Model\Address $shipping_address The address to ship the order to.
     * @return $this
     */
    public function setShippingAddress($shipping_address)
    {
        $this->shipping_address = $shipping_address;
        return $this;
    }
    /**
     * Gets subtotal_money
     * @return \SquareConnect\Model\V1Money
     */
    public function getSubtotalMoney()
    {
        return $this->subtotal_money;
    }
  
    /**
     * Sets subtotal_money
     * @param \SquareConnect\Model\V1Money $subtotal_money The amount of all items purchased in the order, before taxes and shipping.
     * @return $this
     */
    public function setSubtotalMoney($subtotal_money)
    {
        $this->subtotal_money = $subtotal_money;
        return $this;
    }
    /**
     * Gets total_shipping_money
     * @return \SquareConnect\Model\V1Money
     */
    public function getTotalShippingMoney()
    {
        return $this->total_shipping_money;
    }
  
    /**
     * Sets total_shipping_money
     * @param \SquareConnect\Model\V1Money $total_shipping_money The shipping cost for the order.
     * @return $this
     */
    public function setTotalShippingMoney($total_shipping_money)
    {
        $this->total_shipping_money = $total_shipping_money;
        return $this;
    }
    /**
     * Gets total_tax_money
     * @return \SquareConnect\Model\V1Money
     */
    public function getTotalTaxMoney()
    {
        return $this->total_tax_money;
    }
  
    /**
     * Sets total_tax_money
     * @param \SquareConnect\Model\V1Money $total_tax_money The total of all taxes applied to the order.
     * @return $this
     */
    public function setTotalTaxMoney($total_tax_money)
    {
        $this->total_tax_money = $total_tax_money;
        return $this;
    }
    /**
     * Gets total_price_money
     * @return \SquareConnect\Model\V1Money
     */
    public function getTotalPriceMoney()
    {
        return $this->total_price_money;
    }
  
    /**
     * Sets total_price_money
     * @param \SquareConnect\Model\V1Money $total_price_money The total cost of the order.
     * @return $this
     */
    public function setTotalPriceMoney($total_price_money)
    {
        $this->total_price_money = $total_price_money;
        return $this;
    }
    /**
     * Gets total_discount_money
     * @return \SquareConnect\Model\V1Money
     */
    public function getTotalDiscountMoney()
    {
        return $this->total_discount_money;
    }
  
    /**
     * Sets total_discount_money
     * @param \SquareConnect\Model\V1Money $total_discount_money The total of all discounts applied to the order.
     * @return $this
     */
    public function setTotalDiscountMoney($total_discount_money)
    {
        $this->total_discount_money = $total_discount_money;
        return $this;
    }
    /**
     * Gets created_at
     * @return string
     */
    public function getCreatedAt()
    {
        return $this->created_at;
    }
  
    /**
     * Sets created_at
     * @param string $created_at The time when the order was created, in ISO 8601 format.
     * @return $this
     */
    public function setCreatedAt($created_at)
    {
        $this->created_at = $created_at;
        return $this;
    }
    /**
     * Gets updated_at
     * @return string
     */
    public function getUpdatedAt()
    {
        return $this->updated_at;
    }
  
    /**
     * Sets updated_at
     * @param string $updated_at The time when the order was last modified, in ISO 8601 format.
     * @return $this
     */
    public function setUpdatedAt($updated_at)
    {
        $this->updated_at = $updated_at;
        return $this;
    }
    /**
     * Gets expires_at
     * @return string
     */
    public function getExpiresAt()
    {
        return $this->expires_at;
    }
  
    /**
     * Sets expires_at
     * @param string $expires_at The time when the order expires if no action is taken, in ISO 8601 format.
     * @return $this
     */
    public function setExpiresAt($expires_at)
    {
        $this->expires_at = $expires_at;
        return $this;
    }
    /**
     * Gets payment_id
     * @return string
     */
    public function getPaymentId()
    {
        return $this->payment_id;
    }
  
    /**
     * Sets payment_id
     * @param string $payment_id The unique identifier of the payment associated with the order.
     * @return $this
     */
    public function setPaymentId($payment_id)
    {
        $this->payment_id = $payment_id;
        return $this;
    }
    /**
     * Gets buyer_note
     * @return string
     */
    public function getBuyerNote()
    {
        return $this->buyer_note;
    }
  
    /**
     * Sets buyer_note
     * @param string $buyer_note A note provided by the buyer when the order was created, if any.
     * @return $this
     */
    public function setBuyerNote($buyer_note)
    {
        $this->buyer_note = $buyer_note;
        return $this;
    }
    /**
     * Gets completed_note
     * @return string
     */
    public function getCompletedNote()
    {
        return $this->completed_note;
    }
  
    /**
     * Sets completed_note
     * @param string $completed_note A note provided by the merchant when the order's state was set to COMPLETED, if any
     * @return $this
     */
    public function setCompletedNote($completed_note)
    {
        $this->completed_note = $completed_note;
        return $this;
    }
    /**
     * Gets refunded_note
     * @return string
     */
    public function getRefundedNote()
    {
        return $this->refunded_note;
    }
  
    /**
     * Sets refunded_note
     * @param string $refunded_note A note provided by the merchant when the order's state was set to REFUNDED, if any.
     * @return $this
     */
    public function setRefundedNote($refunded_note)
    {
        $this->refunded_note = $refunded_note;
        return $this;
    }
    /**
     * Gets canceled_note
     * @return string
     */
    public function getCanceledNote()
    {
        return $this->canceled_note;
    }
  
    /**
     * Sets canceled_note
     * @param string $canceled_note A note provided by the merchant when the order's state was set to CANCELED, if any.
     * @return $this
     */
    public function setCanceledNote($canceled_note)
    {
        $this->canceled_note = $canceled_note;
        return $this;
    }
    /**
     * Gets tender
     * @return \SquareConnect\Model\V1Tender
     */
    public function getTender()
    {
        return $this->tender;
    }
  
    /**
     * Sets tender
     * @param \SquareConnect\Model\V1Tender $tender The tender used to pay for the order.
     * @return $this
     */
    public function setTender($tender)
    {
        $this->tender = $tender;
        return $this;
    }
    /**
     * Gets order_history
     * @return \SquareConnect\Model\V1OrderHistoryEntry[]
     */
    public function getOrderHistory()
    {
        return $this->order_history;
    }
  
    /**
     * Sets order_history
     * @param \SquareConnect\Model\V1OrderHistoryEntry[] $order_history The history of actions associated with the order.
     * @return $this
     */
    public function setOrderHistory($order_history)
    {
        $this->order_history = $order_history;
        return $this;
    }
    /**
     * Gets promo_code
     * @return string
     */
    public function getPromoCode()
    {
        return $this->promo_code;
    }
  
    /**
     * Sets promo_code
     * @param string $promo_code The promo code provided by the buyer, if any.
     * @return $this
     */
    public function setPromoCode($promo_code)
    {
        $this->promo_code = $promo_code;
        return $this;
    }
    /**
     * Gets btc_receive_address
     * @return string
     */
    public function getBtcReceiveAddress()
    {
        return $this->btc_receive_address;
    }
  
    /**
     * Sets btc_receive_address
     * @param string $btc_receive_address For Bitcoin transactions, the address that the buyer sent Bitcoin to.
     * @return $this
     */
    public function setBtcReceiveAddress($btc_receive_address)
    {
        $this->btc_receive_address = $btc_receive_address;
        return $this;
    }
    /**
     * Gets btc_price_satoshi
     * @return float
     */
    public function getBtcPriceSatoshi()
    {
        return $this->btc_price_satoshi;
    }
  
    /**
     * Sets btc_price_satoshi
     * @param float $btc_price_satoshi For Bitcoin transactions, the price of the buyer's order in satoshi (100 million satoshi equals 1 BTC).
     * @return $this
     */
    public function setBtcPriceSatoshi($btc_price_satoshi)
    {
        $this->btc_price_satoshi = $btc_price_satoshi;
        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     * @param  integer $offset Offset 
     * @return boolean
     */
    public function offsetExists($offset)
    {
        return isset($this->$offset);
    }
  
    /**
     * Gets offset.
     * @param  integer $offset Offset 
     * @return mixed 
     */
    public function offsetGet($offset)
    {
        return $this->$offset;
    }
  
    /**
     * Sets value based on offset.
     * @param  integer $offset Offset 
     * @param  mixed   $value  Value to be set
     * @return void
     */
    public function offsetSet($offset, $value)
    {
        $this->$offset = $value;
    }
  
    /**
     * Unsets offset.
     * @param  integer $offset Offset 
     * @return void
     */
    public function offsetUnset($offset)
    {
        unset($this->$offset);
    }
  
    /**
     * Gets the string presentation of the object
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) {
            return json_encode(\SquareConnect\ObjectSerializer::sanitizeForSerialization($this), JSON_PRETTY_PRINT);
        } else {
            return json_encode(\SquareConnect\ObjectSerializer::sanitizeForSerialization($this));
        }
    }
}
