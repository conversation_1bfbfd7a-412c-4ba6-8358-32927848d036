function getslcategory_update(carid){var product_name=$("#update_product_name").val(),category_id=carid,myurl=$("#posurl_update").val(),csrf=$("#csrfhashresarvation").val();$.ajax({type:"post",async:!1,url:myurl,data:{product_name:product_name,category_id:category_id,isuptade:1,csrf_test_name:csrf},success:function(data){"420"==data?$("#product_search_update").html("Product not found !"):$("#product_search_update").html(data)},error:function(){alert(lang.req_failed)}})}function positemupdate(itemid,existqty,orderid,varientid,isgroup,auid,status){if(($("#uvchange").val(1),$(".maindashboard").addClass("disabled"),$("#fhome").addClass("disabled"),$("#kitchenorder").addClass("disabled"),$("#todayqrorder").addClass("disabled"),$("#todayonlieorder").addClass("disabled"),$("#todayorder").addClass("disabled"),$("#ongoingorder").addClass("disabled"),"add"==status)&&(1==$("#production_setting").val()&&0==checkproduction(itemid,varientid,existqty+1)))return!1;var dataString="itemid="+itemid+"&existqty="+existqty+"&orderid="+orderid+"&varientid="+varientid+"&auid="+auid+"&status="+status+"&isgroup="+isgroup+"&csrf_test_name="+$("#csrfhashresarvation").val(),myurl=basicinfo.baseurl+"ordermanage/order/itemqtyupdate";$.ajax({type:"POST",url:myurl,data:dataString,success:function(data){$("#updatefoodlist").html(data);$("#grtotal").val();var totalitem=$("#totalitem").val();$("#item-number").text(totalitem),$("#getitemp").val(totalitem);var tax=$("#tvat").val(),discount=$("#tdiscount").val(),tgtotal=$("#tgtotal").val();$("#calvat").text(tax),$("#invoice_discount_update").val(discount);var sc=$("#sc").val();$("#service_charge_update").val(sc),$("#caltotal").text(tgtotal),1==basicinfo.isvatinclusive?$("#gtotal_update").text(tgtotal-tax):$("#gtotal_update").text(tgtotal),$("#grandtotal_update").val(tgtotal),$("#orggrandTotal").val(tgtotal),$("#orginattotal_update").val(tgtotal)}})}$(document).ready((function(){function deleteTempCartData(){$("tr.tempCartData").length>0&&$("tr.tempCartData").each((function(){$(this).find('a[onclick*="deletecart"]').each((function(){$(this).trigger("click")}))}))}deleteTempCartData(),$(window).on("beforeunload",(function(){deleteTempCartData()}))})),$(window).load((function(){"use strict";$(".sidebar-mini").addClass("sidebar-collapse")})),$(document).ready((function(){"use strict";$("select.form-control:not(.dont-select-me)").select2({placeholder:lang.sl_option,allowClear:!0}),$("#validate").validate(),$("#add_category").validate(),$("#customer_name").validate(),$(".productclist").slimScroll({size:"3px",height:"345px",allowPageScroll:!0,railVisible:!0}),$(".product-grid").slimScroll({size:"3px",height:"720px",allowPageScroll:!0,railVisible:!0})})),$("body").on("keyup","#update_product_name",(function(){var product_name=$(this).val(),category_id=$("#category_id").val(),myurl=$("#posurl_update").val(),csrf=$("#csrfhashresarvation").val();$.ajax({type:"post",async:!1,url:myurl,data:{product_name:product_name,category_id:category_id,csrf_test_name:csrf},success:function(data){"420"==data?$("#product_search_update").html("Product not found !"):$("#product_search_update").html(data)},error:function(){alert(lang.req_failed)}})})),$("body").on("click","#search_button",(function(){var product_name=$("#update_product_name").val(),category_id=$("#category_id").val(),myurl=$("#posurl_update").val(),csrf=$("#csrfhashresarvation").val();$.ajax({type:"post",async:!1,url:myurl,data:{product_name:product_name,category_id:category_id,csrf_test_name:csrf},success:function(data){"420"==data?$("#product_search_update").html("Product not found !"):$("#product_search_update").html(data)},error:function(){alert(lang.req_failed)}})})),$(document).ready((function(){orderinfo.isthirdparty>0?($("#nonthirdparty_update").hide(),$("#thirdparty_update").show(),$("#delivercom_update").prop("disabled",!1),$("#waiter_update").prop("disabled",!0),$("#tableid_update").prop("disabled",!0),$("#cardarea_update").show()):4==orderinfo.cutomertype||2==orderinfo.cutomertype?($("#nonthirdparty_update").show(),$("#thirdparty_update").hide(),$("#tblsec_update").hide(),$("#delivercom_update").prop("disabled",!0),$("#waiter_update").prop("disabled",!1),$("#tableid_update").prop("disabled",!0),$("#cardarea_update").hide()):($("#nonthirdparty_update").show(),$("#tblsec_update").show(),$("#thirdparty_update").hide(),$("#delivercom_update").prop("disabled",!0),$("#waiter_update").prop("disabled",!1),$("#tableid_update").prop("disabled",!1),$("#cardarea_update").hide()),$(".payment_button").click((function(){$(".payment_method").toggle(),$("select.form-control:not(.dont-select-me)").select2({placeholder:lang.sl_option,allowClear:!0})})),$("#card_typesl").on("change",(function(){var cardtype=$("#card_typesl").val();$("#card_type").val(cardtype),4==cardtype?($("#isonline").val(0),$("#cardarea").hide(),$("#assigncard_terminal").val(""),$("#assignbank").val(""),$("#assignlastdigit").val("")):1==cardtype?($("#isonline").val(0),$("#cardarea").show()):($("#isonline").val(1),$("#cardarea").hide(),$("#assigncard_terminal").val(""),$("#assignbank").val(""),$("#assignlastdigit").val(""))})),$("#ctypeid_update").on("change",(function(){var customertype=$("#ctypeid_update").val();3==customertype?($("#delivercom_update").prop("disabled",!1),$("#waiter_update").prop("disabled",!0),$("#tableid_update").prop("disabled",!0),$("#nonthirdparty_update").hide(),$("#thirdparty_update").show()):4==customertype?($("#nonthirdparty_update").show(),$("#thirdparty_update").hide(),$("#tblsec_update").hide(),$("#delivercom_update").prop("disabled",!0),$("#waiter_update").prop("disabled",!1),$("#tableid_update").prop("disabled",!0)):2==customertype?($("#nonthirdparty_update").show(),$("#tblsec_update").hide(),$("#thirdparty_update").hide(),$("#waiter_update").prop("disabled",!1),$("#tableid_update").prop("disabled",!1),$("#cookingtime_update").prop("disabled",!1),$("#delivercom_update").prop("disabled",!0)):($("#nonthirdparty_update").show(),$("#tblsec_update").show(),$("#thirdparty_update").hide(),$("#delivercom_update").prop("disabled",!0),$("#waiter_update").prop("disabled",!1),$("#tableid_update").prop("disabled",!1))})),$(".update_search-field").select2({placeholder:"Select Product",minimumInputLength:1,ajax:{url:"getitemlistdroup",dataType:"json",delay:250,processResults:function(data){return{results:$.map(data,(function(item){return{text:item.text+"-"+item.variantName,id:item.id+"-"+item.variantid}}))}},cache:!0}})}));