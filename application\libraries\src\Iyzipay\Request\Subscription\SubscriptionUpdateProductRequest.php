<?php

namespace Iyzipay\Request\Subscription;

use <PERSON>yzipay\JsonBuilder;
use Iyzipay\Request;

class SubscriptionUpdateProductRequest extends Request
{
    private $name;
    private $description;
    private $productReferenceCode;

    public function getName()
    {
        return $this->name;
    }

    public function setName($name)
    {
        $this->name = $name;
    }

    public function getDescription()
    {
        return $this->description;
    }

    public function setDescription($description)
    {
        $this->description = $description;
    }

    public function getProductReferenceCode()
    {
        return $this->productReferenceCode;
    }

    public function setProductReferenceCode($productReferenceCode)
    {
        $this->productReferenceCode = $productReferenceCode;
    }


    public function getJsonObject()
    {
        return JsonBuilder::fromJsonObject(parent::getJsonObject())
            ->add("locale", $this->getLocale())
            ->add("conversationId", $this->getConversationId())
            ->add("name", $this->getName())
            ->add("description", $this->getDescription())
            ->add("productReferenceCode", $this->getProductReferenceCode())
            ->getObject();
    }
}