<?php

namespace Iyzipay\Request\Subscription;

use <PERSON>yzipay\JsonBuilder;
use Iyzipay\Request;

class SubscriptionRetrieveProductRequest extends Request
{

    private $productReferenceCode;

    public function getProductReferenceCode()
    {
        return $this->productReferenceCode;
    }

    public function setProductReferenceCode($productReferenceCode)
    {
        $this->productReferenceCode = $productReferenceCode;
    }

    public function getJsonObject()
    {
        return JsonBuilder::fromJsonObject(parent::getJsonObject())
            ->getObject();
    }
}