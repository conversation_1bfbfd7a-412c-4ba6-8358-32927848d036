<?php
// Script untuk menambahkan phrase language untuk Customer Management module
// Jalankan sekali saja setelah module diinstall

// Include CodeIgniter
require_once(BASEPATH.'database/DB.php');

// Phrases yang akan ditambahkan
$phrases = array(
    'customer_list' => 'Customer List',
    'customer_details' => 'Customer Details', 
    'customer_information' => 'Customer Information',
    'customer_statistics' => 'Customer Statistics',
    'total_orders' => 'Total Orders',
    'total_spent' => 'Total Spent',
    'last_order' => 'Last Order',
    'order_history' => 'Order History',
    'member_since' => 'Member Since',
    'view_details' => 'View Details',
    'customer_management' => 'Customer Management',
    'no_record_found' => 'No Record Found',
    'search' => 'Search',
    'loading' => 'Loading',
    'close' => 'Close',
    'back' => 'Back',
    'details' => 'Details',
    'item_name' => 'Item Name',
    'quantity' => 'Quantity',
    'price' => 'Price',
    'total' => 'Total',
    'invoice_no' => 'Invoice No',
    'order_date' => 'Order Date',
    'table' => 'Table',
    'waiter' => 'Waiter',
    'total_amount' => 'Total Amount',
    'status' => 'Status',
    'action' => 'Action',
    'customer_ranking' => 'Customer Ranking',
    'rank' => 'Rank',
    'top_customers_by_spent' => 'Top Customers by Total Spent',
    'top_customers_by_orders' => 'Top Customers by Total Orders',
    'sl' => 'Sl',
    'customer_id' => 'Customer ID',
    'customer_name' => 'Customer Name',
    'email' => 'Email',
    'mobile' => 'Mobile',
    'address' => 'Address',
    'customer_type' => 'Customer Type',
    'order_details' => 'Order Details'
);

// Function untuk menambahkan phrase
function add_phrases($phrases) {
    $CI =& get_instance();
    
    foreach ($phrases as $phrase => $english) {
        // Check if phrase already exists
        $existing = $CI->db->get_where('language', array('phrase' => $phrase))->num_rows();
        
        if ($existing == 0) {
            // Insert new phrase
            $CI->db->insert('language', array('phrase' => $phrase, 'english' => $english));
            echo "Added phrase: $phrase\n";
        } else {
            // Update existing phrase
            $CI->db->where('phrase', $phrase);
            $CI->db->update('language', array('english' => $english));
            echo "Updated phrase: $phrase\n";
        }
    }
}

// Jalankan jika dipanggil langsung
if (defined('BASEPATH')) {
    add_phrases($phrases);
    echo "All phrases have been added/updated successfully!\n";
}
?>
