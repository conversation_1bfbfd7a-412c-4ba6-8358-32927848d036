<?php
/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace SquareConnect\Model;

/**
 * TenderTest Class Doc Comment
 *
 * @category Class
 * @package  SquareConnect
 * <AUTHOR> Inc.
 * @license  http://www.apache.org/licenses/LICENSE-2.0 Apache Licene v2
 * @link     https://squareup.com/developers
 */
class TenderTest extends \PHPUnit_Framework_TestCase
{

    private static $tender;

    /**
     * Setup before running each test case
     */
    public static function setUpBeforeClass() {
        self::$tender = new \SquareConnect\Model\Tender();
    }

    /**
     * Clean up after running each test case
     */
    public static function tearDownAfterClass() {

    }

    /**
     * Test Tender
     */
    public function testTender() {

    }

    /**
     * Test AdditionalRecipients
     */
    public function testAdditionalRecipients() {
        $recipient = [
            "location_id" => "location",
            "description" => "description",
            "amount_money" => [
              "amount" => 1,
              "currency" => "USD"
            ]
        ];
        self::$tender->setAdditionalRecipients([$recipient]);
        $this->assertContains($recipient, self::$tender->getAdditionalRecipients());
    }

}
