<?php

namespace Iyzipay\Request;

use <PERSON>yzipay\JsonBuilder;
use Iyzipay\Request;
use I<PERSON><PERSON>pay\RequestStringBuilder;

class RetrieveProtectedOverleyScriptRequest extends Request
{
    private $position;

    public function getPosition()
    {
        return $this->position;
    }

    public function setPosition($position)
    {
        $this->position = $position;
    }

    public function getJsonObject()
    {
        return JsonBuilder::fromJsonObject(parent::getJsonObject())
            ->add("position", $this->getPosition())
            ->getObject();
    }

    public function toPKIRequestString()
    {
        return RequestStringBuilder::create()
            ->appendSuper(parent::toPKIRequestString())
            ->append("position", $this->getPosition())
            ->getRequestString();
    }
}