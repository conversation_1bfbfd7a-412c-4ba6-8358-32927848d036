<?php
/**
 * NOTE: This class is auto generated by the swagger code generator program. 
 * https://github.com/swagger-api/swagger-codegen 
 * Do not edit the class manually.
 */

namespace SquareConnect\Api;

use \SquareConnect\Configuration;
use \SquareConnect\ApiClient;
use \SquareConnect\ApiException;
use \SquareConnect\ObjectSerializer;

/**
 * TransactionsApiTest Class Doc Comment
 *
 * @category Class
 * @package  SquareConnect
 * <AUTHOR> Inc.
 * @license  http://www.apache.org/licenses/LICENSE-2.0 Apache Licene v2
 * @link     https://squareup.com/developers
 */
class TransactionsApiTest extends \PHPUnit_Framework_TestCase
{

    /**
     * Setup before running each test case
     */
    public static function setUpBeforeClass() {

    }

    /**
     * Clean up after running each test case
     */
    public static function tearDownAfterClass() {

    }

    /**
     * Test case for captureTransaction
     *
     * CaptureTransaction
     *
     */
    public function test_captureTransaction() {

    }
    /**
     * Test case for charge
     *
     * Charge
     *
     */
    public function test_charge() {

    }
    /**
     * Test case for createRefund
     *
     * CreateRefund
     *
     */
    public function test_createRefund() {

    }
    /**
     * Test case for listRefunds
     *
     * ListRefunds
     *
     */
    public function test_listRefunds() {

    }
    /**
     * Test case for listTransactions
     *
     * ListTransactions
     *
     */
    public function test_listTransactions() {

    }
    /**
     * Test case for retrieveTransaction
     *
     * RetrieveTransaction
     *
     */
    public function test_retrieveTransaction() {

    }
    /**
     * Test case for voidTransaction
     *
     * VoidTransaction
     *
     */
    public function test_voidTransaction() {

    }
}
