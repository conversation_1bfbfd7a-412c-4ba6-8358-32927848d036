{"name": "spipu/html2pdf", "type": "library", "description": "Html2Pdf is a HTML to PDF converter written in PHP5 (it uses TCPDF). OFFICIAL PACKAGE", "keywords": ["html", "pdf", "html2pdf"], "homepage": "http://html2pdf.fr/", "license": "OSL-3.0", "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/spipu", "role": "Developer"}], "require": {"php": "^5.6 || ^7.0 || ^8.0", "ext-mbstring": "*", "ext-gd": "*", "tecnickcom/tcpdf": "^6.3"}, "require-dev": {"phpunit/phpunit": "^5.0 || ^9.0"}, "suggest": {"fagundes/zff-html2pdf": "if you need to integrate Html2Pdf with Zend Framework 2 (zf2)", "ext-gd": "Allows to embed images into the PDF"}, "autoload": {"psr-4": {"Spipu\\Html2Pdf\\": "src/"}}, "autoload-dev": {"psr-4": {"Spipu\\Html2Pdf\\Tests\\": "Tests/"}}}