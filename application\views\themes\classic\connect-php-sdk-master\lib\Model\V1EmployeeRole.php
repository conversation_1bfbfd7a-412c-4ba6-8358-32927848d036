<?php
/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace SquareConnect\Model;

use \ArrayAccess;
/**
 * V1EmployeeRole Class Doc Comment
 *
 * @category Class
 * @package  SquareConnect
 * <AUTHOR> Inc.
 * @license  http://www.apache.org/licenses/LICENSE-2.0 Apache License v2
 * @link     https://squareup.com/developers
 */
class V1EmployeeRole implements ArrayAccess
{
    /**
      * Array of property to type mappings. Used for (de)serialization 
      * @var string[]
      */
    static $swaggerTypes = array(
        'id' => 'string',
        'name' => 'string',
        'permissions' => 'string[]',
        'is_owner' => 'bool',
        'created_at' => 'string',
        'updated_at' => 'string'
    );
  
    /** 
      * Array of attributes where the key is the local name, and the value is the original name
      * @var string[] 
      */
    static $attributeMap = array(
        'id' => 'id',
        'name' => 'name',
        'permissions' => 'permissions',
        'is_owner' => 'is_owner',
        'created_at' => 'created_at',
        'updated_at' => 'updated_at'
    );
  
    /**
      * Array of attributes to setter functions (for deserialization of responses)
      * @var string[]
      */
    static $setters = array(
        'id' => 'setId',
        'name' => 'setName',
        'permissions' => 'setPermissions',
        'is_owner' => 'setIsOwner',
        'created_at' => 'setCreatedAt',
        'updated_at' => 'setUpdatedAt'
    );
  
    /**
      * Array of attributes to getter functions (for serialization of requests)
      * @var string[]
      */
    static $getters = array(
        'id' => 'getId',
        'name' => 'getName',
        'permissions' => 'getPermissions',
        'is_owner' => 'getIsOwner',
        'created_at' => 'getCreatedAt',
        'updated_at' => 'getUpdatedAt'
    );
  
    /**
      * $id The role's unique ID, Can only be set by Square.
      * @var string
      */
    protected $id;
    /**
      * $name The role's merchant-defined name.
      * @var string
      */
    protected $name;
    /**
      * $permissions The role's permissions.
      * @var string[]
      */
    protected $permissions;
    /**
      * $is_owner If true, employees with this role have all permissions, regardless of the values indicated in permissions.
      * @var bool
      */
    protected $is_owner;
    /**
      * $created_at The time when the employee entity was created, in ISO 8601 format. Is set by Square when the Role is created.
      * @var string
      */
    protected $created_at;
    /**
      * $updated_at The time when the employee entity was most recently updated, in ISO 8601 format. Is set by Square when the Role updated.
      * @var string
      */
    protected $updated_at;

    /**
     * Constructor
     * @param mixed[] $data Associated array of property value initializing the model
     */
    public function __construct(array $data = null)
    {
        if ($data != null) {
            if (isset($data["id"])) {
              $this->id = $data["id"];
            } else {
              $this->id = null;
            }
            if (isset($data["name"])) {
              $this->name = $data["name"];
            } else {
              $this->name = null;
            }
            if (isset($data["permissions"])) {
              $this->permissions = $data["permissions"];
            } else {
              $this->permissions = null;
            }
            if (isset($data["is_owner"])) {
              $this->is_owner = $data["is_owner"];
            } else {
              $this->is_owner = null;
            }
            if (isset($data["created_at"])) {
              $this->created_at = $data["created_at"];
            } else {
              $this->created_at = null;
            }
            if (isset($data["updated_at"])) {
              $this->updated_at = $data["updated_at"];
            } else {
              $this->updated_at = null;
            }
        }
    }
    /**
     * Gets id
     * @return string
     */
    public function getId()
    {
        return $this->id;
    }
  
    /**
     * Sets id
     * @param string $id The role's unique ID, Can only be set by Square.
     * @return $this
     */
    public function setId($id)
    {
        $this->id = $id;
        return $this;
    }
    /**
     * Gets name
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }
  
    /**
     * Sets name
     * @param string $name The role's merchant-defined name.
     * @return $this
     */
    public function setName($name)
    {
        $this->name = $name;
        return $this;
    }
    /**
     * Gets permissions
     * @return string[]
     */
    public function getPermissions()
    {
        return $this->permissions;
    }
  
    /**
     * Sets permissions
     * @param string[] $permissions The role's permissions.
     * @return $this
     */
    public function setPermissions($permissions)
    {
        $this->permissions = $permissions;
        return $this;
    }
    /**
     * Gets is_owner
     * @return bool
     */
    public function getIsOwner()
    {
        return $this->is_owner;
    }
  
    /**
     * Sets is_owner
     * @param bool $is_owner If true, employees with this role have all permissions, regardless of the values indicated in permissions.
     * @return $this
     */
    public function setIsOwner($is_owner)
    {
        $this->is_owner = $is_owner;
        return $this;
    }
    /**
     * Gets created_at
     * @return string
     */
    public function getCreatedAt()
    {
        return $this->created_at;
    }
  
    /**
     * Sets created_at
     * @param string $created_at The time when the employee entity was created, in ISO 8601 format. Is set by Square when the Role is created.
     * @return $this
     */
    public function setCreatedAt($created_at)
    {
        $this->created_at = $created_at;
        return $this;
    }
    /**
     * Gets updated_at
     * @return string
     */
    public function getUpdatedAt()
    {
        return $this->updated_at;
    }
  
    /**
     * Sets updated_at
     * @param string $updated_at The time when the employee entity was most recently updated, in ISO 8601 format. Is set by Square when the Role updated.
     * @return $this
     */
    public function setUpdatedAt($updated_at)
    {
        $this->updated_at = $updated_at;
        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     * @param  integer $offset Offset 
     * @return boolean
     */
    public function offsetExists($offset)
    {
        return isset($this->$offset);
    }
  
    /**
     * Gets offset.
     * @param  integer $offset Offset 
     * @return mixed 
     */
    public function offsetGet($offset)
    {
        return $this->$offset;
    }
  
    /**
     * Sets value based on offset.
     * @param  integer $offset Offset 
     * @param  mixed   $value  Value to be set
     * @return void
     */
    public function offsetSet($offset, $value)
    {
        $this->$offset = $value;
    }
  
    /**
     * Unsets offset.
     * @param  integer $offset Offset 
     * @return void
     */
    public function offsetUnset($offset)
    {
        unset($this->$offset);
    }
  
    /**
     * Gets the string presentation of the object
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) {
            return json_encode(\SquareConnect\ObjectSerializer::sanitizeForSerialization($this), JSON_PRETTY_PRINT);
        } else {
            return json_encode(\SquareConnect\ObjectSerializer::sanitizeForSerialization($this));
        }
    }
}
