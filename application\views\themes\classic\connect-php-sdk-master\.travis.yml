language: php
sudo: false
dist: trusty
php:
- 5.4
- 5.5
- 5.6
- 7.0
cache:
  directories:
  - $HOME/.composer/cache/files
before_install:
- composer config -g repositories.packagist composer https://packagist.jp
- composer install -vvv
install:
- openssl aes-256-cbc -K $encrypted_afef0992877c_key -iv $encrypted_afef0992877c_iv -in ./travis-ci/accounts.enc -out ./travis-ci/accounts.json -d
script:
- vendor/bin/phpunit test
