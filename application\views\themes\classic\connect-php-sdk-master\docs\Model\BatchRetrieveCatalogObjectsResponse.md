# BatchRetrieveCatalogObjectsResponse

## Properties
Name | Getter | Setter | Type | Description | Notes
------------ | ------------- | ------------- | ------------- | ------------- | -------------
**errors** | getErrors() | setErrors($value) | [**\SquareConnect\Model\Error[]**](Error.md) | The set of [Error](#type-error)s encountered. | [optional] 
**objects** | getObjects() | setObjects($value) | [**\SquareConnect\Model\CatalogObject[]**](CatalogObject.md) | A list of [CatalogObject](#type-catalogobject)s returned. | [optional] 
**related_objects** | getRelatedObjects() | setRelatedObjects($value) | [**\SquareConnect\Model\CatalogObject[]**](CatalogObject.md) | A list of [CatalogObject](#type-catalogobject)s referenced by the object in the &#x60;objects&#x60; field. | [optional] 

Note: All properties are protected and only accessed via getters and setters.

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)

