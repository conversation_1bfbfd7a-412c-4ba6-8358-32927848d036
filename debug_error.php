<?php
// Debug script untuk menangkap error 500 pada sellrpt
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<h2>Debug Error 500 - Sales Report</h2>";

// Simulate CodeIgniter environment
define('BASEPATH', 'application/');
define('APPPATH', 'application/');
define('ENVIRONMENT', 'development');

// Include necessary files
try {
    echo "<h3>1. Loading Configuration Files</h3>";
    
    // Load database config
    include 'application/config/database.php';
    echo "<p style='color: green;'>✓ Database config loaded</p>";
    
    // Load autoload config
    include 'application/config/autoload.php';
    echo "<p style='color: green;'>✓ Autoload config loaded</p>";
    
    echo "<h3>2. Testing Database Connection</h3>";
    $host = $db['default']['hostname'];
    $user = $db['default']['username'];
    $pass = $db['default']['password'];
    $database = $db['default']['database'];
    
    $connection = new mysqli($host, $user, $pass, $database);
    
    if ($connection->connect_error) {
        throw new Exception("Connection failed: " . $connection->connect_error);
    }
    
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    echo "<h3>3. Testing Required Tables</h3>";
    $required_tables = [
        'customer_order',
        'customer_info', 
        'bill',
        'payment_method',
        'setting'
    ];
    
    foreach ($required_tables as $table) {
        $result = $connection->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            echo "<p style='color: green;'>✓ Table '$table' exists</p>";
        } else {
            echo "<p style='color: red;'>✗ Table '$table' missing</p>";
        }
    }
    
    echo "<h3>4. Testing Model Methods Manually</h3>";
    
    // Test invoice dropdown query
    echo "<h4>4.1 Invoice Dropdown Query</h4>";
    $invoice_sql = "SELECT DISTINCT a.saleinvoice FROM customer_order a WHERE a.order_status = 4 AND a.saleinvoice != '' AND a.saleinvoice IS NOT NULL ORDER BY a.saleinvoice ASC LIMIT 5";
    $result = $connection->query($invoice_sql);
    
    if ($result) {
        echo "<p style='color: green;'>✓ Invoice query successful (" . $result->num_rows . " records)</p>";
        $invoices = array('' => 'Select Invoice');
        while ($row = $result->fetch_assoc()) {
            $invoices[$row['saleinvoice']] = $row['saleinvoice'];
        }
        echo "<pre>" . print_r($invoices, true) . "</pre>";
    } else {
        echo "<p style='color: red;'>✗ Invoice query failed: " . $connection->error . "</p>";
    }
    
    // Test customer dropdown query
    echo "<h4>4.2 Customer Dropdown Query</h4>";
    $customer_sql = "SELECT DISTINCT b.customer_id, b.customer_name FROM customer_order a LEFT JOIN customer_info b ON a.customer_id = b.customer_id WHERE a.order_status = 4 AND b.customer_name != '' AND b.customer_name IS NOT NULL ORDER BY b.customer_name ASC LIMIT 5";
    $result = $connection->query($customer_sql);
    
    if ($result) {
        echo "<p style='color: green;'>✓ Customer query successful (" . $result->num_rows . " records)</p>";
        $customers = array('' => 'Select Customer');
        while ($row = $result->fetch_assoc()) {
            $customers[$row['customer_id']] = $row['customer_name'];
        }
        echo "<pre>" . print_r($customers, true) . "</pre>";
    } else {
        echo "<p style='color: red;'>✗ Customer query failed: " . $connection->error . "</p>";
    }
    
    // Test payment method query
    echo "<h4>4.3 Payment Method Query</h4>";
    $payment_sql = "SELECT * FROM payment_method WHERE is_active = 1";
    $result = $connection->query($payment_sql);
    
    if ($result) {
        echo "<p style='color: green;'>✓ Payment method query successful (" . $result->num_rows . " records)</p>";
    } else {
        echo "<p style='color: red;'>✗ Payment method query failed: " . $connection->error . "</p>";
    }
    
    // Test setting query
    echo "<h4>4.4 Setting Query</h4>";
    $setting_sql = "SELECT * FROM setting LIMIT 1";
    $result = $connection->query($setting_sql);
    
    if ($result) {
        echo "<p style='color: green;'>✓ Setting query successful (" . $result->num_rows . " records)</p>";
        if ($result->num_rows > 0) {
            $setting = $result->fetch_assoc();
            echo "<p>Currency ID: " . (isset($setting['currency']) ? $setting['currency'] : 'Not set') . "</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Setting query failed: " . $connection->error . "</p>";
    }
    
    $connection->close();
    
    echo "<h3>5. Testing File Syntax</h3>";
    
    // Test PHP syntax of modified files
    $files_to_check = [
        'application/modules/report/controllers/Reports.php',
        'application/modules/report/models/Report_model.php',
        'application/modules/report/views/salereportfrm.php'
    ];
    
    foreach ($files_to_check as $file) {
        if (file_exists($file)) {
            $output = shell_exec("php -l $file 2>&1");
            if (strpos($output, 'No syntax errors') !== false) {
                echo "<p style='color: green;'>✓ $file - No syntax errors</p>";
            } else {
                echo "<p style='color: red;'>✗ $file - Syntax error: $output</p>";
            }
        } else {
            echo "<p style='color: red;'>✗ $file - File not found</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h3>6. Next Steps</h3>";
echo "<ul>";
echo "<li>If all tests pass, the issue might be with CodeIgniter framework loading</li>";
echo "<li>Check if all required CodeIgniter libraries are loaded</li>";
echo "<li>Check if the 'display' helper function is available</li>";
echo "<li>Check if the 'Modules::run' function is available</li>";
echo "</ul>";
?>
