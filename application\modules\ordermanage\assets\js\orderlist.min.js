function noteCheck(id){$("#status").val(),basicinfo.baseurl}function printRawHtml(view){printJS({printable:view,type:"raw-html"})}function createMargeorder(orderid,value=null){var url=basicinfo.baseurl+"ordermanage/order/showpaymentmodal/"+orderid;callback=function(a){$("#modal-ajaxview").html(a),$("#get-order-flag").val("2")},null==value?getAjaxModal(url):getAjaxModal(url,callback)}function showhidecard(element){var cardtype=$(element).val();$(element).closest("div.row").next().find("div.cardarea");4==cardtype?($("#isonline").val(0),$(element).closest("div.row").next().find("div.cardarea").addClass("display-none"),$("#assigncard_terminal").val(""),$("#assignbank").val(""),$("#assignlastdigit").val("")):1==cardtype?($("#isonline").val(0),$(element).closest("div.row").next().find("div.cardarea").removeClass("display-none")):($("#isonline").val(1),$(element).closest("div.row").next().find("div.cardarea").addClass("display-none"),$("#assigncard_terminal").val(""),$("#assignbank").val(""),$("#assignlastdigit").val(""))}function submitmultiplepay(){var thisForm=$("#paymodal-multiple-form"),inputval=parseFloat(0),maintotalamount=$("#due-amount").text();if($(".number").each((function(){var inputdata=parseFloat($(this).val());inputval+=inputdata})),inputval<parseFloat(maintotalamount))return setTimeout((function(){toastr.options={closeButton:!0,progressBar:!0,showMethod:"slideDown",timeOut:4e3},toastr.error("Pay full amount ","Error")}),100),!1;var formdata=new FormData(thisForm[0]);$.ajax({type:"POST",url:basicinfo.baseurl+"ordermanage/order/paymultiple",data:formdata,processData:!1,contentType:!1,success:function(data){if(1==$("#get-order-flag").val())setTimeout((function(){toastr.options={closeButton:!0,progressBar:!0,showMethod:"slideDown",timeOut:4e3},toastr.success("payment taken successfully","Success"),$("#payprint_marge").modal("hide"),$(".home").trigger("click")}),100);else{$("#payprint_marge").modal("hide");var ordid=$("#get-order-id").val();1!=basicinfo.printtype&&printRawHtml(data),$("#hidecombtn_"+ordid).hide()}}})}function printPosinvoice(id){var csrf=$("#csrfhashresarvation").val(),url=basicinfo.baseurl+"ordermanage/order/posorderinvoice/"+id;$.ajax({type:"GET",url:url,data:{csrf_test_name:csrf},success:function(data){1!=basicinfo.printtype&&printRawHtml(data)}})}function printmergeinvoice(id){var csrf=$("#csrfhashresarvation").val(),url=(id=atob(id),basicinfo.baseurl+"ordermanage/order/checkprint/"+id);$.ajax({type:"GET",url:url,data:{csrf_test_name:csrf},success:function(data){1!=basicinfo.printtype&&printRawHtml(data)}})}function changedueamount(){var inputval=parseFloat(0),maintotalamount=$("#due-amount").text();$(".number").each((function(){var inputdata=parseFloat($(this).val());inputval+=inputdata})),restamount=parseFloat(maintotalamount)-parseFloat(inputval);var changes=restamount.toFixed(2);changes<=0?($("#change-amount").text(Math.abs(changes)),$("#pay-amount").text(0)):($("#change-amount").text(0),$("#pay-amount").text(changes))}function possubpageprint(orderid){var csrf=$("#csrfhashresarvation").val();$.ajax({type:"GET",url:basicinfo.baseurl+"ordermanage/order/posprintdirectsub/"+orderid,data:{csrf_test_name:csrf},success:function(printdata){1!=basicinfo.printtype&&printRawHtml(printdata)}})}function showsplit(orderid){var url=basicinfo.baseurl+"ordermanage/order/showsplitorderlist/"+orderid;getAjaxModal(url,!1,"#modal-ajaxview-split","#payprint_split")}function showhide(id){$("div.food_select").not("#item"+id).removeClass("active"),$("div i").not(".thisrotate"+id).removeClass("left"),$("#item"+id).toggleClass("active"),$(".thisrotate"+id+".rotate").toggleClass("left"),$("#circlek"+id).css("z-index","9");var csrf=$("#csrfhashresarvation").val();if(!0===$("#item"+id).is(".active")){var dataString="orderid="+id+"&csrf_test_name="+csrf;$.ajax({type:"POST",url:basicinfo.baseurl+"ordermanage/order/itemlist",data:dataString,success:function(data){$("#item"+id).html(data)}})}else $("#circlek"+id).css("z-index","3")}$(document).ready((function(){"use strict";var orderlist=$("#tallorder").DataTable({responsive:!0,paging:!0,language:{sProcessing:lang.Processingod,sSearch:lang.search,sLengthMenu:lang.sLengthMenu,sInfo:lang.sInfo,sInfoEmpty:lang.sInfoEmpty,sInfoFiltered:lang.sInfoFiltered,sInfoPostFix:"",sLoadingRecords:lang.sLoadingRecords,sZeroRecords:lang.sZeroRecords,sEmptyTable:lang.sEmptyTable,oPaginate:{sFirst:lang.sFirst,sPrevious:lang.sPrevious,sNext:lang.sNext,sLast:lang.sLast},oAria:{sSortAscending:":"+lang.sSortAscending+'"',sSortDescending:":"+lang.sSortDescending+'"'},select:{rows:{_:lang._sign,0:lang._0sign,1:lang._1sign}},buttons:{copy:lang.copy,csv:lang.csv,excel:lang.excel,pdf:lang.pdf,print:lang.print,colvis:lang.colvis}},dom:"Bfrtip",lengthMenu:[[25,50,100,150,200,500,-1],[25,50,100,150,200,500,"All"]],buttons:[{extend:"copy",className:"btn-sm"},{extend:"csv",title:"ExampleFile",className:"btn-sm",exportOptions:{columns:":visible"}},{extend:"excel",title:"ExampleFile",className:"btn-sm",title:"exportTitle",exportOptions:{columns:":visible"}},{extend:"pdf",title:"ExampleFile",className:"btn-sm",exportOptions:{columns:":visible"}},{extend:"print",className:"btn-sm",exportOptions:{columns:":visible"}},{extend:"colvis",className:"btn-sm"}],searching:!0,processing:!0,serverSide:!0,ajax:{url:basicinfo.baseurl+"ordermanage/order/allorderlist",type:"post",data:function(data){data.csrf_test_name=$("#csrfhashresarvation").val(),data.startdate=$("#from_date").val(),data.enddate=$("#to_date").val()}}});$("#filterordlist").click((function(){var startdate=$("#from_date").val(),enddate=$("#to_date").val();return""==startdate?(alert("Please enter From Date!!"),!1):""==enddate?(alert("Please enter To Date!!"),!1):void orderlist.ajax.reload()})),$("#filterordlistrst").click((function(){$("#from_date").val(""),$("#to_date").val("");orderlist.ajax.reload()}))})),$(document).on("click","#add_new_payment_type",(function(){var url="showpaymentmodal/"+$("#get-order-id").val()+"/1",csrf=$("#csrfhashresarvation").val();$.ajax({type:"GET",url:url,data:{csrf_test_name:csrf},success:function(data){$("#add_new_payment").append(data);var length=$(".number").length;$(".number:eq("+(length-1)+")").val(parseFloat($("#pay-amount").text()))}})})),$(document).on("click",".close_div",(function(){$(this).parent("div").remove(),changedueamount()}));