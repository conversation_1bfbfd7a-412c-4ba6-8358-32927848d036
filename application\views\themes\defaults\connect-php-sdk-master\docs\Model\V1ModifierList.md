# V1ModifierList

## Properties
Name | Getter | Setter | Type | Description | Notes
------------ | ------------- | ------------- | ------------- | ------------- | -------------
**id** | getId() | setId($value) | **string** | The modifier list&#39;s unique ID. | [optional] 
**name** | getName() | setName($value) | **string** | The modifier list&#39;s name. | [optional] 
**selection_type** | getSelectionType() | setSelectionType($value) | **string** | Indicates whether MULTIPLE options or a SINGLE option from the modifier list can be applied to a single item. | [optional] 
**modifier_options** | getModifierOptions() | setModifierOptions($value) | [**\SquareConnect\Model\V1ModifierOption[]**](V1ModifierOption.md) | The options included in the modifier list. | [optional] 

Note: All properties are protected and only accessed via getters and setters.

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)

