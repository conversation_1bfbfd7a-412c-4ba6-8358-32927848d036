* {
	box-sizing: border-box;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
	line-height: 1.4;
}

.wrapper,
.header,
.footer {
	max-width: 1200px;
	padding: 0 30px;
	margin: 0 auto;
	overflow: auto;
}

.box {
	margin: 0 0 30px 0;
	padding: 30px;
	background: #eee;
	border-radius: 5px;
}

img {
	max-width: 100%;
	border-radius: 5px;
}

.image {
	background-size: cover;
        background-position: center center;
	border-radius: 5px;
}

p {
	margin: 30px 0;
}

.col-md-4 > div,
.col-md-12 > div {
    border: 1px solid #0275d8;
    padding: 10px;
}
