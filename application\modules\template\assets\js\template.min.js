"use strict";$("input").attr("autocomplete","off");let prevsltab=$("#filterordlistrst");var baseurl=basicinfo.baseurl;function load_unseen_reservation(view=""){var csrf=$("#csrfhashresarvation").val();$.ajax({url:basicinfo.baseurl+"reservation/reservation/notification",method:"POST",data:{csrf_test_name:csrf,view:view},dataType:"json",success:function(data){data.unseen_reservation>99?$(".reservenotif").html("99+"):data.unseen_reservation>0&&$(".reservenotif").html(data.unseen_reservation)}})}function rem_values(mid){$("#test"+mid).remove();var current=1;$("#mygroupitem tr").each((function(){var newcr="test"+current;$(this).attr("id",newcr),current++}));var m=1;$("#mygroupitem tr td a").each((function(){$(this).attr("onClick","rem_values("+m+")"),m++}));var allVals=[];$(".itemidvid").each((function(){allVals.push($(this).val())})),$("#allid").val(allVals);var allprice=0;$(".myprice").each((function(){allprice=parseFloat(allprice)+parseFloat($(this).val())})),$("#price").val(allprice)}function increse(mid,price,pvid){var sst=$("#sst"+mid).val(),newst=parseInt(sst)+1,newprice=parseFloat(newst)*parseFloat(price);$("#pr"+pvid).val(newprice),$("#sst"+mid).val(newst);var allprice=0;$(".myprice").each((function(){allprice=parseFloat(allprice)+parseFloat($(this).val())})),$("#price").val(allprice)}function decrese(mid,price,pvid){var sst=$("#sst"+mid).val();if(sst<=0)$("#pr"+pvid).val(price),$("#sst"+mid).val(1);else{var newst=parseInt(sst)-1,newprice=parseFloat(newst)*parseFloat(price);$("#pr"+pvid).val(newprice),$("#sst"+mid).val(newst)}var allprice=0;$(".myprice").each((function(){allprice=parseFloat(allprice)+parseFloat($(this).val())})),$("#price").val(allprice)}function addlang(element){var dataString="csrf_test_name="+$("#csrfhashresarvation").val(),url=$(element).attr("data-url");$.ajax({type:"GET",url:url,data:dataString,success:function(data){location.reload()}})}function load_unseen_notification(){var csrf=$("#csrfhashresarvation").val();$.ajax({url:basicinfo.baseurl+"ordermanage/order/notification",method:"POST",data:{view:"",csrf_test_name:csrf},dataType:"json",success:function(data){data.unseen_notification>0&&$(".count").html(data.unseen_notification)}})}$(document).ready((function(){$(document).on("click",".sa-clicon",(function(){swal.close()})),$(document).on("click",".onprocessg",(function(){var datavalue="onprocess=1&csrf_test_name="+$("#csrfhashresarvation").val();$.ajax({type:"POST",url:basicinfo.baseurl+"ordermanage/order/onprocessajax",data:datavalue,success:function(data){$("#onprocesslist").html(data)}})}));var todayorderlist=$("#onprocessing").DataTable({responsive:!0,paging:!0,language:{sProcessing:lang.Processingod,sSearch:lang.search,sLengthMenu:lang.sLengthMenu,sInfo:lang.sInfo,sInfoEmpty:lang.sInfoEmpty,sInfoFiltered:lang.sInfoFiltered,sInfoPostFix:"",sLoadingRecords:lang.sLoadingRecords,sZeroRecords:lang.sZeroRecords,sEmptyTable:lang.sEmptyTable,oPaginate:{sFirst:lang.sFirst,sPrevious:lang.sPrevious,sNext:lang.sNext,sLast:lang.sLast},oAria:{sSortAscending:":"+lang.sSortAscending+'"',sSortDescending:":"+lang.sSortDescending+'"'},select:{rows:{_:lang._sign,0:lang._0sign,1:lang._1sign}},buttons:{copy:lang.copy,csv:lang.csv,excel:lang.excel,pdf:lang.pdf,print:lang.print,colvis:lang.colvis}},dom:"Bfrtip",lengthMenu:[[25,50,100,150,200,500,-1],[25,50,100,150,200,500,"All"]],buttons:[{extend:"copy",className:"btn-sm",footer:!0},{extend:"csv",title:"ExampleFile",className:"btn-sm",footer:!0},{extend:"excel",title:"ExampleFile",className:"btn-sm",title:"exportTitle",footer:!0},{extend:"pdf",title:"ExampleFile",className:"btn-sm",footer:!0},{extend:"print",className:"btn-sm",footer:!0},{extend:"colvis",className:"btn-sm",footer:!0}],searching:!0,processing:!0,serverSide:!0,ajax:{url:basicinfo.baseurl+"ordermanage/order/todayallorder",type:"post",data:function(data){data.csrf_test_name=$("#csrfhashresarvation").val()}},footerCallback:function(row,data,start,end,display){var api=this.api(),intVal=function(i){return"string"==typeof i?1*i.replace(/[\$,]/g,""):"number"==typeof i?i:0};total=api.column(7).data().reduce((function(a,b){return intVal(a)+intVal(b)}),0);var pageTotal=(pageTotal=api.column(7,{page:"current"}).data().reduce((function(a,b){return intVal(a)+intVal(b)}),0)).toFixed(2),total=total.toFixed(2);$(api.column(7).footer()).html(pageTotal+" ( "+total+" total)")}});$(document).on("click",".todlist",(function(){todayorderlist.ajax.reload()}));var onlineoredrlist=$("#Onlineorder").DataTable({responsive:!0,paging:!0,language:{sProcessing:lang.Processingod,sSearch:lang.search,sLengthMenu:lang.sLengthMenu,sInfo:lang.sInfo,sInfoEmpty:lang.sInfoEmpty,sInfoFiltered:lang.sInfoFiltered,sInfoPostFix:"",sLoadingRecords:lang.sLoadingRecords,sZeroRecords:lang.sZeroRecords,sEmptyTable:lang.sEmptyTable,oPaginate:{sFirst:lang.sFirst,sPrevious:lang.sPrevious,sNext:lang.sNext,sLast:lang.sLast},oAria:{sSortAscending:":"+lang.sSortAscending+'"',sSortDescending:":"+lang.sSortDescending+'"'},select:{rows:{_:lang._sign,0:lang._0sign,1:lang._1sign}},buttons:{copy:lang.copy,csv:lang.csv,excel:lang.excel,pdf:lang.pdf,print:lang.print,colvis:lang.colvis}},dom:"Bfrtip",lengthMenu:[[25,50,100,150,200,500,-1],[25,50,100,150,200,500,"All"]],buttons:[{extend:"copy",className:"btn-sm",footer:!0},{extend:"csv",title:"ExampleFile",className:"btn-sm",footer:!0},{extend:"excel",title:"ExampleFile",className:"btn-sm",title:"exportTitle",footer:!0},{extend:"pdf",title:"ExampleFile",className:"btn-sm",footer:!0},{extend:"print",className:"btn-sm",footer:!0},{extend:"colvis",className:"btn-sm",footer:!0}],searching:!0,processing:!0,serverSide:!0,ajax:{url:basicinfo.baseurl+"ordermanage/order/onlinellorder",type:"post",data:function(data){data.csrf_test_name=$("#csrfhashresarvation").val()}},footerCallback:function(row,data,start,end,display){var api=this.api(),intVal=function(i){return"string"==typeof i?1*i.replace(/[\$,]/g,""):"number"==typeof i?i:0};total=api.column(8).data().reduce((function(a,b){return intVal(a)+intVal(b)}),0);var pageTotal=(pageTotal=api.column(8,{page:"current"}).data().reduce((function(a,b){return intVal(a)+intVal(b)}),0)).toFixed(2),total=total.toFixed(2);$(api.column(8).footer()).html(pageTotal+" ( "+total+" total)")}});$(document).on("click",".seelist",(function(){onlineoredrlist.ajax.reload()}));var qroredrlist=$("#myqrorder").DataTable({responsive:!0,paging:!0,language:{sProcessing:lang.Processingod,sSearch:lang.search,sLengthMenu:lang.sLengthMenu,sInfo:lang.sInfo,sInfoEmpty:lang.sInfoEmpty,sInfoFiltered:lang.sInfoFiltered,sInfoPostFix:"",sLoadingRecords:lang.sLoadingRecords,sZeroRecords:lang.sZeroRecords,sEmptyTable:lang.sEmptyTable,oPaginate:{sFirst:lang.sFirst,sPrevious:lang.sPrevious,sNext:lang.sNext,sLast:lang.sLast},oAria:{sSortAscending:":"+lang.sSortAscending+'"',sSortDescending:":"+lang.sSortDescending+'"'},select:{rows:{_:lang._sign,0:lang._0sign,1:lang._1sign}},buttons:{copy:lang.copy,csv:lang.csv,excel:lang.excel,pdf:lang.pdf,print:lang.print,colvis:lang.colvis}},dom:"Bfrtip",createdRow:function(row,data,index){1==data[10]?$(row).css("background-color","#e5cc34c4"):$(row).css("background-color","#ffffff")},lengthMenu:[[25,50,100,150,200,500,-1],[25,50,100,150,200,500,"All"]],buttons:[{extend:"copy",className:"btn-sm",footer:!0},{extend:"csv",title:"ExampleFile",className:"btn-sm",footer:!0},{extend:"excel",title:"ExampleFile",className:"btn-sm",title:"exportTitle",footer:!0},{extend:"pdf",title:"ExampleFile",className:"btn-sm",footer:!0},{extend:"print",className:"btn-sm",footer:!0},{extend:"colvis",className:"btn-sm",footer:!0}],searching:!0,processing:!0,serverSide:!0,ajax:{url:basicinfo.baseurl+"qrapp/qrmodule/allqrorder",type:"post",data:function(data){data.csrf_test_name=$("#csrfhashresarvation").val()}},footerCallback:function(row,data,start,end,display){var api=this.api(),intVal=function(i){return"string"==typeof i?1*i.replace(/[\$,]/g,""):"number"==typeof i?i:0};total=api.column(8).data().reduce((function(a,b){return intVal(a)+intVal(b)}),0);var pageTotal=(pageTotal=api.column(8,{page:"current"}).data().reduce((function(a,b){return intVal(a)+intVal(b)}),0)).toFixed(2),total=total.toFixed(2);$(api.column(8).footer()).html(pageTotal+" ( "+total+" total)")}});$(document).on("click","#todayqrorder",(function(){qroredrlist.ajax.reload()})),$(document).on("click","#cancelreason",(function(){$("#cancelord").modal("hide");var ordid=$("#mycanorder").val(),dataString="status=1&onprocesstab=1&acceptreject=0&reason="+$("#canreason").val()+"&orderid="+ordid+"&csrf_test_name="+$("#csrfhashresarvation").val();$.ajax({type:"POST",url:basicinfo.baseurl+"ordermanage/order/acceptnotify",data:dataString,success:function(data){$("#onprocesslist").html(data),swal("Rejected","Your Order is Cancel","success"),prevsltab.trigger("click"),load_unseen_notification()}})})),$(document).on("click",".aceptorcancel",(function(){var ordid=$(this).attr("data-id"),csrf=($(this).attr("data-type"),$("#csrfhashresarvation").val()),dataovalue="orderid="+ordid+"&csrf_test_name="+csrf,productionsetting=$("#production_setting").val(),message="Are You Accept Or Reject this Order??";if(1==productionsetting){$.ajax({type:"POST",url:basicinfo.baseurl+"ordermanage/order/checkstock",data:dataovalue,async:!1,global:!1,success:function(data){if(1!=data)return message=data,!1}})}if("Are You Accept Or Reject this Order??"!=message)return $("#cancelord").modal("show"),$("#canordid").html(ordid),$("#mycanorder").val(ordid),$("#canreason").val(message),!1;swal({title:"Order Confirmation",text:message,type:"success",showCancelButton:!0,confirmButtonColor:"#28a745",confirmButtonText:"Accept",cancelButtonText:"Reject",closeOnConfirm:!1,closeOnCancel:!0,showCloseButton:!0},(function(isConfirm){if(isConfirm){var dataString="status=1&acceptreject=1&reason=&orderid="+ordid+"&csrf_test_name="+csrf;$.ajax({type:"POST",url:basicinfo.baseurl+"ordermanage/order/acceptnotify",data:dataString,success:function(data){swal("Accepted","Your Order is Accepted","success"),prevsltab.trigger("click"),load_unseen_notification()}})}else $("#cancelord").modal("show"),$("#canordid").html(ordid),$("#mycanorder").val(ordid)}))})),$(document).on("click",".cancelorder",(function(){var ordid=$(this).attr("data-id");$("#cancelord").modal("show"),$("#canordid").html(ordid),$("#mycanorder").val(ordid)}));var allsalesreport=$("#myslreportsf").DataTable({responsive:!0,paging:!0,language:{sProcessing:lang.Processingod,sSearch:lang.search,sLengthMenu:lang.sLengthMenu,sInfo:lang.sInfo,sInfoEmpty:lang.sInfoEmpty,sInfoFiltered:lang.sInfoFiltered,sInfoPostFix:"",sLoadingRecords:lang.sLoadingRecords,sZeroRecords:lang.sZeroRecords,sEmptyTable:lang.sEmptyTable,oPaginate:{sFirst:lang.sFirst,sPrevious:lang.sPrevious,sNext:lang.sNext,sLast:lang.sLast},oAria:{sSortAscending:":"+lang.sSortAscending+'"',sSortDescending:":"+lang.sSortDescending+'"'},select:{rows:{_:lang._sign,0:lang._0sign,1:lang._1sign}},buttons:{copy:lang.copy,csv:lang.csv,excel:lang.excel,pdf:lang.pdf,print:lang.print,colvis:lang.colvis}},dom:"Bfrtip",lengthMenu:[[25,50,100,150,200,500,-1],[25,50,100,150,200,500,"All"]],buttons:[{extend:"copy",className:"btn-sm",footer:!0},{extend:"csv",title:"ExampleFile",className:"btn-sm",footer:!0},{extend:"excel",title:"ExampleFile",className:"btn-sm",title:"exportTitle",footer:!0},{extend:"pdf",title:"ExampleFile",className:"btn-sm",footer:!0},{extend:"print",className:"btn-sm",footer:!0},{extend:"colvis",className:"btn-sm",footer:!0}],searching:!0,processing:!0,serverSide:!0,ajax:{url:basicinfo.baseurl+"report/reports/allsellrpt",type:"post",data:function(data){data.ctypeoption=$("#ctypeoption").val(),data.status=$("#status").val(),data.date_fr=$("#from_date").val(),data.date_to=$("#to_date").val(),data.csrf_test_name=$("#csrfhashresarvation").val()},dataSrc:function(data){data.cardpayments,data.Onlinepayment,data.Cashpayment;return data.data}},drawCallback:function(settings){var api=this.api(),alldata=this.api().ajax.json();$(api.column(0).footer()).html("Total Card Payments: "+alldata.cardpayments+"<br/>  Total Online Payments: "+alldata.Onlinepayment+"<br/>  Total Cash Payments: "+alldata.Cashpayment)},footerCallback:function(row,data,start,end,display){var api=this.api(),intVal=function(i){return"string"==typeof i?1*i.replace(/[\$,]/g,""):"number"==typeof i?i:0};totaldiscount=api.column(5).data().reduce((function(a,b){return intVal(a)+intVal(b)}),0);var discountTotal=(discountTotal=api.column(5,{page:"current"}).data().reduce((function(a,b){return intVal(a)+intVal(b)}),0)).toFixed(2),totaldiscount=totaldiscount.toFixed(2);totalcommision=api.column(6).data().reduce((function(a,b){return intVal(a)+intVal(b)}),0);var commisionTotal=(commisionTotal=api.column(6,{page:"current"}).data().reduce((function(a,b){return intVal(a)+intVal(b)}),0)).toFixed(2),totalcommision=totalcommision.toFixed(2);total=api.column(7).data().reduce((function(a,b){return intVal(a)+intVal(b)}),0);var pageTotal=(pageTotal=api.column(7,{page:"current"}).data().reduce((function(a,b){return intVal(a)+intVal(b)}),0)).toFixed(2),total=total.toFixed(2);$(api.column(5).footer()).html(discountTotal+" ( "+totaldiscount+" total)"),$(api.column(6).footer()).html(commisionTotal+" ( "+totalcommision+" total)"),$(api.column(7).footer()).html(pageTotal+" ( "+total+" total)")}});$("#mysreport").click((function(){allsalesreport.ajax.reload()}));var allsalesreportgt=$("#myslreportsf2").DataTable({responsive:!0,paging:!0,language:{sProcessing:lang.Processingod,sSearch:lang.search,sLengthMenu:lang.sLengthMenu,sInfo:lang.sInfo,sInfoEmpty:lang.sInfoEmpty,sInfoFiltered:lang.sInfoFiltered,sInfoPostFix:"",sLoadingRecords:lang.sLoadingRecords,sZeroRecords:lang.sZeroRecords,sEmptyTable:lang.sEmptyTable,oPaginate:{sFirst:lang.sFirst,sPrevious:lang.sPrevious,sNext:lang.sNext,sLast:lang.sLast},oAria:{sSortAscending:":"+lang.sSortAscending+'"',sSortDescending:":"+lang.sSortDescending+'"'},select:{rows:{_:lang._sign,0:lang._0sign,1:lang._1sign}},buttons:{copy:lang.copy,csv:lang.csv,excel:lang.excel,pdf:lang.pdf,print:lang.print,colvis:lang.colvis}},dom:"Bfrtip",lengthMenu:[[25,50,100,150,200,500,-1],[25,50,100,150,200,500,"All"]],buttons:[{extend:"copy",className:"btn-sm",footer:!0},{extend:"csv",title:"ExampleFile",className:"btn-sm",footer:!0},{extend:"excel",title:"ExampleFile",className:"btn-sm",title:"exportTitle",footer:!0},{extend:"pdf",title:"ExampleFile",className:"btn-sm",footer:!0},{extend:"print",className:"btn-sm",footer:!0},{extend:"colvis",className:"btn-sm",footer:!0}],searching:!0,processing:!0,serverSide:!0,ajax:{url:basicinfo.baseurl+"report/reports/allsellgtrpt",type:"post",data:function(data){data.ctypeoption=$("#ctypeoption").val(),data.status=$("#status").val(),data.date_fr=$("#from_date").val(),data.date_to=$("#to_date").val(),data.csrf_test_name=$("#csrfhashresarvation").val()},dataSrc:function(data){data.cardpayments,data.Onlinepayment,data.Cashpayment;return data.data}},drawCallback:function(settings){var api=this.api(),alldata=this.api().ajax.json();$(api.column(0).footer()).html("Total Card Payments: "+alldata.cardpayments+"<br/>  Total Online Payments: "+alldata.Onlinepayment+"<br/>  Total Cash Payments: "+alldata.Cashpayment)},footerCallback:function(row,data,start,end,display){var api=this.api(),intVal=function(i){return"string"==typeof i?1*i.replace(/[\$,]/g,""):"number"==typeof i?i:0};totaldiscount=api.column(5).data().reduce((function(a,b){return intVal(a)+intVal(b)}),0);var discountTotal=(discountTotal=api.column(5,{page:"current"}).data().reduce((function(a,b){return intVal(a)+intVal(b)}),0)).toFixed(2),totaldiscount=totaldiscount.toFixed(2);totalcommision=api.column(6).data().reduce((function(a,b){return intVal(a)+intVal(b)}),0);var commisionTotal=(commisionTotal=api.column(6,{page:"current"}).data().reduce((function(a,b){return intVal(a)+intVal(b)}),0)).toFixed(2),totalcommision=totalcommision.toFixed(2);total=api.column(7).data().reduce((function(a,b){return intVal(a)+intVal(b)}),0);var pageTotal=(pageTotal=api.column(7,{page:"current"}).data().reduce((function(a,b){return intVal(a)+intVal(b)}),0)).toFixed(2),total=total.toFixed(2);$(api.column(5).footer()).html(discountTotal+" ( "+totaldiscount+" total)"),$(api.column(6).footer()).html(commisionTotal+" ( "+totalcommision+" total)"),$(api.column(7).footer()).html(pageTotal+" ( "+total+" total)")}});$("#mysreport2").click((function(){allsalesreportgt.ajax.reload()}))})),$(".social-icon").iconpicker(),load_unseen_reservation(),setInterval((function(){load_unseen_reservation()}),1e4),$("#fullscreen").on("click",(function(){$(".pe-7s-expand1").toggleClass("fullscreen-active")})),$((function(){var fsDocButton=document.getElementById("fullscreen"),fsExitDocButton=document.getElementById("fullscreen");fsDocButton.addEventListener("click",(function(e){var ele;e.preventDefault(),(ele=document.documentElement).requestFullscreen?ele.requestFullscreen():ele.webkitRequestFullscreen?ele.webkitRequestFullscreen():ele.mozRequestFullScreen?ele.mozRequestFullScreen():ele.msRequestFullscreen?ele.msRequestFullscreen():console.log("Fullscreen API is not supported.")})),fsExitDocButton.addEventListener("click",(function(e){e.preventDefault(),document.exitFullscreen?document.exitFullscreen():document.webkitExitFullscreen?document.webkitExitFullscreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.msExitFullscreen?document.msExitFullscreen():console.log("Fullscreen API is not supported.")}))})),$((function(){var $container=$(".grid");$container.imagesLoaded((function(){$container.masonry({itemSelector:".grid-col",columnWidth:".grid-sizer",percentPosition:!0})})),$("a[data-toggle=tab]").each((function(){$(this).on("shown.bs.tab",(function(){$container.imagesLoaded((function(){$container.masonry({itemSelector:".grid-col",columnWidth:".grid-sizer",percentPosition:!0})}))}))}))})),$((function(){$(".selectall").click((function(){$(this).parent().parent().siblings().find(".individual").prop("checked",$(this).prop("checked"))}))})),$("#respritbl").DataTable({responsive:!0,paging:!0,language:{sProcessing:lang.Processingod,sSearch:lang.search,sLengthMenu:lang.sLengthMenu,sInfo:lang.sInfo,sInfoEmpty:lang.sInfoEmpty,sInfoFiltered:lang.sInfoFiltered,sInfoPostFix:"",sLoadingRecords:lang.sLoadingRecords,sZeroRecords:lang.sZeroRecords,sEmptyTable:lang.sEmptyTable,oPaginate:{sFirst:lang.sFirst,sPrevious:lang.sPrevious,sNext:lang.sNext,sLast:lang.sLast},oAria:{sSortAscending:":"+lang.sSortAscending+'"',sSortDescending:":"+lang.sSortDescending+'"'},select:{rows:{_:lang._sign,0:lang._0sign,1:lang._1sign}},buttons:{copy:lang.copy,csv:lang.csv,excel:lang.excel,pdf:lang.pdf,print:lang.print,colvis:lang.colvis}},dom:"Bfrtip",lengthMenu:[[25,50,100,150,200,500,-1],[25,50,100,150,200,500,"All"]],buttons:[{extend:"copy",className:"btn-sm",footer:!0},{extend:"csv",title:"Report",className:"btn-sm",footer:!0},{extend:"excel",title:"Report",className:"btn-sm",title:"exportTitle",footer:!0},{extend:"pdf",title:"Report",className:"btn-sm",footer:!0},{extend:"print",className:"btn-sm",footer:!0},{extend:"colvis",className:"btn-sm",footer:!0}],searching:!0,processing:!0}),$((function(){var segment4=$("#segment4").val();$("#langtab").DataTable({responsive:!1,paging:!0,dom:"Bfrtip",lengthMenu:[[25,50,100,150,200,500,-1],[25,50,100,150,200,500,"All"]],buttons:[],searching:!0,processing:!0,serverSide:!0,ajax:{url:basicinfo.baseurl+"setting/language/searchlang/"+segment4,type:"post",data:function(data){data.csrf_test_name=$("#csrfhashresarvation").val()}},oSearch:{bSmart:!1,bRegex:!0,sSearch:""}})})),$("#item_list").autocomplete({source:function(request,response){var csrf=$("#csrfhashresarvation").val();$.ajax({type:"POST",url:basicinfo.baseurl+"itemmanage/item_food/checkfood",dataType:"json",data:{q:request.term,csrf_test_name:csrf},success:function(data){response(data)}})},minLength:1,select:function(event,ui){$("#item_list").val("");var foodname=ui.item.ProductName,foodid=ui.item.value,varient=ui.item.varientid,varientname=ui.item.variantName,price=ui.item.price,myitemv=foodid+varient,getpid=$("#allid").val(),isexists=0;if(""!=getpid){var pidarray=getpid.split(","),joinpid=getpid+","+myitemv;jQuery.inArray(myitemv,pidarray)>=0&&(isexists=1,alert("This Item Already Added"));$("#allid").val(joinpid)}else pidarray=getpid.split(","),joinpid=myitemv,$("#allid").val(joinpid);if(0==isexists){var trid=$("#mytble tr:last").attr("id").replace("test",""),mytrid=parseInt(trid)+parseInt(1),new_html_img1='<tr id="test'+mytrid+'"><td><input name="itemidvid" class="itemidvid" type="hidden" value="'+varient+foodid+'"><input name="itemid[]" id="itemid" type="hidden" value="'+foodid+'">'+foodname+'</td><td><input name="varientid[]" id="varientid" type="hidden" value="'+varient+'">'+varientname+'</td><td><input name="myprice" class="myprice" type="hidden" id="pr'+varient+foodid+'" value="'+price+'">'+price+'</td><td style="width:100px;"><button onclick="decrese('+mytrid+","+price+","+varient+foodid+')" class="reduced items-count" type="button"><i class="fa fa-minus"></i></button><input type="text" style="width:25px;" name="qty[]" id="sst'+mytrid+'" maxlength="12" value="1" class="input-text qty" readonly="readonly"><button onclick="increse('+mytrid+","+price+","+varient+foodid+')" class="increase items-count" type="button"><i class="fa fa-plus"></i></button></td><td><a onClick="rem_values('+mytrid+')" style="cursor:pointer;">Remove</a></td></tr>';$("#mygroupitem").append(new_html_img1);var allprice=0;$(".myprice").each((function(){allprice=parseFloat(allprice)+parseFloat($(this).val())})),$("#price").val(allprice)}return this.value="",!1}}),$(".listcat2").on("click",(function(event){var spid=$(this).next(".dropcat").attr("id");$("#"+spid).hasClass("display-none")?($("#"+spid).removeClass("display-none"),$("#"+spid).addClass("display-block")):($("#"+spid).removeClass("display-block"),$("#"+spid).addClass("display-none"))})),$(".listcat3").on("click",(function(event){var spid=$(this).next(".dropcat").attr("id");$("#"+spid).hasClass("display-none")?($("#"+spid).removeClass("display-none"),$("#"+spid).addClass("display-block")):($("#"+spid).removeClass("display-block"),$("#"+spid).addClass("display-none"))}));