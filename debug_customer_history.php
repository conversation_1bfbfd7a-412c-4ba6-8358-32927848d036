<?php
// Debug script for customer history endpoint
echo "<h2>Debug Customer History Endpoint</h2>";

// Test the endpoint directly
$customer_id = 1; // Test with customer ID 1

echo "<h3>Testing URL: ordermanage/order/get_customer_order_datatable/$customer_id</h3>";

// Test the endpoint
$url = "http://localhost/viera_kasir/ordermanage/order/get_customer_order_datatable/$customer_id";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_VERBOSE, true);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);
curl_close($ch);

echo "<p><strong>HTTP Code:</strong> $http_code</p>";
echo "<p><strong>cURL Error:</strong> " . ($curl_error ?: 'None') . "</p>";

echo "<h3>Raw Response:</h3>";
echo "<pre>" . htmlspecialchars($response) . "</pre>";

echo "<h3>JSON Analysis:</h3>";
if ($response) {
    $json_data = json_decode($response, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "<p><strong>✅ Valid JSON</strong></p>";
        echo "<pre>" . print_r($json_data, true) . "</pre>";
    } else {
        echo "<p><strong>❌ Invalid JSON:</strong> " . json_last_error_msg() . "</p>";
        
        // Check for PHP errors in response
        if (strpos($response, 'Fatal error') !== false || strpos($response, 'Parse error') !== false) {
            echo "<p><strong>🚨 PHP Error detected in response!</strong></p>";
        }
    }
} else {
    echo "<p><strong>❌ Empty response</strong></p>";
}

// Test with different customer IDs
echo "<h3>Test Multiple Customer IDs:</h3>";
for ($i = 1; $i <= 5; $i++) {
    $test_url = "http://localhost/viera_kasir/ordermanage/order/get_customer_order_datatable/$i";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $test_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $test_response = curl_exec($ch);
    $test_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    $status_icon = ($test_http_code == 200) ? "✅" : "❌";
    $json_valid = json_decode($test_response) ? "✅" : "❌";
    
    echo "<p>Customer ID $i: $status_icon HTTP $test_http_code | JSON $json_valid</p>";
}

echo "<h3>Troubleshooting Checklist:</h3>";
echo "<ul>";
echo "<li>Check if CodeIgniter routing is working</li>";
echo "<li>Check if Order controller exists and method is accessible</li>";
echo "<li>Check for PHP syntax errors</li>";
echo "<li>Check database connection</li>";
echo "<li>Check if order_model method exists</li>";
echo "</ul>";
?>
