<div class="row">
    <!--  table area -->
    <div class="col-sm-12">

        <div class="panel panel-default thumbnail">
            <link href="<?php echo base_url('application/modules/hrm/assets/css/positionview_style.css') ?>"
                rel="stylesheet" type="text/css" />
            <div class="panel-body">
                <table width="100%" class="datatable table table-striped table-bordered table-hover">
                    <thead>
                        <tr>
                            <th><?php echo display('Sl') ?></th>
                            <th><?php echo display('position_name') ?></th>
                            <th><?php echo display('position_details') ?></th>
                            <th><?php echo display('action') ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($position)) { ?>
                        <?php $sl = 1; ?>
                        <?php foreach ($position as $que) { ?>
                        <tr class="<?php echo ($sl & 1) ? "odd gradeX" : "even gradeC" ?>">
                            <td><?php echo $sl; ?></td>
                            <td><?php echo $que->position_name; ?></td>
                            <td><?php echo $que->position_details; ?></td>

                            <td class="center">
                                <?php if ($this->permission->method('hrm', 'update')->access()) : ?>
                                <a href="<?php echo base_url("hrm/employees/update_form/$que->pos_id") ?>"
                                    class="btn btn-xs btn-success"><i class="fa fa-pencil"></i></a>
                                <?php endif; ?>

                            </td>
                        </tr>
                        <?php $sl++; ?>
                        <?php } ?>
                        <?php } ?>
                    </tbody>
                </table> <!-- /.table-responsive -->
            </div>
        </div>
    </div>
</div>