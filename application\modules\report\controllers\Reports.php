<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Reports extends MX_Controller
{

    public function __construct()
    {
        parent::__construct();
        $this->db->query('SET SESSION sql_mode = ""');
        $this->load->model([
            'report_model',
            'logs_model',
        ]);

        $this->load->helper('formatting_helper');
    }

    public function index($id = null)
    {

        $this->permission->method('report', 'read')->redirect();
        $data['title']    = display('purchase_report');
        $first_date       = str_replace('/', '-', $this->input->post('from_date') ?? '');
        $start_date       = date('Y-m-d', strtotime($first_date));
        $second_date      = str_replace('/', '-', $this->input->post('to_date') ?? '');
        $end_date         = date('Y-m-d', strtotime($second_date));
        $data['preport']  = $this->report_model->pruchasereport($start_date, $end_date);
        $settinginfo      = $this->report_model->settinginfo();
        $data['setting']  = $settinginfo;
        $data['currency'] = $this->report_model->currencysetting($settinginfo->currency);
        $data['module']   = "report";
        $data['page']     = "prechasereport";
        echo Modules::run('template/layout', $data);
    }

    public function purchasereport()
    {
        $this->permission->method('report', 'read')->redirect();
        $data['title']    = display('purchase_report');
        $first_date       = str_replace('/', '-', $this->input->post('from_date'));
        $start_date       = date('Y-m-d', strtotime($first_date));
        $second_date      = str_replace('/', '-', $this->input->post('to_date'));
        $end_date         = date('Y-m-d', strtotime($second_date));
        $data['preport']  = $this->report_model->pruchasereport($start_date, $end_date);
        $settinginfo      = $this->report_model->settinginfo();
        $data['setting']  = $settinginfo;
        $data['currency'] = $this->report_model->currencysetting($settinginfo->currency);
        $data['module']   = "report";
        $data['page']     = "getpreport";
        $this->load->view('report/getpreport', $data);
    }

    public function productwise()
    {
        $this->permission->method('report', 'read')->redirect();
        $data['title'] = display('purchase_report');

        $data['allproduct'] = $this->report_model->productreportall();
        $settinginfo        = $this->report_model->settinginfo();
        $data['setting']    = $settinginfo;
        $data['currency']   = $this->report_model->currencysetting($settinginfo->currency);
        $data['module']     = "report";
        $data['page']       = "product_wise_report";
        echo Modules::run('template/layout', $data);
    }

    public function productwisereport()
    {
        $this->permission->method('report', 'read')->redirect();
        $data['title']      = display('purchase_report');
        $first_date         = str_replace('/', '-', $this->input->post('from_date'));
        $start_date         = date('Y-m-d', strtotime($first_date));
        $second_date        = str_replace('/', '-', $this->input->post('to_date'));
        $end_date           = date('Y-m-d', strtotime($second_date));
        $pid                = $this->input->post('productid');
        $data['allproduct'] = $this->report_model->productreport($start_date, $end_date, $pid);
        $settinginfo        = $this->report_model->settinginfo();
        $data['setting']    = $settinginfo;
        $data['currency']   = $this->report_model->currencysetting($settinginfo->currency);
        $data['module']     = "report";
        $data['page']       = "getproreport";
        $this->load->view('report/getproreport', $data);
    }

    public function ingredientwise()
    {
        $this->permission->method('report', 'read')->redirect();
        $data['title'] = display('purchase_report');

        $data['allproduct'] = $this->report_model->allingredient();
        $settinginfo        = $this->report_model->settinginfo();
        $data['setting']    = $settinginfo;
        $data['currency']   = $this->report_model->currencysetting($settinginfo->currency);
        $data['module']     = "report";
        $data['page']       = "ingredient_wise_report";
        echo Modules::run('template/layout', $data);
    }

    public function ingredientwisereport()
    {
        $this->permission->method('report', 'read')->redirect();
        $data['title']      = display('purchase_report');
        $first_date         = str_replace('/', '-', $this->input->post('from_date'));
        $start_date         = date('Y-m-d', strtotime($first_date));
        $second_date        = str_replace('/', '-', $this->input->post('to_date'));
        $end_date           = date('Y-m-d', strtotime($second_date));
        $pid                = $this->input->post('productid');
        $data['allproduct'] = $this->report_model->ingredientreport($start_date, $end_date, $pid);
        $settinginfo        = $this->report_model->settinginfo();
        $data['setting']    = $settinginfo;
        $data['currency']   = $this->report_model->currencysetting($settinginfo->currency);
        $data['module']     = "report";
        $data['page']       = "kitchenreport";
        $this->load->view('report/kitchenreport', $data);
    }

    public function sellrpt()
    {
        try {
            $this->permission->method('report', 'read')->redirect();
            $data['title']         = display('sell_report');
            $settinginfo           = $this->report_model->settinginfo();
            $data['setting']       = $settinginfo;
            $data['currency']      = $this->report_model->currencysetting($settinginfo->currency);
            $data['paymentmethod'] = $this->report_model->pmethod_dropdown();

            // Get invoices dropdown
            $data['invoices']      = $this->report_model->get_invoice_dropdown();
            if (empty($data['invoices'])) {
                $data['invoices'] = array('' => 'Select Invoice');
            }

            // Get customers dropdown (same as invoice)
            $data['customers']     = $this->report_model->get_customer_dropdown();
            if (empty($data['customers'])) {
                $data['customers'] = array('' => 'Select Customer');
            }

            $data['module']        = "report";
            $data['page']          = "salereportfrm";
            echo Modules::run('template/layout', $data);
        } catch (Exception $e) {
            // Log the error and show a user-friendly message
            log_message('error', 'Error in sellrpt: ' . $e->getMessage());
            show_error('An error occurred while loading the sales report page. Please try again or contact support.');
        }
    }

    public function sellrptbydate()
    {
        $this->permission->method('report', 'read')->redirect();
        $data['title']    = display('sell_report');
        $settinginfo      = $this->report_model->settinginfo();
        $data['setting']  = $settinginfo;
        $data['currency'] = $this->report_model->currencysetting($settinginfo->currency);
        $data['module']   = "report";
        $data['page']     = "salereportbyproduct";
        echo Modules::run('template/layout', $data);
    }

    public function salereportbydate()
    {
        $this->permission->method('report', 'read')->redirect();
        $data['title']     = display('sell_report');
        $first_date        = str_replace('/', '-', $this->input->post('from_date'));
        $start_date        = date('Y-m-d', strtotime($first_date));
        $second_date       = str_replace('/', '-', $this->input->post('to_date'));
        $end_date          = date('Y-m-d', strtotime($second_date));
        $data['preport']   = $this->report_model->salereportbydates($start_date, $end_date);
        $settinginfo       = $this->report_model->settinginfo();
        $data['daterange'] = "customer_order.order_date BETWEEN '$start_date' AND '$end_date' AND customer_order.order_status=4";
        $data['setting']   = $settinginfo;
        $data['currency']  = $this->report_model->currencysetting($settinginfo->currency);
        $data['module']    = "report";
        $data['page']      = "salebydate";
        $this->load->view('report/salebydate', $data);
    }

    public function salereport()
    {
        $this->permission->method('report', 'read')->redirect();
        $data['title'] = display('sell_report');

        $pid         = $this->input->post('paytype', true);
        $invoice_no  = $this->input->post('invoice_no', true);
        $customer_id = $this->input->post('customer_id', true);

        $start_date  = $this->input->post('from_date');
        $end_date    = $this->input->post('to_date');

        $data['preport']  = $this->report_model->salereport($start_date, $end_date, $pid, $invoice_no, $customer_id);
        $settinginfo      = $this->report_model->settinginfo();
        $data['setting']  = $settinginfo;
        $data['currency'] = $this->report_model->currencysetting($settinginfo->currency);
        $data['module']   = "report";
        $data['page']     = "ajaxsalereport";
        $this->load->view('report/ajaxsalereport', $data);
    }

    public function sellrpt2()
    {
        $this->permission->method('report', 'read')->redirect();
        $data['title']       = display('sell_report');
        $settinginfo         = $this->report_model->settinginfo();
        $data['setting']     = $settinginfo;
        $data['currency']    = $this->report_model->currencysetting($settinginfo->currency);
        $data['ctypeoption'] = $this->report_model->ctype_dropdown();
        $data['module']      = "report";
        $data['page']        = "salereportfrm2";
        echo Modules::run('template/layout', $data);
    }

    public function generaterpt()
    {
        $this->permission->method('report', 'read')->redirect();
        $first_date  = str_replace('/', '-', $this->input->post('from_date'));
        $start_date  = date('Y-m-d', strtotime($first_date));
        $second_date = str_replace('/', '-', $this->input->post('to_date'));
        $end_date    = date('Y-m-d', strtotime($second_date));
        $preport     = $this->report_model->salereport($start_date, $end_date, null, null, null);

        if ($preport) {

            foreach ($preport as $pitem) {
                $existsorder = $this->db->select("*")->from('tbl_generatedreport')->where('order_id', $pitem->order_id)->order_by('order_id', 'desc')->get()->row();

                if (empty($existsorder)) {
                    $generaterpt = [
                        'order_id'          => $pitem->order_id,
                        'saleinvoice'       => $pitem->saleinvoice,
                        'customer_id'       => $pitem->customer_id,
                        'cutomertype'       => $pitem->cutomertype,
                        'isthirdparty'      => $pitem->isthirdparty,
                        'waiter_id'         => $pitem->waiter_id,
                        'kitchen'           => $pitem->kitchen,
                        'order_date'        => $pitem->order_date,
                        'order_time'        => $pitem->order_time,
                        'table_no'          => $pitem->table_no,
                        'tokenno'           => $pitem->tokenno,
                        'totalamount'       => $pitem->totalamount,
                        'customerpaid'      => $pitem->customerpaid,
                        'customer_note'     => $pitem->customer_note,
                        'anyreason'         => $pitem->anyreason,
                        'order_status'      => $pitem->order_status,
                        'nofification'      => $pitem->nofification,
                        'orderacceptreject' => $pitem->orderacceptreject,
                        'reportDate'        => $start_date,
                    ];
                    $this->db->insert('tbl_generatedreport', $generaterpt);
                }
            }
        }
    }

    public function generatedrpt()
    {
        $this->permission->method('report', 'read')->redirect();
        $data['title']       = display('sell_report');
        $settinginfo         = $this->report_model->settinginfo();
        $data['setting']     = $settinginfo;
        $data['currency']    = $this->report_model->currencysetting($settinginfo->currency);
        $data['ctypeoption'] = $this->report_model->ctype_dropdown();
        $data['module']      = "report";
        $data['page']        = "searchgenrpt";
        echo Modules::run('template/layout', $data);
    }

    public function allsellrpt()
    {
        $list   = $this->report_model->get_allsalesorder();
        $card   = $this->report_model->count_allpayments(1);
        $online = $this->report_model->count_allpayments(0);
        $cash   = $this->report_model->count_allpayments(4);
        $data   = [];
        $no     = $_POST['start'];

        foreach ($list as $rowdata) {
            $no++;
            $row          = [];
            $thisrpartyid = $rowdata->isthirdparty;

            if ($thisrpartyid > 0) {
                $thirdpartyinfo  = $this->db->select('*')->from('tbl_thirdparty_customer')->where('companyId', $thisrpartyid)->get()->row();
                $persent         = ($thirdpartyinfo->commision * $rowdata->totalamount) / 100;
                $delivaricompany = ' - ' . $thirdpartyinfo->company_name;
            } else {
                $persent         = 0;
                $delivaricompany = '';
            }

            $row[]  = $rowdata->order_date;
            $row[]  = $rowdata->saleinvoice;
            $row[]  = $rowdata->customer_name;
            $row[]  = $rowdata->first_name . $rowdata->last_name;
            $row[]  = $rowdata->customer_type . $delivaricompany;
            $row[]  = $rowdata->discount;
            $row[]  = $persent;
            $row[]  = $rowdata->totalamount;
            $data[] = $row;
        }

        if (empty($card)) {
            $card = 0;
        }

        if (empty($online)) {
            $online = 0;
        }

        if (empty($cash)) {
            $cash = 0;
        }

        $output = [
            "draw"            => $_POST['draw'],
            "cardpayments"    => $card,
            "Onlinepayment"   => $online,
            "Cashpayment"     => $cash,
            "recordsTotal"    => $this->report_model->count_allsalesorder(),
            "recordsFiltered" => $this->report_model->count_filtersalesorder(),
            "data"            => $data,
        ];
        echo json_encode($output);
    }

    public function allsellgtrpt()
    {
        $list   = $this->report_model->get_allsalesgtorder();
        $card   = $this->report_model->count_allpaymentsgt(1);
        $online = $this->report_model->count_allpaymentsgt(0);
        $cash   = $this->report_model->count_allpaymentsgt(4);
        $data   = [];
        $no     = $_POST['start'];

        foreach ($list as $rowdata) {
            $no++;
            $row          = [];
            $thisrpartyid = $rowdata->isthirdparty;

            if ($thisrpartyid > 0) {
                $thirdpartyinfo  = $this->db->select('*')->from('tbl_thirdparty_customer')->where('companyId', $thisrpartyid)->get()->row();
                $persent         = ($thirdpartyinfo->commision * $rowdata->totalamount) / 100;
                $delivaricompany = ' - ' . $thirdpartyinfo->company_name;
            } else {
                $persent         = 0;
                $delivaricompany = '';
            }

            $row[]  = $rowdata->order_date;
            $row[]  = $rowdata->saleinvoice;
            $row[]  = $rowdata->customer_name;
            $row[]  = $rowdata->first_name . $rowdata->last_name;
            $row[]  = $rowdata->customer_type . $delivaricompany;
            $row[]  = $rowdata->discount;
            $row[]  = $persent;
            $row[]  = $rowdata->totalamount;
            $data[] = $row;
        }

        $output = [
            "draw"            => $_POST['draw'],
            "cardpayments"    => $card,
            "Onlinepayment"   => $online,
            "Cashpayment"     => $cash,
            "recordsTotal"    => $this->report_model->count_allsalesgtorder(),
            "recordsFiltered" => $this->report_model->count_filtersalesgtorder(),
            "data"            => $data,
        ];
        echo json_encode($output);
    }

    public function itemsReport()
    {
        try {
            $this->permission->method('report', 'read')->redirect();
            $data['title'] = display('sell_report');
            $catid         = $this->input->post('catid');

            // Validate input dates
            $from_date = $this->input->post('from_date');
            $to_date = $this->input->post('to_date');

            if (empty($from_date) || empty($to_date)) {
                echo json_encode(['error' => 'Please provide valid date range']);
                return;
            }

            $first_date  = str_replace('/', '-', $from_date);
            $start_date  = date('Y-m-d', strtotime($first_date));
            $second_date = str_replace('/', '-', $to_date);
            $end_date    = date('Y-m-d', strtotime($second_date));

            // Validate converted dates
            if ($start_date === false || $end_date === false) {
                echo json_encode(['error' => 'Invalid date format']);
                return;
            }

            $preports    = $this->report_model->itemsReport($start_date, $end_date);
            $i           = 0;
            $order_ids   = [''];

            if (!empty($preports)) {
                foreach ($preports as $preport) {
                    $order_ids[$i] = $preport->order_id;
                    $i++;
                }
            }

            $data['items'] = $this->report_model->order_items($order_ids, $catid);

            $data['allorderid'] = $order_ids;
            $settinginfo        = $this->report_model->settinginfo();
            $data['setting']    = $settinginfo;
            $data['currency']   = $this->report_model->currencysetting($settinginfo->currency);
            $data['module']     = "report";
            $data['name']       = 'Items Name';
            $data['page']       = "ajaxsalereportitems";
            $this->load->view('report/ajaxsalereportitems', $data);
        } catch (Exception $e) {
            log_message('error', 'Error in itemsReport: ' . $e->getMessage());
            echo json_encode(['error' => 'An error occurred while generating the report']);
        }
    }

    public function sellrptItems()
    {
        $this->permission->method('report', 'read')->redirect();
        $data['title']        = display('sell_report_items');
        $settinginfo          = $this->report_model->settinginfo();
        $data['setting']      = $settinginfo;
        $data['currency']     = $this->report_model->currencysetting($settinginfo->currency);
        $data['categorylist'] = $this->report_model->category_dropdown();
        $data['module']       = "report";
        $data['view']         = 'itemsReport';
        $data['page']         = "salereportfrmItems";
        echo Modules::run('template/layout', $data);
    }

    public function sellrptwaiter()
    {
        $this->permission->method('report', 'read')->redirect();
        $data['title']        = display('sell_report_waiters');
        $settinginfo          = $this->report_model->settinginfo();
        $data['setting']      = $settinginfo;
        $data['currency']     = $this->report_model->currencysetting($settinginfo->currency);
        $data['module']       = "report";
        $data['categorylist'] = '';
        $data['view']         = 'waitersReport';
        $data['page']         = "salereportfrmItems";
        echo Modules::run('template/layout', $data);
    }

    public function waitersReport()
    {
        $this->permission->method('report', 'read')->redirect();
        $data['title'] = display('sell_report');

        $first_date    = str_replace('/', '-', $this->input->post('from_date'));
        $start_date    = date('Y-m-d', strtotime($first_date));
        $second_date   = str_replace('/', '-', $this->input->post('to_date'));
        $end_date      = date('Y-m-d', strtotime($second_date));
        $data['items'] = $this->report_model->order_waiters($start_date, $end_date);

        $settinginfo      = $this->report_model->settinginfo();
        $data['setting']  = $settinginfo;
        $data['currency'] = $this->report_model->currencysetting($settinginfo->currency);
        $data['module']   = "report";
        $data['name']     = 'Waiter Name';
        $data['page']     = "ajaxsalereportitems";
        $this->load->view('report/ajaxsalereportitems', $data);
    }

    public function delviryReport()
    {
        $this->permission->method('report', 'read')->redirect();
        $data['title'] = display('sell_report');

        $first_date    = str_replace('/', '-', $this->input->post('from_date'));
        $start_date    = date('Y-m-d', strtotime($first_date));
        $second_date   = str_replace('/', '-', $this->input->post('to_date'));
        $end_date      = date('Y-m-d', strtotime($second_date));
        $data['items'] = $this->report_model->order_delviry($start_date, $end_date);

        $settinginfo      = $this->report_model->settinginfo();
        $data['setting']  = $settinginfo;
        $data['currency'] = $this->report_model->currencysetting($settinginfo->currency);
        $data['module']   = "report";
        $data['name']     = 'Delivery type';
        $data['page']     = "ajaxsalereportdelivery";
        $this->load->view('report/ajaxsalereportdelivery', $data);
    }

    public function sellrptdelvirytype()
    {
        $this->permission->method('report', 'read')->redirect();
        $data['title']    = display('sell_report_delvirytype');
        $settinginfo      = $this->report_model->settinginfo();
        $data['setting']  = $settinginfo;
        $data['currency'] = $this->report_model->currencysetting($settinginfo->currency);
        $data['module']   = "report";

        $data['view'] = 'delviryReport';
        $data['page'] = "salereportfrmItems";
        echo Modules::run('template/layout', $data);
    }

    public function sellrptCasher()
    {
        $this->permission->method('report', 'read')->redirect();
        $data['title']        = display('sell_report_Casher');
        $settinginfo          = $this->report_model->settinginfo();
        $data['setting']      = $settinginfo;
        $data['currency']     = $this->report_model->currencysetting($settinginfo->currency);
        $data['module']       = "report";
        $data['categorylist'] = '';
        $data['view']         = 'casherReport';
        $data['page']         = "salereportfrmItems";
        echo Modules::run('template/layout', $data);
    }

    public function casherReport()
    {
        $this->permission->method('report', 'read')->redirect();
        $data['title'] = display('sell_report');

        $first_date    = str_replace('/', '-', $this->input->post('from_date'));
        $start_date    = date('Y-m-d', strtotime($first_date));
        $second_date   = str_replace('/', '-', $this->input->post('to_date'));
        $end_date      = date('Y-m-d', strtotime($second_date));
        $data['items'] = $this->report_model->order_casher($start_date, $end_date);

        $settinginfo      = $this->report_model->settinginfo();
        $data['setting']  = $settinginfo;
        $data['currency'] = $this->report_model->currencysetting($settinginfo->currency);
        $data['module']   = "report";
        $data['name']     = 'Casher name';
        $data['page']     = "ajaxsalereportitems";
        $this->load->view('report/ajaxsalereportitems', $data);
    }

    public function unpaid_sell()
    {
        $this->permission->method('report', 'read')->redirect();
        $data['title']    = display('unpaid_sell');
        $settinginfo      = $this->report_model->settinginfo();
        $data['setting']  = $settinginfo;
        $data['currency'] = $this->report_model->currencysetting($settinginfo->currency);
        $data['module']   = "report";

        $data['view'] = 'unpaidReport';
        $data['page'] = "salereportfrmunpaid";
        echo Modules::run('template/layout', $data);
    }

    public function unpaidReport()
    {
        $this->permission->method('report', 'read')->redirect();
        $data['title'] = display('unpaid_sell');

        $memberid = $this->input->post('memberid');

        $data['items'] = $this->report_model->show_marge_payment($memberid);

        $data['memberid'] = $memberid;

        $settinginfo      = $this->report_model->settinginfo();
        $data['setting']  = $settinginfo;
        $data['currency'] = $this->report_model->currencysetting($settinginfo->currency);
        $data['module']   = "report";
        $data['name']     = display('ordid');
        $data['page']     = "ajaxsalereportunpaid";
        $this->load->view('report/ajaxsalereportunpaid', $data);
    }

    public function showpaymentmodal($id)
    {
        $marge                 = $this->report_model->show_marge_payment_modal($id);
        $data['marge']         = $marge;
        $data['paymentmethod'] = $this->report_model->pmethod_dropdown();

        $this->load->view('ordermanage/paymodal', $data);
    }

    public function kichansrpt()
    {
        $this->permission->method('report', 'read')->redirect();
        $data['title']    = display('kitchen_sell');
        $data['kitchen']  = $this->report_model->allkitchan();
        $settinginfo      = $this->report_model->settinginfo();
        $data['setting']  = $settinginfo;
        $data['currency'] = $this->report_model->currencysetting($settinginfo->currency);
        $data['module']   = "report";
        $data['view']     = 'kichanReport';

        $data['page'] = "kicReport";
        echo Modules::run('template/layout', $data);
    }

    public function kichanReport()
    {
        $this->permission->method('report', 'read')->redirect();
        $data['title'] = display('sell_report');

        $first_date  = str_replace('/', '-', $this->input->post('from_date'));
        $start_date  = date('Y-m-d', strtotime($first_date));
        $second_date = str_replace('/', '-', $this->input->post('to_date'));
        $end_date    = date('Y-m-d', strtotime($second_date));

        $i = 0;

        $findkicen = $this->report_model->kiread();

        $kichendata = [];
        $y          = 0;

        foreach ($findkicen as $kitchen) {
            $preports        = $this->report_model->itemsKiReport($kitchen->kitchenid, $start_date, $end_date);
            $totalamount     = 0;
            $pricewithaddons = 0;

            foreach ($preports as $value) {

                if ($value->price > 0) {
                    $newprice = $value->price;
                } else {
                    $newprice = $value->mprice;
                }

                if ($value->OffersRate > 0) {

                    $getdisprice = $newprice * $value->OffersRate / 100;
                    $grprice     = $newprice - $getdisprice;
                    $itemprice   = $value->menuqty * $grprice;
                } else {
                    $itemprice = $value->menuqty * $newprice;
                }

                if (@$countprice->add_on_id != null) {
                    $add_on_ids  = explode(',', @$countprice->add_on_id);
                    $add_on_qtys = explode(',', @$countprice->addonsqty);
                    $i           = 0;

                    foreach ($add_on_ids as $add_on_id) {
                        $add_on_price    = $this->report_model->findaddons($add_on_id);
                        $pricewithaddons = $add_on_price->price * $add_on_qtys[$i];

                        $i++;
                    }
                    //end foreach

                }

                $totalamount = $totalamount + $pricewithaddons + $itemprice;
            }
            //end foreach
            $kichendata[$y] = ['kiname' => $kitchen->kitchen_name, 'totalprice' => $totalamount];
            $y++;
        }

        $data['items'] = $kichendata;

        $settinginfo      = $this->report_model->settinginfo();
        $data['setting']  = $settinginfo;
        $data['currency'] = $this->report_model->currencysetting($settinginfo->currency);
        $wherevat         = "`bill_date` BETWEEN '" . $start_date . "' AND '" . $end_date . "' AND `bill_status`=1";
        $sdvat            = $this->db->select("SUM(service_charge+VAT) as sdvat")->from('bill')->where($wherevat)->get()->row();
        $data['vatsd']    = $sdvat->sdvat;
        $data['module']   = "report";
        $data['name']     = 'Kitchen Name';
        $data['page']     = "kicanwiseReport";
        $this->load->view('report/kicanwiseReport', $data);
    }

    public function servicerpt()
    {
        $this->permission->method('report', 'read')->redirect();
        $data['title']    = display('scharge_report');
        $settinginfo      = $this->report_model->settinginfo();
        $data['setting']  = $settinginfo;
        $data['currency'] = $this->report_model->currencysetting($settinginfo->currency);
        $data['module']   = "report";
        $data['view']     = 'schargeReport';
        $data['page']     = "schargeReport";
        echo Modules::run('template/layout', $data);
    }

    public function schargeReport()
    {
        $this->permission->method('report', 'read')->redirect();
        $data['title'] = display('sell_report');

        $first_date  = str_replace('/', '-', $this->input->post('from_date'));
        $start_date  = date('Y-m-d', strtotime($first_date));
        $second_date = str_replace('/', '-', $this->input->post('to_date'));
        $end_date    = date('Y-m-d', strtotime($second_date));

        $i                        = 0;
        $id                       = $this->input->post('orderid');
        $findkicen                = $this->report_model->kiread($id);
        $data['allservicecharge'] = $this->report_model->serchargeReport($id, $start_date, $end_date);

        $settinginfo      = $this->report_model->settinginfo();
        $data['setting']  = $settinginfo;
        $data['currency'] = $this->report_model->currencysetting($settinginfo->currency);
        $data['module']   = "report";
        $data['name']     = display('ordid');
        $data['page']     = "servicechargewisereport";
        $this->load->view('report/servicechargewisereport', $data);
    }

    #payroll commission

    public function payroll_commission($id = null)
    {
        $this->permission->method('report', 'read')->redirect();
        $data['title']    = display('commission');
        $settinginfo      = $this->report_model->settinginfo();
        $data['setting']  = $settinginfo;
        $data['currency'] = $this->report_model->currencysetting($settinginfo->currency);

        if (!empty($id)) {
            $data['table_id']      = $id;
            $data['table_details'] = $this->db->select('tablename')->from('rest_table')->where('tableid', $id)->get()->row();
        }

        $data['module'] = "report";
        $data['view']   = 'showpayroll_commission';
        $data['page']   = "commissionReport";
        echo Modules::run('template/layout', $data);
    }

    public function showpayroll_commission()
    {
        $data['title'] = display('commission') . ' ' . display('report');

        $settinginfo      = $this->report_model->settinginfo();
        $data['setting']  = $settinginfo;
        $data['currency'] = $this->report_model->currencysetting($settinginfo->currency);
        $first_date       = str_replace('/', '-', $this->input->post('from_date'));
        $start_date       = date('Y-m-d', strtotime($first_date));
        $second_date      = str_replace('/', '-', $this->input->post('to_date'));
        $end_date         = date('Y-m-d', strtotime($second_date));
        $table_id         = $this->input->post('table_id');

        if (!empty($table_id)) {
            $data['showcommision'] = $this->report_model->showDataCommsion($start_date, $end_date, $table_id);
        } else {
            $data['showcommision'] = $this->report_model->showDataCommsion($start_date, $end_date);
        }

        $data['commissionRate'] = $this->report_model->showCommsionRate(6);

        $this->load->view('report/showcommision', $data);
    }

    public function table_sale()
    {
        $this->permission->method('report', 'read')->redirect();
        $data['title']    = display('table');
        $settinginfo      = $this->report_model->settinginfo();
        $data['setting']  = $settinginfo;
        $data['currency'] = $this->report_model->currencysetting($settinginfo->currency);
        $data['module']   = "report";
        $data['view']     = 'table_sale_show';
        $data['page']     = "salebytable";
        echo Modules::run('template/layout', $data);
    }

    public function table_sale_show()
    {
        $data['title'] = display('table') . ' ' . display('report');

        $settinginfo           = $this->report_model->settinginfo();
        $data['setting']       = $settinginfo;
        $data['currency']      = $this->report_model->currencysetting($settinginfo->currency);
        $first_date            = str_replace('/', '-', $this->input->post('from_date'));
        $start_date            = date('Y-m-d', strtotime($first_date));
        $second_date           = str_replace('/', '-', $this->input->post('to_date'));
        $end_date              = date('Y-m-d', strtotime($second_date));
        $data['showcommision'] = $this->report_model->showDataTable($start_date, $end_date);
        $this->load->view('report/totaltablewisesale', $data);
    }

    public function cashregister()
    {
        $this->permission->method('report', 'read')->redirect();
        $data['title']    = display('sell_report_cashregister');
        $settinginfo      = $this->report_model->settinginfo();
        $data['setting']  = $settinginfo;
        $data['currency'] = $this->report_model->currencysetting($settinginfo->currency);
        $counterlist      = $this->db->select('*')->from('tbl_cashcounter')->get()->result();
        $userlist         = $this->db->select('tbl_cashregister.*,user.firstname,user.lastname')->from('tbl_cashregister')->join('user', 'user.id=tbl_cashregister.userid', 'left')->get()->result();
        $list['']         = 'Select Counter No';
        $list2['']        = 'Select Cashier';

        if (!empty($counterlist)) {

            foreach ($counterlist as $value) {
                $list[$value->counterno] = $value->counterno;
            }
        }

        $data['allcounter'] = $list;

        if (!empty($userlist)) {

            foreach ($userlist as $value) {
                $list2[$value->userid] = $value->firstname . ' ' . $value->lastname;
            }
        }

        $data['alluser'] = $list2;
        $data['module']  = "report";
        $data['page']    = "cashregister";
        echo Modules::run('template/layout', $data);
    }

    public function getcashregister()
    {
        $data['cashreport'] = $this->report_model->cashregister();
        $this->load->view('report/cash_report', $data);
    }

    public function getcashregisterorder()
    {
        $this->permission->method('report', 'read')->redirect();
        $start_date        = $this->input->post('startdate');
        $end_date          = $this->input->post('enddate');
        $uid               = $this->input->post('uid');
        $data['billeport'] = $this->report_model->cashregisterbill($start_date, $end_date, $uid);
        $this->load->view('report/details', $data);
    }

    public function downloadcashregister()
    {
        $startdate = $this->input->post('startdate');
        $enddate   = $this->input->post('enddate');
        $saveid    = $this->input->post('uid');
        $where     = "opendate Between '$startdate' AND '$enddate'";
        $checkuser = $this->db->select('*')->from('tbl_cashregister')->where('userid', $saveid)->where($where)->where('status', 1)->order_by('id', 'DESC')->get()->row();

        $iteminfo  = $this->report_model->summeryiteminfo($saveid, $startdate, $enddate);
        $i         = 0;
        $order_ids = [''];

        foreach ($iteminfo as $orderid) {
            $order_ids[$i] = $orderid->order_id;
            $i++;
        }

        $addonsitem = $this->report_model->closingaddons($order_ids);
        $k          = 0;
        $test       = [];

        foreach ($addonsitem as $addonsall) {
            $addons    = explode(",", $addonsall->add_on_id);
            $addonsqty = explode(",", $addonsall->addonsqty);
            $x         = 0;

            foreach ($addons as $addonsid) {
                $test[$k][$addonsid] = $addonsqty[$x];
                $x++;
            }

            $k++;
        }

        $final = [];
        array_walk_recursive($test, function ($item, $key) use (&$final) {
            $final[$key] = isset($final[$key]) ? $item + $final[$key] : $item;
        });
        $totalprice = 0;

        foreach ($final as $key => $item) {
            $addonsinfo = $this->db->select("*")->from('add_ons')->where('add_on_id', $key)->get()->row();
            $totalprice = $totalprice + ($addonsinfo->price * $item);
        }

        $data['addonsprice']  = $totalprice;
        $data['registerinfo'] = $checkuser;
        $data['billinfo']     = $this->report_model->billsummery($saveid, $startdate, $enddate);
        $data['totalamount']  = $this->report_model->collectcashsummery($saveid, $startdate, $enddate);
        $data['totalchange']  = $this->report_model->changecashsummery($saveid, $startdate, $enddate);
        $data['itemsummery']  = $this->report_model->closingiteminfo($order_ids);
        echo $viewprint       = $this->load->view('cashclosingsummeryreportpdf', $data, true);
    }

    public function printcashregister()
    {
        $startdate = $this->input->post('startdate');
        $enddate   = $this->input->post('enddate');
        $saveid    = $this->input->post('uid');
        $where     = "opendate Between '$startdate' AND '$enddate'";
        $checkuser = $this->db->select('*')->from('tbl_cashregister')->where('userid', $saveid)->where($where)->where('status', 1)->order_by('id', 'DESC')->get()->row();

        $iteminfo  = $this->report_model->summeryiteminfo($saveid, $startdate, $enddate);
        $i         = 0;
        $order_ids = [''];

        foreach ($iteminfo as $orderid) {
            $order_ids[$i] = $orderid->order_id;
            $i++;
        }

        $addonsitem = $this->report_model->closingaddons($order_ids);
        $k          = 0;
        $test       = [];

        foreach ($addonsitem as $addonsall) {
            $addons    = explode(",", $addonsall->add_on_id);
            $addonsqty = explode(",", $addonsall->addonsqty);
            $x         = 0;

            foreach ($addons as $addonsid) {
                $test[$k][$addonsid] = $addonsqty[$x];
                $x++;
            }

            $k++;
        }

        $final = [];
        array_walk_recursive($test, function ($item, $key) use (&$final) {
            $final[$key] = isset($final[$key]) ? $item + $final[$key] : $item;
        });
        $totalprice = 0;

        foreach ($final as $key => $item) {
            $addonsinfo = $this->db->select("*")->from('add_ons')->where('add_on_id', $key)->get()->row();
            $totalprice = $totalprice + ($addonsinfo->price * $item);
        }

        $data['addonsprice']  = $totalprice;
        $data['registerinfo'] = $checkuser;
        $data['billinfo']     = $this->report_model->billsummery($saveid, $startdate, $enddate);
        $data['totalamount']  = $this->report_model->collectcashsummery($saveid, $startdate, $enddate);
        $data['totalchange']  = $this->report_model->changecashsummery($saveid, $startdate, $enddate);
        $data['itemsummery']  = $this->report_model->closingiteminfo($order_ids);
        echo $viewprint       = $this->load->view('cashclosingsummeryreport', $data, true);
    }

    public function reportSellByCustomer()
    {
        $this->permission->method('report', 'read')->redirect();
        $data['title']        = "Laporan Penjualan Pelanggan";
        $settinginfo          = $this->report_model->settinginfo();
        $data['setting']      = $settinginfo;
        $data['currency']     = $this->report_model->currencysetting($settinginfo->currency);
        $data['module']       = "report";
        $data['categorylist'] = '';
        $data['view']         = 'waitersReport';
        $data['page']         = "salereportfrmItems";
        echo Modules::run('template/layout', $data);
    }

    public function showPageReportSoldItemByCustomer()
    {
        $this->permission->method('report', 'read')->redirect();
        $data['title']         = display('sell_report');
        $settinginfo           = $this->report_model->settinginfo();
        $data['setting']       = $settinginfo;
        $data['currency']      = $this->report_model->currencysetting($settinginfo->currency);
        $data['paymentmethod'] = $this->report_model->pmethod_dropdown();
        $data['module']        = "report";
        $data['page']          = "salereportfrm";
        echo Modules::run('template/layout', $data);
    }

    public function reportSoldItemByCustomer()
    {
        $this->permission->method('report', 'read')->redirect();
        $data['title'] = 'Laporan Penjualan Item Per Pelanggan';

        // read payload
        $customerId        = $this->input->post('customer_id', true);
        $start_date = $this->input->post('from_date');
        $end_date   = $this->input->post('to_date');

        // gain data & construct page data
        $data['dataReportSoldItemByCustomer']  = $this->report_model->reportSoldItemByCustomer($start_date, $end_date, $customerId);
        $settinginfo      = $this->report_model->settinginfo();
        $data['setting']  = $settinginfo;
        $data['currency'] = $this->report_model->currencysetting($settinginfo->currency);
        $data['module']   = "report";
        $data['page']     = "ajaxReportSoldItemByCustomer";
        $this->load->view('report/ajaxReportSoldItemByCustomer', $data);
    }
}
