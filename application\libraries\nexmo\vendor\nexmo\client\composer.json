{"name": "nexmo/client", "description": "PHP Client for using Nexmo's API.", "license": "MIT", "type": "library", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer", "homepage": "http://twitter.com/tjlytle"}], "support": {"email": "<EMAIL>"}, "require": {"php": ">=5.6", "php-http/client-implementation": "^1.0", "zendframework/zend-diactoros": "^1.3", "php-http/guzzle6-adapter": "^1.0", "lcobucci/jwt": "^3.2"}, "require-dev": {"phpunit/phpunit": "^5.3", "php-http/mock-client": "^0.3.0", "estahn/phpunit-json-assertions": "@stable", "squizlabs/php_codesniffer": "^3.1"}, "autoload": {"psr-4": {"Nexmo\\": "src/"}}, "autoload-dev": {"psr-4": {"Nexmo\\": "test/", "NexmoTest\\": "test/"}}}