"use strict";
var today = $("#today").val();
var csrf = $("#csrf").val();
$(document).ready(function () {
  "use strict";

  var view_name = $("#view_name").val();
  console.log({ view_name });

  var myurl = baseurl + "report/reports/" + view_name;
  var csrf = $("#csrfhashresarvation").val();
  var dataString =
    "from_date=" + today + "&to_date=" + today + "&csrf_test_name=" + csrf;
  $.ajax({
    type: "POST",
    url: myurl,
    data: dataString,
    success: function (data) {
      $("#getresult2").html(data);

      // Initialize DataTable after AJAX content is loaded
      setTimeout(function () {
        if ($.fn.DataTable.isDataTable('#respritbl')) {
          $('#respritbl').DataTable().destroy();
        }

        if ($('#respritbl').length > 0) {
          $('#respritbl').DataTable({
            responsive: true,
            paging: true,
            searching: true,
            ordering: true,
            info: true,
            autoWidth: false,
            language: {
              sProcessing: (typeof lang !== 'undefined' && lang.Processingod) ? lang.Processingod : "Processing...",
              sSearch: (typeof lang !== 'undefined' && lang.search) ? lang.search : "Search:",
              sLengthMenu: (typeof lang !== 'undefined' && lang.sLengthMenu) ? lang.sLengthMenu : "Show _MENU_ entries",
              sInfo: (typeof lang !== 'undefined' && lang.sInfo) ? lang.sInfo : "Showing _START_ to _END_ of _TOTAL_ entries",
              sInfoEmpty: (typeof lang !== 'undefined' && lang.sInfoEmpty) ? lang.sInfoEmpty : "Showing 0 to 0 of 0 entries",
              sInfoFiltered: (typeof lang !== 'undefined' && lang.sInfoFiltered) ? lang.sInfoFiltered : "(filtered from _MAX_ total entries)",
              sInfoPostFix: "",
              sLoadingRecords: (typeof lang !== 'undefined' && lang.sLoadingRecords) ? lang.sLoadingRecords : "Loading...",
              sZeroRecords: (typeof lang !== 'undefined' && lang.sZeroRecords) ? lang.sZeroRecords : "No matching records found",
              sEmptyTable: (typeof lang !== 'undefined' && lang.sEmptyTable) ? lang.sEmptyTable : "No data available in table",
              oPaginate: {
                sFirst: (typeof lang !== 'undefined' && lang.sFirst) ? lang.sFirst : "First",
                sPrevious: (typeof lang !== 'undefined' && lang.sPrevious) ? lang.sPrevious : "Previous",
                sNext: (typeof lang !== 'undefined' && lang.sNext) ? lang.sNext : "Next",
                sLast: (typeof lang !== 'undefined' && lang.sLast) ? lang.sLast : "Last"
              },
              oAria: {
                sSortAscending: (typeof lang !== 'undefined' && lang.sSortAscending) ? ":" + lang.sSortAscending + '"' : ": activate to sort column ascending",
                sSortDescending: (typeof lang !== 'undefined' && lang.sSortDescending) ? ":" + lang.sSortDescending + '"' : ": activate to sort column descending"
              }
            },
            dom: 'Bfrtip',
            lengthMenu: [[25, 50, 100, 150, 200, 500, -1], [25, 50, 100, 150, 200, 500, "All"]],
            buttons: [
              { extend: 'copy', className: 'btn btn-sm btn-primary', footer: true },
              { extend: 'csv', title: 'Sales Report Items', className: 'btn btn-sm btn-primary', footer: true },
              { extend: 'excel', title: 'Sales Report Items', className: 'btn btn-sm btn-primary', footer: true },
              { extend: 'pdf', title: 'Sales Report Items', className: 'btn btn-sm btn-primary', footer: true },
              { extend: 'print', className: 'btn btn-sm btn-primary', footer: true },
              { extend: 'colvis', className: 'btn btn-sm btn-primary', footer: true }
            ],
            columnDefs: [
              {
                targets: -1, // Last column (Total Amount)
                type: 'num-fmt',
                className: 'text-right'
              }
            ],
            order: [[0, 'asc']], // Default sort by first column (ascending)
            pageLength: 25,
            stateSave: false,
            processing: false
          });
        }
      }, 100); // Small delay to ensure DOM is ready
    },
  });
});
