<link href="<?php echo base_url('application/modules/report/assets/css/ajaxsalereportitems.css'); ?>" rel="stylesheet" type="text/css" />
<div class="table-responsive">
    <table class="table table-bordered table-striped table-hover" id="respritbl">
        <thead>
            <tr>
                <th><?php echo $name; ?></th>
                <?php if ($name == "Items Name") { ?>
                    <th>Category</th>
                    <th class="text-center"><?php echo display('quantity'); ?></th>
                    <th class="text-right">Unit Price</th>
                <?php } ?>
                <th class="text-right"><?php echo display('total_amount'); ?></th>
            </tr>
        </thead>
        <tbody class="ajaxsalereportitems">
            <?php
            $totalprice = 0;
            if ($items && count($items) > 0) {
                if ($name == "Items Name") {
                    foreach ($items as $item) {
                        // Skip TOTAL row for now, we'll add it at the end
                        if ($item->menu_id == 'TOTAL') {
                            continue;
                        }
                        
                        $totalprice += $item->total_sales;
                        ?>
                        <tr>
                            <td><?php echo htmlspecialchars($item->ProductName); ?></td>
                            <td><?php echo htmlspecialchars($item->category_name); ?></td>
                            <td class="text-center"><?php echo number_format($item->totalqty); ?></td>
                            <td class="text-right">
                                <?php 
                                $currency_symbol = (isset($currency) && $currency->curr_icon) ? $currency->curr_icon : 'Rp ';
                                echo $currency_symbol . number_format($item->mprice, 0, ',', '.'); 
                                ?>
                            </td>
                            <td class="text-right">
                                <?php echo $currency_symbol . number_format($item->total_sales, 0, ',', '.'); ?>
                            </td>
                        </tr>
                        <?php
                    }
                    
                    // Add TOTAL row if exists
                    foreach ($items as $item) {
                        if ($item->menu_id == 'TOTAL') {
                            ?>
                            <tr style="background-color: #f5f5f5; font-weight: bold; border-top: 2px solid #ddd;">
                                <td colspan="2"><strong>TOTAL</strong></td>
                                <td class="text-center"><strong><?php echo number_format($item->totalqty); ?></strong></td>
                                <td class="text-right">-</td>
                                <td class="text-right">
                                    <strong><?php echo $currency_symbol . number_format($item->total_sales, 0, ',', '.'); ?></strong>
                                </td>
                            </tr>
                            <?php
                            break;
                        }
                    }
                } else {
                    // For other report types (not Items Name)
                    foreach ($items as $item) {
                        if ($item->menu_id == 'TOTAL') {
                            continue;
                        }
                        $totalprice += $item->total_sales;
                        ?>
                        <tr>
                            <td><?php echo htmlspecialchars($item->ProductName); ?></td>
                            <td class="text-right">
                                <?php echo $currency_symbol . number_format($item->total_sales, 0, ',', '.'); ?>
                            </td>
                        </tr>
                        <?php
                    }
                    
                    // Add TOTAL row for non-items view
                    foreach ($items as $item) {
                        if ($item->menu_id == 'TOTAL') {
                            ?>
                            <tr style="background-color: #f5f5f5; font-weight: bold; border-top: 2px solid #ddd;">
                                <td><strong>TOTAL</strong></td>
                                <td class="text-right">
                                    <strong><?php echo $currency_symbol . number_format($item->total_sales, 0, ',', '.'); ?></strong>
                                </td>
                            </tr>
                            <?php
                            break;
                        }
                    }
                }
            } else {
                ?>
                <tr>
                    <td colspan="5" class="text-center">
                        <div style="padding: 30px;">
                            <i class="fa fa-info-circle" style="font-size: 48px; color: #ccc; margin-bottom: 15px;"></i><br>
                            <h4 style="color: #999; margin-bottom: 10px;">No Data Available</h4>
                            <p style="color: #666; margin-bottom: 0;">
                                Please check your date range and ensure there are completed orders in the selected period.<br>
                                <small>Date range: <?php echo $this->input->post('from_date') . ' - ' . $this->input->post('to_date'); ?></small>
                            </p>
                        </div>
                    </td>
                </tr>
                <?php
            }
            ?>
        </tbody>
    </table>
</div>

<?php if ($items && count($items) > 0): ?>
<div class="row" style="margin-top: 20px;">
    <div class="col-md-6">
        <div class="alert alert-info">
            <i class="fa fa-info-circle"></i>
            <strong>Report Summary:</strong><br>
            Total Items: <?php echo count($items) - 1; ?> (excluding total row)<br>
            Date Range: <?php echo $this->input->post('from_date') . ' to ' . $this->input->post('to_date'); ?>
        </div>
    </div>
    <div class="col-md-6">
        <div class="alert alert-success">
            <i class="fa fa-money"></i>
            <strong>Grand Total:</strong><br>
            <?php 
            foreach ($items as $item) {
                if ($item->menu_id == 'TOTAL') {
                    echo '<h4>' . $currency_symbol . number_format($item->total_sales, 0, ',', '.') . '</h4>';
                    break;
                }
            }
            ?>
        </div>
    </div>
</div>
<?php endif; ?>
