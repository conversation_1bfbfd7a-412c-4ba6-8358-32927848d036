$(document).ready((function(){"use strict";$("#onprocessing").DataTable({responsive:!0,paging:!0,language:{sProcessing:lang.Processingod,sSearch:lang.search,sLengthMenu:lang.sLengthMenu,sInfo:lang.sInfo,sInfoEmpty:lang.sInfoEmpty,sInfoFiltered:lang.sInfoFiltered,sInfoPostFix:"",sLoadingRecords:lang.sLoadingRecords,sZeroRecords:lang.sZeroRecords,sEmptyTable:lang.sEmptyTable,oPaginate:{sFirst:lang.sFirst,sPrevious:lang.sPrevious,sNext:lang.sNext,sLast:lang.sLast},oAria:{sSortAscending:":"+lang.sSortAscending+'"',sSortDescending:":"+lang.sSortDescending+'"'},select:{rows:{_:lang._sign,0:lang._0sign,1:lang._1sign}},buttons:{copy:lang.copy,csv:lang.csv,excel:lang.excel,pdf:lang.pdf,print:lang.print,colvis:lang.colvis}},dom:"Bfrtip",lengthMenu:[[25,50,100,150,200,500,-1],[25,50,100,150,200,500,"All"]],buttons:[{extend:"copy",className:"btn-sm",footer:!0},{extend:"csv",title:"ExampleFile",className:"btn-sm",footer:!0},{extend:"excel",title:"ExampleFile",className:"btn-sm",title:"exportTitle",footer:!0},{extend:"pdf",title:"ExampleFile",className:"btn-sm",footer:!0},{extend:"print",className:"btn-sm",footer:!0},{extend:"colvis",className:"btn-sm",footer:!0}],searching:!0,processing:!0,serverSide:!0,ajax:{url:basicinfo.baseurl+"ordermanage/order/todayallorder",type:"post",data:function(data){data.csrf_test_name=$("#csrfhashresarvation").val()}},footerCallback:function(row,data,start,end,display){var api=this.api(),intVal=function(i){return"string"==typeof i?1*i.replace(/[\$,]/g,""):"number"==typeof i?i:0};total=api.column(7).data().reduce((function(a,b){return intVal(a)+intVal(b)}),0);var pageTotal=(pageTotal=api.column(7,{page:"current"}).data().reduce((function(a,b){return intVal(a)+intVal(b)}),0)).toFixed(2),total=total.toFixed(2);$(api.column(7).footer()).html(pageTotal+" ( "+total+" total)")}})}));