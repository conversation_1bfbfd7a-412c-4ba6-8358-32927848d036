<?php
    function helperFormatNumber($params)
	{
		$number = isset($params['number']) ? $params['number'] : 0;
		$decimal = isset($params['decimalPlace']) ? $params['decimalPlace'] : 0;
		$code = isset($params['code']) ? $params['code'] : '';

		$result = number_format($number, $decimal, ',', '.');

		if ($code == 'rupiah') {
			return 'Rp ' . $result;
		} elseif ($code == 'thousandSeparator') {
			return $result;
		}

		return $result;
	}
