<link href="<?php echo base_url('application/modules/report/assets/css/ajaxsalereportitems.css'); ?>" rel="stylesheet"
	type="text/css" />
<div class="table-responsive">
	<table class="table table-bordered table-striped table-hover" id="respritbl">
		<thead>
			<tr>
				<th><?php echo $name; ?></th>
				<?php if ($name == "Items Name") { ?>
					<th><?php echo display('varient_name'); ?></th>
					<th><?php echo display('quantity'); ?></th>
				<?php } ?>
				<th class="text-right"><?php echo display('total_amount'); ?></th>

			</tr>
		</thead>
		<tbody class="ajaxsalereportitems">
			<?php
			$totalprice = 0;
			if ($items) {
				if ($name == "Items Name") {
					foreach ($items as $item) {
						if ($item->isgroup == 1) {
							$isgrouporid = "'" . implode("','", $allorderid) . "'";
							$condition = "order_id IN($isgrouporid) AND groupmid=$item->groupmid AND groupvarient=$item->groupvarient";
							$sql = "SELECT  DISTINCT(groupmid) as menu_id,qroupqty,isgroup FROM order_menu WHERE {$condition} AND isgroup=1 Group BY order_id";
							$query = $this->db->query($sql);
							$myqtyinfo = $query->result();
							$mqty = 0;
							foreach ($myqtyinfo as $myqty) {
								$mqty = $mqty + $myqty->qroupqty;
							}
							if ($item->price > 0) {
								if ($item->OffersRate > 0) {
									$getdisprice = $item->price * $item->OffersRate / 100;
									$itemprice = $item->price - $getdisprice;
								} else {
									$itemprice = $item->price;
								}
							} else {
								// Jika price = 0 (compliment), tetap gunakan 0, jangan ambil dari mprice
								$itemprice = 0;
							}
							$itemqty = $mqty;
							$totalprice = $totalprice + ($itemqty * $itemprice);
						} else {
							if ($item->price > 0) {
								if ($item->OffersRate > 0) {
									$getdisprice = $item->price * $item->OffersRate / 100;
									$itemprice = $item->price - $getdisprice;
								} else {
									$itemprice = $item->price;
								}
							} else {
								// Jika price = 0 (compliment), tetap gunakan 0, jangan ambil dari mprice
								$itemprice = 0;
							}
							$itemqty = $item->totalqty;
							$totalprice = $totalprice + ($item->totalqty * $itemprice);
						}
			?>
						<tr>

							<td><?php echo $item->ProductName; ?></td>
							<td><?php echo $item->category_name; ?></td>
							<!-- <td><?php //echo $item->variantName; 
										?></td> -->
							<td><?php echo $itemqty; ?></td>
							<td class="order_total">
								<?php if ($currency->position == 1) {
									echo $currency->curr_icon;
								} ?>

								<!-- <//?php echo ($itemqty * $itemprice); ?> -->

								<?php echo

								helperFormatNumber([
									'number' => $itemqty * $itemprice,
									'code' => 'thousandSeparator',
									'decimalPlace' => 0
								]);

								?>
								<?php
								if ($currency->position == 2) {
									echo $currency->curr_icon;
								}
								?>
							</td>

						</tr>
					<?php }
				} else {
					foreach ($items as $item) {
						$totalprice = $totalprice + $item->totalamount
					?>
						<tr>

							<td><?php echo $item->ProductName; ?></td>

							<td class="total_ammount">

								<?php if ($currency->position == 1) {
									echo $currency->curr_icon;
								} ?>

								<?php echo

								helperFormatNumber([
									'number' => $item->totalamount,
									'code' => 'thousandSeparator',
									'decimalPlace' => 0
								]);

								?>

								<!-- <//?php echo $item->totalamount; ?> -->

								<?php if ($currency->position == 2) {
									echo $currency->curr_icon;
								} ?>
							</td>

						</tr>

			<?php
					}
				}
			}
			?>
		</tbody>
		<tfoot class="ajaxsalereportitems-footer">
			<tr>
				<td class="ajaxsalereportitems-fo-total-sale" colspan="<?php if ($name == "Items Name") {
																			echo 3;
																		} else {
																			echo 1;
																		} ?>" align="right">&nbsp;
					<b><?php echo display('total_sale') ?> </b>
				</td>
				<td class="fo-total-sale"><b>

						<?php if ($currency->position == 1) {
							echo $currency->curr_icon;
						} ?>

						<?php echo

						helperFormatNumber([
							'number' => $totalprice,
							'code' => 'thousandSeparator',
							'decimalPlace' => 0
						]);

						?>

						<!-- <//?php echo $totalprice; ?> -->

						<?php if ($currency->position == 2) {
							echo $currency->curr_icon;
						} ?></b>
				</td>
			</tr>
		</tfoot>
	</table>
</div>

<script type="text/javascript">
	$(document).ready(function() {
		"use strict";

		// Initialize DataTable on the respritbl table
		if ($.fn.DataTable.isDataTable('#respritbl')) {
			$('#respritbl').DataTable().destroy();
		}

		$('#respritbl').DataTable({
			responsive: true,
			paging: true,
			searching: true,
			ordering: true,
			info: true,
			autoWidth: false,
			language: {
				sProcessing: (typeof lang !== 'undefined' && lang.Processingod) ? lang.Processingod : "Processing...",
				sSearch: (typeof lang !== 'undefined' && lang.search) ? lang.search : "Search:",
				sLengthMenu: (typeof lang !== 'undefined' && lang.sLengthMenu) ? lang.sLengthMenu : "Show _MENU_ entries",
				sInfo: (typeof lang !== 'undefined' && lang.sInfo) ? lang.sInfo : "Showing _START_ to _END_ of _TOTAL_ entries",
				sInfoEmpty: (typeof lang !== 'undefined' && lang.sInfoEmpty) ? lang.sInfoEmpty : "Showing 0 to 0 of 0 entries",
				sInfoFiltered: (typeof lang !== 'undefined' && lang.sInfoFiltered) ? lang.sInfoFiltered : "(filtered from _MAX_ total entries)",
				sInfoPostFix: "",
				sLoadingRecords: (typeof lang !== 'undefined' && lang.sLoadingRecords) ? lang.sLoadingRecords : "Loading...",
				sZeroRecords: (typeof lang !== 'undefined' && lang.sZeroRecords) ? lang.sZeroRecords : "No matching records found",
				sEmptyTable: (typeof lang !== 'undefined' && lang.sEmptyTable) ? lang.sEmptyTable : "No data available in table",
				oPaginate: {
					sFirst: (typeof lang !== 'undefined' && lang.sFirst) ? lang.sFirst : "First",
					sPrevious: (typeof lang !== 'undefined' && lang.sPrevious) ? lang.sPrevious : "Previous",
					sNext: (typeof lang !== 'undefined' && lang.sNext) ? lang.sNext : "Next",
					sLast: (typeof lang !== 'undefined' && lang.sLast) ? lang.sLast : "Last"
				},
				oAria: {
					sSortAscending: (typeof lang !== 'undefined' && lang.sSortAscending) ? ":" + lang.sSortAscending + '"' : ": activate to sort column ascending",
					sSortDescending: (typeof lang !== 'undefined' && lang.sSortDescending) ? ":" + lang.sSortDescending + '"' : ": activate to sort column descending"
				}
			},
			dom: 'Bfrtip',
			lengthMenu: [
				[25, 50, 100, 150, 200, 500, -1],
				[25, 50, 100, 150, 200, 500, "All"]
			],
			buttons: [{
					extend: 'copy',
					className: 'btn btn-sm btn-primary',
					footer: true
				},
				{
					extend: 'csv',
					title: 'Sales Report Items',
					className: 'btn btn-sm btn-primary',
					footer: true
				},
				{
					extend: 'excel',
					title: 'Sales Report Items',
					className: 'btn btn-sm btn-primary',
					footer: true
				},
				{
					extend: 'pdf',
					title: 'Sales Report Items',
					className: 'btn btn-sm btn-primary',
					footer: true
				},
				{
					extend: 'print',
					className: 'btn btn-sm btn-primary',
					footer: true
				},
				{
					extend: 'colvis',
					className: 'btn btn-sm btn-primary',
					footer: true
				}
			],
			columnDefs: [{
				targets: -1, // Last column (Total Amount)
				type: 'num-fmt',
				className: 'text-right'
			}],
			order: [
				[0, 'asc']
			], // Default sort by first column (ascending)
			pageLength: 25,
			stateSave: false,
			processing: false
		});
	});
</script>