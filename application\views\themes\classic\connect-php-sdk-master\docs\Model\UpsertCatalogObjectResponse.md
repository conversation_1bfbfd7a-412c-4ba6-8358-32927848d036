# UpsertCatalogObjectResponse

## Properties
Name | Getter | Setter | Type | Description | Notes
------------ | ------------- | ------------- | ------------- | ------------- | -------------
**errors** | getErrors() | setErrors($value) | [**\SquareConnect\Model\Error[]**](Error.md) | The set of [Error](#type-error)s encountered. | [optional] 
**catalog_object** | getCatalogObject() | setCatalogObject($value) | [**\SquareConnect\Model\CatalogObject**](CatalogObject.md) | The created [CatalogObject](#type-catalogobject). | [optional] 
**id_mappings** | getIdMappings() | setIdMappings($value) | [**\SquareConnect\Model\CatalogIdMapping[]**](CatalogIdMapping.md) | The mapping between client and server IDs for this Upsert. | [optional] 

Note: All properties are protected and only accessed via getters and setters.

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)

