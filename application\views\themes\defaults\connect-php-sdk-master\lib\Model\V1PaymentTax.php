<?php
/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace SquareConnect\Model;

use \ArrayAccess;
/**
 * V1PaymentTax Class Doc Comment
 *
 * @category Class
 * @package  SquareConnect
 * <AUTHOR> Inc.
 * @license  http://www.apache.org/licenses/LICENSE-2.0 Apache License v2
 * @link     https://squareup.com/developers
 */
class V1PaymentTax implements ArrayAccess
{
    /**
      * Array of property to type mappings. Used for (de)serialization 
      * @var string[]
      */
    static $swaggerTypes = array(
        'errors' => '\SquareConnect\Model\Error[]',
        'name' => 'string',
        'applied_money' => '\SquareConnect\Model\V1Money',
        'rate' => 'string',
        'inclusion_type' => 'string',
        'fee_id' => 'string'
    );
  
    /** 
      * Array of attributes where the key is the local name, and the value is the original name
      * @var string[] 
      */
    static $attributeMap = array(
        'errors' => 'errors',
        'name' => 'name',
        'applied_money' => 'applied_money',
        'rate' => 'rate',
        'inclusion_type' => 'inclusion_type',
        'fee_id' => 'fee_id'
    );
  
    /**
      * Array of attributes to setter functions (for deserialization of responses)
      * @var string[]
      */
    static $setters = array(
        'errors' => 'setErrors',
        'name' => 'setName',
        'applied_money' => 'setAppliedMoney',
        'rate' => 'setRate',
        'inclusion_type' => 'setInclusionType',
        'fee_id' => 'setFeeId'
    );
  
    /**
      * Array of attributes to getter functions (for serialization of requests)
      * @var string[]
      */
    static $getters = array(
        'errors' => 'getErrors',
        'name' => 'getName',
        'applied_money' => 'getAppliedMoney',
        'rate' => 'getRate',
        'inclusion_type' => 'getInclusionType',
        'fee_id' => 'getFeeId'
    );
  
    /**
      * $errors Any errors that occurred during the request.
      * @var \SquareConnect\Model\Error[]
      */
    protected $errors;
    /**
      * $name The merchant-defined name of the tax.
      * @var string
      */
    protected $name;
    /**
      * $applied_money The amount of money that this tax adds to the payment.
      * @var \SquareConnect\Model\V1Money
      */
    protected $applied_money;
    /**
      * $rate The rate of the tax, as a string representation of a decimal number. A value of 0.07 corresponds to a rate of 7%.
      * @var string
      */
    protected $rate;
    /**
      * $inclusion_type Whether the tax is an ADDITIVE tax or an INCLUSIVE tax.
      * @var string
      */
    protected $inclusion_type;
    /**
      * $fee_id The ID of the tax, if available. Taxes applied in older versions of Square Register might not have an ID.
      * @var string
      */
    protected $fee_id;

    /**
     * Constructor
     * @param mixed[] $data Associated array of property value initializing the model
     */
    public function __construct(array $data = null)
    {
        if ($data != null) {
            if (isset($data["errors"])) {
              $this->errors = $data["errors"];
            } else {
              $this->errors = null;
            }
            if (isset($data["name"])) {
              $this->name = $data["name"];
            } else {
              $this->name = null;
            }
            if (isset($data["applied_money"])) {
              $this->applied_money = $data["applied_money"];
            } else {
              $this->applied_money = null;
            }
            if (isset($data["rate"])) {
              $this->rate = $data["rate"];
            } else {
              $this->rate = null;
            }
            if (isset($data["inclusion_type"])) {
              $this->inclusion_type = $data["inclusion_type"];
            } else {
              $this->inclusion_type = null;
            }
            if (isset($data["fee_id"])) {
              $this->fee_id = $data["fee_id"];
            } else {
              $this->fee_id = null;
            }
        }
    }
    /**
     * Gets errors
     * @return \SquareConnect\Model\Error[]
     */
    public function getErrors()
    {
        return $this->errors;
    }
  
    /**
     * Sets errors
     * @param \SquareConnect\Model\Error[] $errors Any errors that occurred during the request.
     * @return $this
     */
    public function setErrors($errors)
    {
        $this->errors = $errors;
        return $this;
    }
    /**
     * Gets name
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }
  
    /**
     * Sets name
     * @param string $name The merchant-defined name of the tax.
     * @return $this
     */
    public function setName($name)
    {
        $this->name = $name;
        return $this;
    }
    /**
     * Gets applied_money
     * @return \SquareConnect\Model\V1Money
     */
    public function getAppliedMoney()
    {
        return $this->applied_money;
    }
  
    /**
     * Sets applied_money
     * @param \SquareConnect\Model\V1Money $applied_money The amount of money that this tax adds to the payment.
     * @return $this
     */
    public function setAppliedMoney($applied_money)
    {
        $this->applied_money = $applied_money;
        return $this;
    }
    /**
     * Gets rate
     * @return string
     */
    public function getRate()
    {
        return $this->rate;
    }
  
    /**
     * Sets rate
     * @param string $rate The rate of the tax, as a string representation of a decimal number. A value of 0.07 corresponds to a rate of 7%.
     * @return $this
     */
    public function setRate($rate)
    {
        $this->rate = $rate;
        return $this;
    }
    /**
     * Gets inclusion_type
     * @return string
     */
    public function getInclusionType()
    {
        return $this->inclusion_type;
    }
  
    /**
     * Sets inclusion_type
     * @param string $inclusion_type Whether the tax is an ADDITIVE tax or an INCLUSIVE tax.
     * @return $this
     */
    public function setInclusionType($inclusion_type)
    {
        $this->inclusion_type = $inclusion_type;
        return $this;
    }
    /**
     * Gets fee_id
     * @return string
     */
    public function getFeeId()
    {
        return $this->fee_id;
    }
  
    /**
     * Sets fee_id
     * @param string $fee_id The ID of the tax, if available. Taxes applied in older versions of Square Register might not have an ID.
     * @return $this
     */
    public function setFeeId($fee_id)
    {
        $this->fee_id = $fee_id;
        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     * @param  integer $offset Offset 
     * @return boolean
     */
    public function offsetExists($offset)
    {
        return isset($this->$offset);
    }
  
    /**
     * Gets offset.
     * @param  integer $offset Offset 
     * @return mixed 
     */
    public function offsetGet($offset)
    {
        return $this->$offset;
    }
  
    /**
     * Sets value based on offset.
     * @param  integer $offset Offset 
     * @param  mixed   $value  Value to be set
     * @return void
     */
    public function offsetSet($offset, $value)
    {
        $this->$offset = $value;
    }
  
    /**
     * Unsets offset.
     * @param  integer $offset Offset 
     * @return void
     */
    public function offsetUnset($offset)
    {
        unset($this->$offset);
    }
  
    /**
     * Gets the string presentation of the object
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) {
            return json_encode(\SquareConnect\ObjectSerializer::sanitizeForSerialization($this), JSON_PRETTY_PRINT);
        } else {
            return json_encode(\SquareConnect\ObjectSerializer::sanitizeForSerialization($this));
        }
    }
}
