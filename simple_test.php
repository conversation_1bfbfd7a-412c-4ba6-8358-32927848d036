<?php
// Simple test untuk melihat error
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "Testing basic functionality...<br>";

// Test 1: Basic PHP
echo "1. PHP working: OK<br>";

// Test 2: Database connection
try {
    include 'application/config/database.php';
    $host = $db['default']['hostname'];
    $user = $db['default']['username'];
    $pass = $db['default']['password'];
    $database = $db['default']['database'];
    
    $connection = new mysqli($host, $user, $pass, $database);
    
    if ($connection->connect_error) {
        echo "2. Database connection: FAILED - " . $connection->connect_error . "<br>";
    } else {
        echo "2. Database connection: OK<br>";
        
        // Test simple query
        $result = $connection->query("SELECT COUNT(*) as count FROM customer_order WHERE order_status = 4");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "3. Sample query: OK - Found " . $row['count'] . " completed orders<br>";
        } else {
            echo "3. Sample query: FAILED - " . $connection->error . "<br>";
        }
        
        $connection->close();
    }
} catch (Exception $e) {
    echo "2. Database test: ERROR - " . $e->getMessage() . "<br>";
}

// Test 3: File permissions
$files = [
    'application/modules/report/controllers/Reports.php',
    'application/modules/report/models/Report_model.php'
];

foreach ($files as $file) {
    if (file_exists($file) && is_readable($file)) {
        echo "4. File $file: OK<br>";
    } else {
        echo "4. File $file: NOT ACCESSIBLE<br>";
    }
}

echo "<br>If all tests show OK, try accessing the sales report page again.";
?>
