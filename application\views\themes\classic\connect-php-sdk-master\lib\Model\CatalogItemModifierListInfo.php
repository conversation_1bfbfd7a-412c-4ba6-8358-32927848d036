<?php
/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace SquareConnect\Model;

use \ArrayAccess;
/**
 * CatalogItemModifierListInfo Class Doc Comment
 *
 * @category Class
 * @package  SquareConnect
 * <AUTHOR> Inc.
 * @license  http://www.apache.org/licenses/LICENSE-2.0 Apache License v2
 * @link     https://squareup.com/developers
 */
class CatalogItemModifierListInfo implements ArrayAccess
{
    /**
      * Array of property to type mappings. Used for (de)serialization 
      * @var string[]
      */
    static $swaggerTypes = array(
        'modifier_list_id' => 'string',
        'modifier_overrides' => '\SquareConnect\Model\CatalogModifierOverride[]',
        'min_selected_modifiers' => 'int',
        'max_selected_modifiers' => 'int',
        'enabled' => 'bool'
    );
  
    /** 
      * Array of attributes where the key is the local name, and the value is the original name
      * @var string[] 
      */
    static $attributeMap = array(
        'modifier_list_id' => 'modifier_list_id',
        'modifier_overrides' => 'modifier_overrides',
        'min_selected_modifiers' => 'min_selected_modifiers',
        'max_selected_modifiers' => 'max_selected_modifiers',
        'enabled' => 'enabled'
    );
  
    /**
      * Array of attributes to setter functions (for deserialization of responses)
      * @var string[]
      */
    static $setters = array(
        'modifier_list_id' => 'setModifierListId',
        'modifier_overrides' => 'setModifierOverrides',
        'min_selected_modifiers' => 'setMinSelectedModifiers',
        'max_selected_modifiers' => 'setMaxSelectedModifiers',
        'enabled' => 'setEnabled'
    );
  
    /**
      * Array of attributes to getter functions (for serialization of requests)
      * @var string[]
      */
    static $getters = array(
        'modifier_list_id' => 'getModifierListId',
        'modifier_overrides' => 'getModifierOverrides',
        'min_selected_modifiers' => 'getMinSelectedModifiers',
        'max_selected_modifiers' => 'getMaxSelectedModifiers',
        'enabled' => 'getEnabled'
    );
  
    /**
      * $modifier_list_id The ID of the [CatalogModifierList](#type-catalogmodifierlist) controlled by this [CatalogModifierListInfo](#type-catalogmodifierlistinfo).
      * @var string
      */
    protected $modifier_list_id;
    /**
      * $modifier_overrides A set of [CatalogModifierOverride](#type-catalogmodifieroverride) objects that override whether a given [CatalogModifier](#type-catalogmodifier) is enabled by default.
      * @var \SquareConnect\Model\CatalogModifierOverride[]
      */
    protected $modifier_overrides;
    /**
      * $min_selected_modifiers If zero or larger, the smallest number of [CatalogModifier](#type-catalogmodifier)s that must be selected from this [CatalogModifierList](#type-catalogmodifierlist).
      * @var int
      */
    protected $min_selected_modifiers;
    /**
      * $max_selected_modifiers If zero or larger, the largest number of [CatalogModifier](#type-catalogmodifier)s that can be selected from this [CatalogModifierList](#type-catalogmodifierlist).
      * @var int
      */
    protected $max_selected_modifiers;
    /**
      * $enabled If `true`, enable this [CatalogModifierList](#type-catalogmodifierlist).
      * @var bool
      */
    protected $enabled;

    /**
     * Constructor
     * @param mixed[] $data Associated array of property value initializing the model
     */
    public function __construct(array $data = null)
    {
        if ($data != null) {
            if (isset($data["modifier_list_id"])) {
              $this->modifier_list_id = $data["modifier_list_id"];
            } else {
              $this->modifier_list_id = null;
            }
            if (isset($data["modifier_overrides"])) {
              $this->modifier_overrides = $data["modifier_overrides"];
            } else {
              $this->modifier_overrides = null;
            }
            if (isset($data["min_selected_modifiers"])) {
              $this->min_selected_modifiers = $data["min_selected_modifiers"];
            } else {
              $this->min_selected_modifiers = null;
            }
            if (isset($data["max_selected_modifiers"])) {
              $this->max_selected_modifiers = $data["max_selected_modifiers"];
            } else {
              $this->max_selected_modifiers = null;
            }
            if (isset($data["enabled"])) {
              $this->enabled = $data["enabled"];
            } else {
              $this->enabled = null;
            }
        }
    }
    /**
     * Gets modifier_list_id
     * @return string
     */
    public function getModifierListId()
    {
        return $this->modifier_list_id;
    }
  
    /**
     * Sets modifier_list_id
     * @param string $modifier_list_id The ID of the [CatalogModifierList](#type-catalogmodifierlist) controlled by this [CatalogModifierListInfo](#type-catalogmodifierlistinfo).
     * @return $this
     */
    public function setModifierListId($modifier_list_id)
    {
        $this->modifier_list_id = $modifier_list_id;
        return $this;
    }
    /**
     * Gets modifier_overrides
     * @return \SquareConnect\Model\CatalogModifierOverride[]
     */
    public function getModifierOverrides()
    {
        return $this->modifier_overrides;
    }
  
    /**
     * Sets modifier_overrides
     * @param \SquareConnect\Model\CatalogModifierOverride[] $modifier_overrides A set of [CatalogModifierOverride](#type-catalogmodifieroverride) objects that override whether a given [CatalogModifier](#type-catalogmodifier) is enabled by default.
     * @return $this
     */
    public function setModifierOverrides($modifier_overrides)
    {
        $this->modifier_overrides = $modifier_overrides;
        return $this;
    }
    /**
     * Gets min_selected_modifiers
     * @return int
     */
    public function getMinSelectedModifiers()
    {
        return $this->min_selected_modifiers;
    }
  
    /**
     * Sets min_selected_modifiers
     * @param int $min_selected_modifiers If zero or larger, the smallest number of [CatalogModifier](#type-catalogmodifier)s that must be selected from this [CatalogModifierList](#type-catalogmodifierlist).
     * @return $this
     */
    public function setMinSelectedModifiers($min_selected_modifiers)
    {
        $this->min_selected_modifiers = $min_selected_modifiers;
        return $this;
    }
    /**
     * Gets max_selected_modifiers
     * @return int
     */
    public function getMaxSelectedModifiers()
    {
        return $this->max_selected_modifiers;
    }
  
    /**
     * Sets max_selected_modifiers
     * @param int $max_selected_modifiers If zero or larger, the largest number of [CatalogModifier](#type-catalogmodifier)s that can be selected from this [CatalogModifierList](#type-catalogmodifierlist).
     * @return $this
     */
    public function setMaxSelectedModifiers($max_selected_modifiers)
    {
        $this->max_selected_modifiers = $max_selected_modifiers;
        return $this;
    }
    /**
     * Gets enabled
     * @return bool
     */
    public function getEnabled()
    {
        return $this->enabled;
    }
  
    /**
     * Sets enabled
     * @param bool $enabled If `true`, enable this [CatalogModifierList](#type-catalogmodifierlist).
     * @return $this
     */
    public function setEnabled($enabled)
    {
        $this->enabled = $enabled;
        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     * @param  integer $offset Offset 
     * @return boolean
     */
    public function offsetExists($offset)
    {
        return isset($this->$offset);
    }
  
    /**
     * Gets offset.
     * @param  integer $offset Offset 
     * @return mixed 
     */
    public function offsetGet($offset)
    {
        return $this->$offset;
    }
  
    /**
     * Sets value based on offset.
     * @param  integer $offset Offset 
     * @param  mixed   $value  Value to be set
     * @return void
     */
    public function offsetSet($offset, $value)
    {
        $this->$offset = $value;
    }
  
    /**
     * Unsets offset.
     * @param  integer $offset Offset 
     * @return void
     */
    public function offsetUnset($offset)
    {
        unset($this->$offset);
    }
  
    /**
     * Gets the string presentation of the object
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) {
            return json_encode(\SquareConnect\ObjectSerializer::sanitizeForSerialization($this), JSON_PRETTY_PRINT);
        } else {
            return json_encode(\SquareConnect\ObjectSerializer::sanitizeForSerialization($this));
        }
    }
}
