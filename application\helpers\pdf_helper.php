<?php
    use Dompdf\Dompdf;

    function generate_pdf_file($html, $filename = 'output.pdf', $folder = 'assets/custom/')
    {
        // Load Composer Autoload
        $autoload = APPPATH . '../vendor/autoload.php';
        if (!file_exists($autoload)) {
            die('Autoload file not found. Run composer install.');
        }

        require_once $autoload;

        // Create PDF
        try {
            $dompdf = new Dompdf();
            $dompdf->loadHtml($html);
            $dompdf->setPaper('A4', 'portrait');
            $dompdf->render();
        } catch (Exception $e) {
            die('PDF generation failed: ' . $e->getMessage());
        }

        // Make sure the folder exists
        $savePath = FCPATH . $folder;
        if (!is_dir($savePath)) {
            if (!mkdir($savePath, 0755, true)) {
                die('Failed to create folder: ' . $savePath);
            }
        }

        $fullPath = $savePath . $filename;
        $pdfOutput = $dompdf->output();

        if (!$pdfOutput || file_put_contents($fullPath, $pdfOutput) === false) {
            die('Failed to save PDF to: ' . $fullPath);
        }

        return base_url($folder . $filename); // Return URL for download
    }
