@!0QR code demo
! (k 1A2 (k 1C(k 1E0(k 1P0Testing 123(k 1Q0Most simple example

a(k 1A2 (k 1C(k 1E0(k 1P0Testing 123(k 1Q0Same example, centred
a 
!0Data encoding
! (k 1A2 (k 1C(k 1E0(k+ 1P00123456789012345678901234567890123456789(k 1Q0Numeric

(k 1A2 (k 1C(k 1E0(k+ 1P0abcdefghijklmnopqrstuvwxyzabcdefghijklmn(k 1Q0Alphanumeric

(k 1A2 (k 1C(k 1E0(k+ 1P0                                        (k 1Q0Binary

!0Error correction
! (k 1A2 (k 1C(k 1E0(k 1P0Testing 123(k 1Q0Error correction L

(k 1A2 (k 1C(k 1E1(k 1P0Testing 123(k 1Q0Error correction M

(k 1A2 (k 1C(k 1E2(k 1P0Testing 123(k 1Q0Error correction Q

(k 1A2 (k 1C(k 1E3(k 1P0Testing 123(k 1Q0Error correction H

!0Pixel size
! (k 1A2 (k 1C(k 1E0(k 1P0Testing 123(k 1Q0Pixel size 1 (minimum)

(k 1A2 (k 1C(k 1E0(k 1P0Testing 123(k 1Q0Pixel size 2 

(k 1A2 (k 1C(k 1E0(k 1P0Testing 123(k 1Q0Pixel size 3 (default)

(k 1A2 (k 1C(k 1E0(k 1P0Testing 123(k 1Q0Pixel size 4 

(k 1A2 (k 1C(k 1E0(k 1P0Testing 123(k 1Q0Pixel size 5 

(k 1A2 (k 1C
(k 1E0(k 1P0Testing 123(k 1Q0Pixel size 10 

(k 1A2 (k 1C(k 1E0(k 1P0Testing 123(k 1Q0Pixel size 16 (maximum)

!0QR model
! (k 1A1 (k 1C(k 1E0(k 1P0Testing 123(k 1Q0QR Model 1

(k 1A2 (k 1C(k 1E0(k 1P0Testing 123(k 1Q0QR Model 2 (default)

(k 1A3 (k 1C(k 1E0(k 1P0Testing 123(k 1Q0Micro QR code
(not supported on all printers)

VA