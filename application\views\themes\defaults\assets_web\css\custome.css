.bookinfo{margin-bottom:0 !important;}
.modal-dialog {
    max-width: 560px !important;
}
.calculate-content .btn {
   color:#FFF !important;
}
.serach{cursor:pointer;}
.snackbar {
    visibility: hidden;
    width: 100%;
    background-color: #333;
    color: #fff;
    text-align: center;
    border-radius: 2px;
    padding: 16px;
    position: relative;
    z-index: 9999;
    bottom: 30px;
    font-size: 17px;
	display:none;
	transition: all 300ms linear 0s;
}

.show {
	display:block;
    visibility: visible !important;
    -webkit-animation: fadein 0.5s, fadeout 0.5s 2.5s;
    animation: fadein 0.5s, fadeout 0.5s 2.5s;
	
}
.ui-front {
    z-index: 9999 !important;
}

.shipping-form h2, .coupon-form h2{
	margin-bottom: 15px;
}

.cart-totals .cart-totals-border{
	border: 0;
}

@media(max-width: 991px){
	.shipping-form,
	.coupon-form{
		margin-bottom: 30px	
	}
}