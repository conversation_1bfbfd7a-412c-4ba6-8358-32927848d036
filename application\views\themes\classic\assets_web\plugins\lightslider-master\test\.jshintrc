{"curly": true, "eqeqeq": true, "immed": true, "latedef": true, "newcap": true, "noarg": true, "sub": true, "undef": true, "unused": true, "boss": true, "eqnull": true, "browser": true, "node": true, "predef": ["j<PERSON><PERSON><PERSON>", "QUnit", "module", "test", "asyncTest", "expect", "start", "stop", "ok", "equal", "notEqual", "deepEqual", "notDeepEqual", "strictEqual", "notStrictEqual", "throws"]}