<?php
/**
 * NOTE: This class is auto generated by the swagger code generator program. 
 * https://github.com/swagger-api/swagger-codegen 
 * Do not edit the class manually.
 */

namespace SquareConnect\Api;

use \SquareConnect\Configuration;
use \SquareConnect\ApiClient;
use \SquareConnect\ApiException;
use \SquareConnect\ObjectSerializer;

/**
 * V1TransactionsApiTest Class Doc Comment
 *
 * @category Class
 * @package  SquareConnect
 * <AUTHOR> Inc.
 * @license  http://www.apache.org/licenses/LICENSE-2.0 Apache Licene v2
 * @link     https://squareup.com/developers
 */
class V1TransactionsApiTest extends \PHPUnit_Framework_TestCase
{

    /**
     * Setup before running each test case
     */
    public static function setUpBeforeClass() {

    }

    /**
     * Clean up after running each test case
     */
    public static function tearDownAfterClass() {

    }

    /**
     * Test case for createRefund
     *
     * Issues a refund for a previously processed payment. You must issue a refund within 60 days of the associated payment.
     *
     */
    public function test_createRefund() {

    }
    /**
     * Test case for listBankAccounts
     *
     * Provides non-confidential details for all of a location's associated bank accounts. This endpoint does not provide full bank account numbers, and there is no way to obtain a full bank account number with the Connect API.
     *
     */
    public function test_listBankAccounts() {

    }
    /**
     * Test case for listOrders
     *
     * Provides summary information for a merchant's online store orders.
     *
     */
    public function test_listOrders() {

    }
    /**
     * Test case for listPayments
     *
     * Provides summary information for all payments taken by a merchant or any of the merchant's mobile staff during a date range. Date ranges cannot exceed one year in length. See Date ranges for details of inclusive and exclusive dates.
     *
     */
    public function test_listPayments() {

    }
    /**
     * Test case for listRefunds
     *
     * Provides the details for all refunds initiated by a merchant or any of the merchant's mobile staff during a date range. Date ranges cannot exceed one year in length.
     *
     */
    public function test_listRefunds() {

    }
    /**
     * Test case for listSettlements
     *
     * Provides summary information for all deposits and withdrawals initiated by Square to a merchant's bank account during a date range. Date ranges cannot exceed one year in length.
     *
     */
    public function test_listSettlements() {

    }
    /**
     * Test case for retrieveBankAccount
     *
     * Provides non-confidential details for a merchant's associated bank account. This endpoint does not provide full bank account numbers, and there is no way to obtain a full bank account number with the Connect API.
     *
     */
    public function test_retrieveBankAccount() {

    }
    /**
     * Test case for retrieveOrder
     *
     * Provides comprehensive information for a single online store order, including the order's history.
     *
     */
    public function test_retrieveOrder() {

    }
    /**
     * Test case for retrievePayment
     *
     * Provides comprehensive information for a single payment.
     *
     */
    public function test_retrievePayment() {

    }
    /**
     * Test case for retrieveSettlement
     *
     * Provides comprehensive information for a single settlement, including the entries that contribute to the settlement's total.
     *
     */
    public function test_retrieveSettlement() {

    }
    /**
     * Test case for updateOrder
     *
     * Updates the details of an online store order. Every update you perform on an order corresponds to one of three actions:
     *
     */
    public function test_updateOrder() {

    }
}
