@media print {
  a[href]:after {
    content: none !important;
  }
}

.ajaxsalereportitems .order_total {
  text-align: right;
}
.ajaxsalereportitems .total_ammount {
  text-align: right;
}

.ajaxsalereportitems-footer .ajaxsalereportitems-fo-total-sale {
  text-align: right;
  font-size: 14px !important;
}
.ajaxsalereportitems-footer .fo-total-sale {
  text-align: right;
}

/* DataTable specific styles for respritbl */
#respritbl_wrapper {
  margin-top: 10px;
}

#respritbl_wrapper .dt-buttons {
  margin-bottom: 10px;
}

#respritbl_wrapper .dt-buttons .btn {
  margin-right: 5px;
  margin-bottom: 5px;
}

#respritbl_wrapper .dataTables_filter {
  margin-bottom: 10px;
}

#respritbl_wrapper .dataTables_length {
  margin-bottom: 10px;
}

#respritbl_wrapper .dataTables_info {
  padding-top: 8px;
}

#respritbl_wrapper .dataTables_paginate {
  padding-top: 8px;
}

/* Ensure table remains responsive */
#respritbl_wrapper .table-responsive {
  overflow-x: auto;
}

/* Maintain existing table styling */
#respritbl.dataTable {
  border-collapse: collapse !important;
}

#respritbl.dataTable thead th {
  border-bottom: 2px solid #dee2e6;
  vertical-align: bottom;
}

#respritbl.dataTable tbody td {
  vertical-align: middle;
}

/* Sort icons styling */
#respritbl.dataTable thead .sorting:before,
#respritbl.dataTable thead .sorting_asc:before,
#respritbl.dataTable thead .sorting_desc:before {
  right: 1em;
  content: "\2195";
  opacity: 0.2;
}

#respritbl.dataTable thead .sorting_asc:before {
  content: "\2191";
  opacity: 1;
}

#respritbl.dataTable thead .sorting_desc:before {
  content: "\2193";
  opacity: 1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  #respritbl_wrapper .dt-buttons {
    text-align: center;
  }

  #respritbl_wrapper .dt-buttons .btn {
    margin: 2px;
    font-size: 12px;
    padding: 4px 8px;
  }
}