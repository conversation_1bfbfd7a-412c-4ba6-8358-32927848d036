{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "33d88d1508ecebc3b495193b84696b8c", "packages": [{"name": "laminas/laminas-servicemanager", "version": "3.22.1", "source": {"type": "git", "url": "https://github.com/laminas/laminas-servicemanager.git", "reference": "de98d297d4743956a0558a6d71616979ff779328"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-servicemanager/zipball/de98d297d4743956a0558a6d71616979ff779328", "reference": "de98d297d4743956a0558a6d71616979ff779328", "shasum": ""}, "require": {"laminas/laminas-stdlib": "^3.17", "php": "~8.1.0 || ~8.2.0 || ~8.3.0", "psr/container": "^1.0"}, "conflict": {"ext-psr": "*", "laminas/laminas-code": "<4.10.0", "zendframework/zend-code": "<3.3.1", "zendframework/zend-servicemanager": "*"}, "provide": {"psr/container-implementation": "^1.0"}, "replace": {"container-interop/container-interop": "^1.2.0"}, "require-dev": {"composer/package-versions-deprecated": "^*********", "friendsofphp/proxy-manager-lts": "^1.0.14", "laminas/laminas-code": "^4.10.0", "laminas/laminas-coding-standard": "~2.5.0", "laminas/laminas-container-config-test": "^0.8", "mikey179/vfsstream": "^1.6.11", "phpbench/phpbench": "^1.2.9", "phpunit/phpunit": "^10.4", "psalm/plugin-phpunit": "^0.18.4", "vimeo/psalm": "^5.8.0"}, "suggest": {"friendsofphp/proxy-manager-lts": "ProxyManager ^2.1.1 to handle lazy initialization of services"}, "bin": ["bin/generate-deps-for-config-factory", "bin/generate-factory-for-class"], "type": "library", "autoload": {"files": ["src/autoload.php"], "psr-4": {"Laminas\\ServiceManager\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Factory-Driven Dependency Injection Container", "homepage": "https://laminas.dev", "keywords": ["PSR-11", "dependency-injection", "di", "dic", "laminas", "service-manager", "servicemanager"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-servicemanager/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-servicemanager/issues", "rss": "https://github.com/laminas/laminas-servicemanager/releases.atom", "source": "https://github.com/laminas/laminas-servicemanager"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2023-10-24T11:19:47+00:00"}, {"name": "laminas/laminas-stdlib", "version": "3.19.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-stdlib.git", "reference": "6a192dd0882b514e45506f533b833b623b78fff3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-stdlib/zipball/6a192dd0882b514e45506f533b833b623b78fff3", "reference": "6a192dd0882b514e45506f533b833b623b78fff3", "shasum": ""}, "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0"}, "conflict": {"zendframework/zend-stdlib": "*"}, "require-dev": {"laminas/laminas-coding-standard": "^2.5", "phpbench/phpbench": "^1.2.15", "phpunit/phpunit": "^10.5.8", "psalm/plugin-phpunit": "^0.18.4", "vimeo/psalm": "^5.20.0"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Stdlib\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "SPL extensions, array utilities, error handlers, and more", "homepage": "https://laminas.dev", "keywords": ["laminas", "stdlib"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-stdlib/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-stdlib/issues", "rss": "https://github.com/laminas/laminas-stdlib/releases.atom", "source": "https://github.com/laminas/laminas-stdlib"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-01-19T12:39:49+00:00"}, {"name": "psr/container", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/513e0666f7216c7459170d56df27dfcefe1689ea", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.2"}, "time": "2021-11-05T16:50:12+00:00"}, {"name": "psr/http-message", "version": "2.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "time": "2023-04-04T09:54:51+00:00"}], "packages-dev": [{"name": "amphp/amp", "version": "v2.6.4", "source": {"type": "git", "url": "https://github.com/amphp/amp.git", "reference": "ded3d9be08f526089eb7ee8d9f16a9768f9dec2d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/amp/zipball/ded3d9be08f526089eb7ee8d9f16a9768f9dec2d", "reference": "ded3d9be08f526089eb7ee8d9f16a9768f9dec2d", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"amphp/php-cs-fixer-config": "dev-master", "amphp/phpunit-util": "^1", "ext-json": "*", "jetbrains/phpstorm-stubs": "^2019.3", "phpunit/phpunit": "^7 | ^8 | ^9", "react/promise": "^2", "vimeo/psalm": "^3.12"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"files": ["lib/functions.php", "lib/Internal/functions.php"], "psr-4": {"Amp\\": "lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A non-blocking concurrency framework for PHP applications.", "homepage": "https://amphp.org/amp", "keywords": ["async", "asynchronous", "awaitable", "concurrency", "event", "event-loop", "future", "non-blocking", "promise"], "support": {"irc": "irc://irc.freenode.org/amphp", "issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v2.6.4"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2024-03-21T18:52:26+00:00"}, {"name": "amphp/byte-stream", "version": "v1.8.2", "source": {"type": "git", "url": "https://github.com/amphp/byte-stream.git", "reference": "4f0e968ba3798a423730f567b1b50d3441c16ddc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/byte-stream/zipball/4f0e968ba3798a423730f567b1b50d3441c16ddc", "reference": "4f0e968ba3798a423730f567b1b50d3441c16ddc", "shasum": ""}, "require": {"amphp/amp": "^2", "php": ">=7.1"}, "require-dev": {"amphp/php-cs-fixer-config": "dev-master", "amphp/phpunit-util": "^1.4", "friendsofphp/php-cs-fixer": "^2.3", "jetbrains/phpstorm-stubs": "^2019.3", "phpunit/phpunit": "^6 || ^7 || ^8", "psalm/phar": "^3.11.4"}, "type": "library", "autoload": {"files": ["lib/functions.php"], "psr-4": {"Amp\\ByteStream\\": "lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A stream abstraction to make working with non-blocking I/O simple.", "homepage": "https://amphp.org/byte-stream", "keywords": ["amp", "amphp", "async", "io", "non-blocking", "stream"], "support": {"issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v1.8.2"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2024-04-13T18:00:56+00:00"}, {"name": "composer/package-versions-deprecated", "version": "*********", "source": {"type": "git", "url": "https://github.com/composer/package-versions-deprecated.git", "reference": "b4f54f74ef3453349c24a845d22392cd31e65f1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/package-versions-deprecated/zipball/b4f54f74ef3453349c24a845d22392cd31e65f1d", "reference": "b4f54f74ef3453349c24a845d22392cd31e65f1d", "shasum": ""}, "require": {"composer-plugin-api": "^1.1.0 || ^2.0", "php": "^7 || ^8"}, "replace": {"ocramius/package-versions": "1.11.99"}, "require-dev": {"composer/composer": "^1.9.3 || ^2.0@dev", "ext-zip": "^1.13", "phpunit/phpunit": "^6.5 || ^7"}, "type": "composer-plugin", "extra": {"class": "PackageVersions\\Installer", "branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"PackageVersions\\": "src/PackageVersions"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "Composer plugin that provides efficient querying for installed package versions (no runtime IO)", "support": {"issues": "https://github.com/composer/package-versions-deprecated/issues", "source": "https://github.com/composer/package-versions-deprecated/tree/*********"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-01-17T14:14:24+00:00"}, {"name": "composer/pcre", "version": "3.2.0", "source": {"type": "git", "url": "https://github.com/composer/pcre.git", "reference": "ea4ab6f9580a4fd221e0418f2c357cdd39102a90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/pcre/zipball/ea4ab6f9580a4fd221e0418f2c357cdd39102a90", "reference": "ea4ab6f9580a4fd221e0418f2c357cdd39102a90", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "conflict": {"phpstan/phpstan": "<1.11.8"}, "require-dev": {"phpstan/phpstan": "^1.11.8", "phpstan/phpstan-strict-rules": "^1.1", "phpunit/phpunit": "^8 || ^9"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}, "phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "support": {"issues": "https://github.com/composer/pcre/issues", "source": "https://github.com/composer/pcre/tree/3.2.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-07-25T09:36:02+00:00"}, {"name": "composer/semver", "version": "3.4.2", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "c51258e759afdb17f1fd1fe83bc12baaef6309d6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/c51258e759afdb17f1fd1fe83bc12baaef6309d6", "reference": "c51258e759afdb17f1fd1fe83bc12baaef6309d6", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.4", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.4.2"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-07-12T11:35:52+00:00"}, {"name": "composer/xdebug-handler", "version": "3.0.5", "source": {"type": "git", "url": "https://github.com/composer/xdebug-handler.git", "reference": "6c1925561632e83d60a44492e0b344cf48ab85ef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/xdebug-handler/zipball/6c1925561632e83d60a44492e0b344cf48ab85ef", "reference": "6c1925561632e83d60a44492e0b344cf48ab85ef", "shasum": ""}, "require": {"composer/pcre": "^1 || ^2 || ^3", "php": "^7.2.5 || ^8.0", "psr/log": "^1 || ^2 || ^3"}, "require-dev": {"phpstan/phpstan": "^1.0", "phpstan/phpstan-strict-rules": "^1.1", "phpunit/phpunit": "^8.5 || ^9.6 || ^10.5"}, "type": "library", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/3.0.5"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-05-06T16:37:16+00:00"}, {"name": "dealerdirect/phpcodesniffer-composer-installer", "version": "v0.7.2", "source": {"type": "git", "url": "https://github.com/Dealerdirect/phpcodesniffer-composer-installer.git", "reference": "1c968e542d8843d7cd71de3c5c9c3ff3ad71a1db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Dealerdirect/phpcodesniffer-composer-installer/zipball/1c968e542d8843d7cd71de3c5c9c3ff3ad71a1db", "reference": "1c968e542d8843d7cd71de3c5c9c3ff3ad71a1db", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=5.3", "squizlabs/php_codesniffer": "^2.0 || ^3.1.0 || ^4.0"}, "require-dev": {"composer/composer": "*", "php-parallel-lint/php-parallel-lint": "^1.3.1", "phpcompatibility/php-compatibility": "^9.0"}, "type": "composer-plugin", "extra": {"class": "Dealerdirect\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\Plugin"}, "autoload": {"psr-4": {"Dealerdirect\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.frenck.nl", "role": "Developer / IT Manager"}, {"name": "Contributors", "homepage": "https://github.com/Dealerdirect/phpcodesniffer-composer-installer/graphs/contributors"}], "description": "PHP_CodeSniffer Standards Composer Installer Plugin", "homepage": "http://www.dealerdirect.com", "keywords": ["PHPCodeSniffer", "PHP_CodeSniffer", "code quality", "codesniffer", "composer", "installer", "phpcbf", "phpcs", "plugin", "qa", "quality", "standard", "standards", "style guide", "stylecheck", "tests"], "support": {"issues": "https://github.com/dealerdirect/phpcodesniffer-composer-installer/issues", "source": "https://github.com/dealerdirect/phpcodesniffer-composer-installer"}, "time": "2022-02-04T12:51:07+00:00"}, {"name": "dnoegel/php-xdg-base-dir", "version": "v0.1.1", "source": {"type": "git", "url": "https://github.com/dnoegel/php-xdg-base-dir.git", "reference": "8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dnoegel/php-xdg-base-dir/zipball/8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd", "reference": "8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "~7.0|~6.0|~5.0|~4.8.35"}, "type": "library", "autoload": {"psr-4": {"XdgBaseDir\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "implementation of xdg base directory specification for php", "support": {"issues": "https://github.com/dnoegel/php-xdg-base-dir/issues", "source": "https://github.com/dnoegel/php-xdg-base-dir/tree/v0.1.1"}, "time": "2019-12-04T15:06:13+00:00"}, {"name": "felixfbecker/advanced-json-rpc", "version": "v3.2.1", "source": {"type": "git", "url": "https://github.com/felixfbecker/php-advanced-json-rpc.git", "reference": "b5f37dbff9a8ad360ca341f3240dc1c168b45447"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/felixfbecker/php-advanced-json-rpc/zipball/b5f37dbff9a8ad360ca341f3240dc1c168b45447", "reference": "b5f37dbff9a8ad360ca341f3240dc1c168b45447", "shasum": ""}, "require": {"netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "php": "^7.1 || ^8.0", "phpdocumentor/reflection-docblock": "^4.3.4 || ^5.0.0"}, "require-dev": {"phpunit/phpunit": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"AdvancedJsonRpc\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["ISC"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A more advanced JSONRPC implementation", "support": {"issues": "https://github.com/felixfbecker/php-advanced-json-rpc/issues", "source": "https://github.com/felixfbecker/php-advanced-json-rpc/tree/v3.2.1"}, "time": "2021-06-11T22:34:44+00:00"}, {"name": "felixfbecker/language-server-protocol", "version": "v1.5.2", "source": {"type": "git", "url": "https://github.com/felixfbecker/php-language-server-protocol.git", "reference": "6e82196ffd7c62f7794d778ca52b69feec9f2842"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/felixfbecker/php-language-server-protocol/zipball/6e82196ffd7c62f7794d778ca52b69feec9f2842", "reference": "6e82196ffd7c62f7794d778ca52b69feec9f2842", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpstan/phpstan": "*", "squizlabs/php_codesniffer": "^3.1", "vimeo/psalm": "^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"LanguageServerProtocol\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["ISC"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP classes for the Language Server Protocol", "keywords": ["language", "microsoft", "php", "server"], "support": {"issues": "https://github.com/felixfbecker/php-language-server-protocol/issues", "source": "https://github.com/felixfbecker/php-language-server-protocol/tree/v1.5.2"}, "time": "2022-03-02T22:36:06+00:00"}, {"name": "fidry/cpu-core-counter", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/theofidry/cpu-core-counter.git", "reference": "f92996c4d5c1a696a6a970e20f7c4216200fcc42"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theofidry/cpu-core-counter/zipball/f92996c4d5c1a696a6a970e20f7c4216200fcc42", "reference": "f92996c4d5c1a696a6a970e20f7c4216200fcc42", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"fidry/makefile": "^0.2.0", "fidry/php-cs-fixer-config": "^1.1.2", "phpstan/extension-installer": "^1.2.0", "phpstan/phpstan": "^1.9.2", "phpstan/phpstan-deprecation-rules": "^1.0.0", "phpstan/phpstan-phpunit": "^1.2.2", "phpstan/phpstan-strict-rules": "^1.4.4", "phpunit/phpunit": "^8.5.31 || ^9.5.26", "webmozarts/strict-phpunit": "^7.5"}, "type": "library", "autoload": {"psr-4": {"Fidry\\CpuCoreCounter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Théo FIDRY", "email": "<EMAIL>"}], "description": "Tiny utility to get the number of CPU cores.", "keywords": ["CPU", "core"], "support": {"issues": "https://github.com/theofidry/cpu-core-counter/issues", "source": "https://github.com/theofidry/cpu-core-counter/tree/1.1.0"}, "funding": [{"url": "https://github.com/theofidry", "type": "github"}], "time": "2024-02-07T09:43:46+00:00"}, {"name": "laminas/laminas-coding-standard", "version": "2.5.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-coding-standard.git", "reference": "c1aaa18a7c860c6932677a3e4ec13161f9fc7d61"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-coding-standard/zipball/c1aaa18a7c860c6932677a3e4ec13161f9fc7d61", "reference": "c1aaa18a7c860c6932677a3e4ec13161f9fc7d61", "shasum": ""}, "require": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7 || ^1.0", "php": "^7.4 || ^8.0", "slevomat/coding-standard": "^7.0", "squizlabs/php_codesniffer": "^3.6", "webimpress/coding-standard": "^1.2"}, "conflict": {"phpstan/phpdoc-parser": ">=1.6.0"}, "type": "phpcodesniffer-standard", "autoload": {"psr-4": {"LaminasCodingStandard\\": "src/LaminasCodingStandard/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Laminas Coding Standard", "homepage": "https://laminas.dev", "keywords": ["Coding Standard", "laminas"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-coding-standard/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-coding-standard/issues", "rss": "https://github.com/laminas/laminas-coding-standard/releases.atom", "source": "https://github.com/laminas/laminas-coding-standard"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2023-01-05T15:53:40+00:00"}, {"name": "laminas/laminas-db", "version": "2.20.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-db.git", "reference": "207b9ee70a8b518913c1fad688d7a64fe89a8b91"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-db/zipball/207b9ee70a8b518913c1fad688d7a64fe89a8b91", "reference": "207b9ee70a8b518913c1fad688d7a64fe89a8b91", "shasum": ""}, "require": {"laminas/laminas-stdlib": "^3.7.1", "php": "~8.1.0 || ~8.2.0 || ~8.3.0"}, "conflict": {"zendframework/zend-db": "*"}, "require-dev": {"laminas/laminas-coding-standard": "^2.4.0", "laminas/laminas-eventmanager": "^3.6.0", "laminas/laminas-hydrator": "^4.7", "laminas/laminas-servicemanager": "^3.19.0", "phpunit/phpunit": "^9.5.25"}, "suggest": {"laminas/laminas-eventmanager": "Laminas\\EventManager component", "laminas/laminas-hydrator": "(^3.2 || ^4.3) Laminas\\Hydrator component for using HydratingResultSets", "laminas/laminas-servicemanager": "Laminas\\ServiceManager component"}, "type": "library", "extra": {"laminas": {"component": "Laminas\\Db", "config-provider": "Laminas\\Db\\ConfigProvider"}}, "autoload": {"psr-4": {"Laminas\\Db\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Database abstraction layer, SQL abstraction, result set abstraction, and RowDataGateway and TableDataGateway implementations", "homepage": "https://laminas.dev", "keywords": ["db", "laminas"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-db/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-db/issues", "rss": "https://github.com/laminas/laminas-db/releases.atom", "source": "https://github.com/laminas/laminas-db"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-04-02T01:04:56+00:00"}, {"name": "laminas/laminas-escaper", "version": "2.13.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-escaper.git", "reference": "af459883f4018d0f8a0c69c7a209daef3bf973ba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-escaper/zipball/af459883f4018d0f8a0c69c7a209daef3bf973ba", "reference": "af459883f4018d0f8a0c69c7a209daef3bf973ba", "shasum": ""}, "require": {"ext-ctype": "*", "ext-mbstring": "*", "php": "~8.1.0 || ~8.2.0 || ~8.3.0"}, "conflict": {"zendframework/zend-escaper": "*"}, "require-dev": {"infection/infection": "^0.27.0", "laminas/laminas-coding-standard": "~2.5.0", "maglnet/composer-require-checker": "^3.8.0", "phpunit/phpunit": "^9.6.7", "psalm/plugin-phpunit": "^0.18.4", "vimeo/psalm": "^5.9"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Escaper\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Securely and safely escape HTML, HTML attributes, JavaScript, CSS, and URLs", "homepage": "https://laminas.dev", "keywords": ["escaper", "laminas"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-escaper/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-escaper/issues", "rss": "https://github.com/laminas/laminas-escaper/releases.atom", "source": "https://github.com/laminas/laminas-escaper"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2023-10-10T08:35:13+00:00"}, {"name": "laminas/laminas-eventmanager", "version": "3.13.1", "source": {"type": "git", "url": "https://github.com/laminas/laminas-eventmanager.git", "reference": "933d1b5cf03fa4cf3016cebfd0555fa2ba3f2024"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-eventmanager/zipball/933d1b5cf03fa4cf3016cebfd0555fa2ba3f2024", "reference": "933d1b5cf03fa4cf3016cebfd0555fa2ba3f2024", "shasum": ""}, "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0"}, "conflict": {"container-interop/container-interop": "<1.2", "zendframework/zend-eventmanager": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~2.5.0", "laminas/laminas-stdlib": "^3.18", "phpbench/phpbench": "^1.2.15", "phpunit/phpunit": "^10.5.5", "psalm/plugin-phpunit": "^0.18.4", "psr/container": "^1.1.2 || ^2.0.2", "vimeo/psalm": "^5.18"}, "suggest": {"laminas/laminas-stdlib": "^2.7.3 || ^3.0, to use the FilterChain feature", "psr/container": "^1.1.2 || ^2.0.2, to use the lazy listeners feature"}, "type": "library", "autoload": {"psr-4": {"Laminas\\EventManager\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Trigger and listen to events within a PHP application", "homepage": "https://laminas.dev", "keywords": ["event", "eventmanager", "events", "laminas"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-eventmanager/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-eventmanager/issues", "rss": "https://github.com/laminas/laminas-eventmanager/releases.atom", "source": "https://github.com/laminas/laminas-eventmanager"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-06-24T14:01:06+00:00"}, {"name": "laminas/laminas-filter", "version": "2.36.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-filter.git", "reference": "307afc21ada0648e84cdcf9e14cd84bd43ee9d13"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-filter/zipball/307afc21ada0648e84cdcf9e14cd84bd43ee9d13", "reference": "307afc21ada0648e84cdcf9e14cd84bd43ee9d13", "shasum": ""}, "require": {"ext-mbstring": "*", "laminas/laminas-servicemanager": "^3.21.0", "laminas/laminas-stdlib": "^3.13.0", "php": "~8.1.0 || ~8.2.0 || ~8.3.0"}, "conflict": {"laminas/laminas-validator": "<2.10.1", "zendframework/zend-filter": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~2.5.0", "laminas/laminas-crypt": "^3.11", "laminas/laminas-i18n": "^2.26.0", "laminas/laminas-uri": "^2.11", "pear/archive_tar": "^1.5.0", "phpunit/phpunit": "^10.5.20", "psalm/plugin-phpunit": "^0.19.0", "psr/http-factory": "^1.1.0", "vimeo/psalm": "^5.24.0"}, "suggest": {"laminas/laminas-crypt": "Laminas\\Crypt component, for encryption filters", "laminas/laminas-i18n": "Laminas\\I18n component for filters depending on i18n functionality", "laminas/laminas-uri": "Laminas\\Uri component, for the UriNormalize filter", "psr/http-factory-implementation": "psr/http-factory-implementation, for creating file upload instances when consuming PSR-7 in file upload filters"}, "type": "library", "extra": {"laminas": {"component": "Laminas\\Filter", "config-provider": "Laminas\\Filter\\ConfigProvider"}}, "autoload": {"psr-4": {"Laminas\\Filter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Programmatically filter and normalize data and files", "homepage": "https://laminas.dev", "keywords": ["filter", "laminas"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-filter/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-filter/issues", "rss": "https://github.com/laminas/laminas-filter/releases.atom", "source": "https://github.com/laminas/laminas-filter"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-06-13T10:31:36+00:00"}, {"name": "laminas/laminas-i18n", "version": "2.28.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-i18n.git", "reference": "e1e312650232e5ef26c28ea08f3c4c18633f48c3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-i18n/zipball/e1e312650232e5ef26c28ea08f3c4c18633f48c3", "reference": "e1e312650232e5ef26c28ea08f3c4c18633f48c3", "shasum": ""}, "require": {"ext-intl": "*", "laminas/laminas-servicemanager": "^3.21.0", "laminas/laminas-stdlib": "^3.0", "laminas/laminas-translator": "^1.0", "php": "~8.1.0 || ~8.2.0 || ~8.3.0"}, "conflict": {"laminas/laminas-view": "<2.20.0", "zendframework/zend-i18n": "*"}, "require-dev": {"laminas/laminas-cache": "^3.12.1", "laminas/laminas-cache-storage-adapter-memory": "^2.3.0", "laminas/laminas-cache-storage-deprecated-factory": "^1.2", "laminas/laminas-coding-standard": "~2.5.0", "laminas/laminas-config": "^3.9.0", "laminas/laminas-eventmanager": "^3.13", "laminas/laminas-filter": "^2.34", "laminas/laminas-validator": "^2.49", "laminas/laminas-view": "^2.34", "phpunit/phpunit": "^10.5.11", "psalm/plugin-phpunit": "^0.19.0", "vimeo/psalm": "^5.22.2"}, "suggest": {"laminas/laminas-cache": "You should install this package to cache the translations", "laminas/laminas-config": "You should install this package to use the INI translation format", "laminas/laminas-eventmanager": "You should install this package to use the events in the translator", "laminas/laminas-filter": "You should install this package to use the provided filters", "laminas/laminas-i18n-resources": "This package provides validator and captcha translations", "laminas/laminas-validator": "You should install this package to use the provided validators", "laminas/laminas-view": "You should install this package to use the provided view helpers"}, "type": "library", "extra": {"laminas": {"component": "Laminas\\I18n", "config-provider": "Laminas\\I18n\\ConfigProvider"}}, "autoload": {"psr-4": {"Laminas\\I18n\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Provide translations for your application, and filter and validate internationalized values", "homepage": "https://laminas.dev", "keywords": ["i18n", "laminas"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-i18n/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-i18n/issues", "rss": "https://github.com/laminas/laminas-i18n/releases.atom", "source": "https://github.com/laminas/laminas-i18n"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-07-15T12:54:14+00:00"}, {"name": "laminas/laminas-session", "version": "2.21.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-session.git", "reference": "b8cd890f7682a255b335c2ca45df9a7cbc58873d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-session/zipball/b8cd890f7682a255b335c2ca45df9a7cbc58873d", "reference": "b8cd890f7682a255b335c2ca45df9a7cbc58873d", "shasum": ""}, "require": {"laminas/laminas-eventmanager": "^3.12", "laminas/laminas-servicemanager": "^3.22", "laminas/laminas-stdlib": "^3.18", "php": "~8.1.0 || ~8.2.0 || ~8.3.0"}, "conflict": {"zendframework/zend-session": "*"}, "require-dev": {"ext-xdebug": "*", "laminas/laminas-cache": "^3.12.2", "laminas/laminas-cache-storage-adapter-memory": "^2.3", "laminas/laminas-coding-standard": "~2.5.0", "laminas/laminas-db": "^2.20.0", "laminas/laminas-http": "^2.19", "laminas/laminas-validator": "^2.57.0", "mongodb/mongodb": "~1.17.1", "phpunit/phpunit": "^9.6.19", "psalm/plugin-phpunit": "^0.19.0", "vimeo/psalm": "^5.24.0"}, "suggest": {"laminas/laminas-cache": "Laminas\\Cache component", "laminas/laminas-db": "Laminas\\Db component", "laminas/laminas-http": "Laminas\\Http component", "laminas/laminas-servicemanager": "Laminas\\ServiceManager component", "laminas/laminas-validator": "Laminas\\Validator component", "mongodb/mongodb": "If you want to use the MongoDB session save handler"}, "type": "library", "extra": {"laminas": {"component": "Laminas\\Session", "config-provider": "Laminas\\Session\\ConfigProvider"}}, "autoload": {"psr-4": {"Laminas\\Session\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Object-oriented interface to PHP sessions and storage", "homepage": "https://laminas.dev", "keywords": ["laminas", "session"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-session/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-session/issues", "rss": "https://github.com/laminas/laminas-session/releases.atom", "source": "https://github.com/laminas/laminas-session"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-06-19T14:36:45+00:00"}, {"name": "laminas/laminas-translator", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-translator.git", "reference": "86d176c01a96b0ef205192b776cb69e8d4ca06b1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-translator/zipball/86d176c01a96b0ef205192b776cb69e8d4ca06b1", "reference": "86d176c01a96b0ef205192b776cb69e8d4ca06b1", "shasum": ""}, "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0"}, "require-dev": {"laminas/laminas-coding-standard": "~2.5.0", "vimeo/psalm": "^5.24.0"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Translator\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Interfaces for the Translator component of laminas-i18n", "homepage": "https://laminas.dev", "keywords": ["i18n", "laminas"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-i18n/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-translator/issues", "rss": "https://github.com/laminas/laminas-translator/releases.atom", "source": "https://github.com/laminas/laminas-translator"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-06-18T15:09:24+00:00"}, {"name": "laminas/laminas-uri", "version": "2.11.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-uri.git", "reference": "e662c685125061d3115906e5eb30f966842cc226"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-uri/zipball/e662c685125061d3115906e5eb30f966842cc226", "reference": "e662c685125061d3115906e5eb30f966842cc226", "shasum": ""}, "require": {"laminas/laminas-escaper": "^2.9", "laminas/laminas-validator": "^2.39", "php": "~8.1.0 || ~8.2.0 || ~8.3.0"}, "conflict": {"zendframework/zend-uri": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~2.4.0", "phpunit/phpunit": "^9.5.25"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Uri\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "A component that aids in manipulating and validating » Uniform Resource Identifiers (URIs)", "homepage": "https://laminas.dev", "keywords": ["laminas", "uri"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-uri/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-uri/issues", "rss": "https://github.com/laminas/laminas-uri/releases.atom", "source": "https://github.com/laminas/laminas-uri"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2023-10-18T09:56:55+00:00"}, {"name": "myclabs/deep-copy", "version": "1.12.0", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "3a6b9a42cd8f8771bd4295d13e1423fa7f3d942c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/3a6b9a42cd8f8771bd4295d13e1423fa7f3d942c", "reference": "3a6b9a42cd8f8771bd4295d13e1423fa7f3d942c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3 <3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpspec/prophecy": "^1.10", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.12.0"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2024-06-12T14:39:25+00:00"}, {"name": "netresearch/jsonmapper", "version": "v4.4.1", "source": {"type": "git", "url": "https://github.com/cweiske/jsonmapper.git", "reference": "132c75c7dd83e45353ebb9c6c9f591952995bbf0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cweiske/jsonmapper/zipball/132c75c7dd83e45353ebb9c6c9f591952995bbf0", "reference": "132c75c7dd83e45353ebb9c6c9f591952995bbf0", "shasum": ""}, "require": {"ext-json": "*", "ext-pcre": "*", "ext-reflection": "*", "ext-spl": "*", "php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "~7.5 || ~8.0 || ~9.0 || ~10.0", "squizlabs/php_codesniffer": "~3.5"}, "type": "library", "autoload": {"psr-0": {"JsonMapper": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["OSL-3.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://github.com/cweiske/jsonmapper/", "role": "Developer"}], "description": "Map nested JSON structures onto PHP classes", "support": {"email": "<EMAIL>", "issues": "https://github.com/cweiske/jsonmapper/issues", "source": "https://github.com/cweiske/jsonmapper/tree/v4.4.1"}, "time": "2024-01-31T06:18:54+00:00"}, {"name": "nikic/php-parser", "version": "v4.19.1", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "4e1b88d21c69391150ace211e9eaf05810858d0b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/4e1b88d21c69391150ace211e9eaf05810858d0b", "reference": "4e1b88d21c69391150ace211e9eaf05810858d0b", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.1"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^6.5 || ^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v4.19.1"}, "time": "2024-03-17T08:10:35+00:00"}, {"name": "phar-io/manifest", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "54750ef60c58e43759730615a392c31c80e23176"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/54750ef60c58e43759730615a392c31c80e23176", "reference": "54750ef60c58e43759730615a392c31c80e23176", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1", "php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/2.0.4"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:33:53+00:00"}, {"name": "phar-io/version", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.2.1"}, "time": "2022-02-21T01:04:05+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/2.x"}, "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.3.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "622548b623e81ca6d78b721c5e029f4ce664f170"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/622548b623e81ca6d78b721c5e029f4ce664f170", "reference": "622548b623e81ca6d78b721c5e029f4ce664f170", "shasum": ""}, "require": {"ext-filter": "*", "php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.2", "phpdocumentor/type-resolver": "^1.3", "webmozart/assert": "^1.9.1"}, "require-dev": {"mockery/mockery": "~1.3.2", "psalm/phar": "^4.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.3.0"}, "time": "2021-10-19T17:43:47+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.6.2", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "48f445a408c131e38cab1c235aa6d2bb7a0bb20d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/48f445a408c131e38cab1c235aa6d2bb7a0bb20d", "reference": "48f445a408c131e38cab1c235aa6d2bb7a0bb20d", "shasum": ""}, "require": {"php": "^7.4 || ^8.0", "phpdocumentor/reflection-common": "^2.0"}, "require-dev": {"ext-tokenizer": "*", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^9.5", "rector/rector": "^0.13.9", "vimeo/psalm": "^4.25"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.6.2"}, "time": "2022-10-14T12:47:21+00:00"}, {"name": "phpstan/phpdoc-parser", "version": "1.5.1", "source": {"type": "git", "url": "https://github.com/phpstan/phpdoc-parser.git", "reference": "981cc368a216c988e862a75e526b6076987d1b50"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/981cc368a216c988e862a75e526b6076987d1b50", "reference": "981cc368a216c988e862a75e526b6076987d1b50", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.5", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/phpunit": "^9.5", "symfony/process": "^5.2"}, "type": "library", "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPDoc parser with support for nullable, intersection and generic types", "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.5.1"}, "time": "2022-05-05T11:32:40+00:00"}, {"name": "phpunit/php-code-coverage", "version": "10.1.15", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "5da8b1728acd1e6ffdf2ff32ffbdfd04307f26ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/5da8b1728acd1e6ffdf2ff32ffbdfd04307f26ae", "reference": "5da8b1728acd1e6ffdf2ff32ffbdfd04307f26ae", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.18 || ^5.0", "php": ">=8.1", "phpunit/php-file-iterator": "^4.0", "phpunit/php-text-template": "^3.0", "sebastian/code-unit-reverse-lookup": "^3.0", "sebastian/complexity": "^3.0", "sebastian/environment": "^6.0", "sebastian/lines-of-code": "^2.0", "sebastian/version": "^4.0", "theseer/tokenizer": "^1.2.0"}, "require-dev": {"phpunit/phpunit": "^10.1"}, "suggest": {"ext-pcov": "PHP extension that provides line coverage", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "type": "library", "extra": {"branch-alias": {"dev-main": "10.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/10.1.15"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-06-29T08:25:15+00:00"}, {"name": "phpunit/php-file-iterator", "version": "4.1.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "a95037b6d9e608ba092da1b23931e537cadc3c3c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/a95037b6d9e608ba092da1b23931e537cadc3c3c", "reference": "a95037b6d9e608ba092da1b23931e537cadc3c3c", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/4.1.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-08-31T06:24:48+00:00"}, {"name": "phpunit/php-invoker", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "reference": "f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7", "reference": "f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^10.0"}, "suggest": {"ext-pcntl": "*"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Invoke callables with a timeout", "homepage": "https://github.com/sebastian<PERSON>mann/php-invoker/", "keywords": ["process"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/4.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:56:09+00:00"}, {"name": "phpunit/php-text-template", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "0c7b06ff49e3d5072f057eb1fa59258bf287a748"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-text-template/zipball/0c7b06ff49e3d5072f057eb1fa59258bf287a748", "reference": "0c7b06ff49e3d5072f057eb1fa59258bf287a748", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/3.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-08-31T14:07:24+00:00"}, {"name": "phpunit/php-timer", "version": "6.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "e2a2d67966e740530f4a3343fe2e030ffdc1161d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-timer/zipball/e2a2d67966e740530f4a3343fe2e030ffdc1161d", "reference": "e2a2d67966e740530f4a3343fe2e030ffdc1161d", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-timer/tree/6.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:57:52+00:00"}, {"name": "phpunit/phpunit", "version": "10.5.29", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "8e9e80872b4e8064401788ee8a32d40b4455318f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/8e9e80872b4e8064401788ee8a32d40b4455318f", "reference": "8e9e80872b4e8064401788ee8a32d40b4455318f", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.12.0", "phar-io/manifest": "^2.0.4", "phar-io/version": "^3.2.1", "php": ">=8.1", "phpunit/php-code-coverage": "^10.1.15", "phpunit/php-file-iterator": "^4.1.0", "phpunit/php-invoker": "^4.0.0", "phpunit/php-text-template": "^3.0.1", "phpunit/php-timer": "^6.0.0", "sebastian/cli-parser": "^2.0.1", "sebastian/code-unit": "^2.0.0", "sebastian/comparator": "^5.0.1", "sebastian/diff": "^5.1.1", "sebastian/environment": "^6.1.0", "sebastian/exporter": "^5.1.2", "sebastian/global-state": "^6.0.2", "sebastian/object-enumerator": "^5.0.0", "sebastian/recursion-context": "^5.0.0", "sebastian/type": "^4.0.0", "sebastian/version": "^4.0.1"}, "suggest": {"ext-soap": "To be able to generate mocks based on WSDL files"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-main": "10.5-dev"}}, "autoload": {"files": ["src/Framework/Assert/Functions.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "security": "https://github.com/sebastian<PERSON>mann/phpunit/security/policy", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/10.5.29"}, "funding": [{"url": "https://phpunit.de/sponsors.html", "type": "custom"}, {"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpunit/phpunit", "type": "tidelift"}], "time": "2024-07-30T11:08:00+00:00"}, {"name": "psalm/plugin-phpunit", "version": "0.19.0", "source": {"type": "git", "url": "https://github.com/psalm/psalm-plugin-phpunit.git", "reference": "e344eaaa27871e79c6cb97b9efe52a735f9d1966"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/psalm/psalm-plugin-phpunit/zipball/e344eaaa27871e79c6cb97b9efe52a735f9d1966", "reference": "e344eaaa27871e79c6cb97b9efe52a735f9d1966", "shasum": ""}, "require": {"composer/package-versions-deprecated": "^1.10", "composer/semver": "^1.4 || ^2.0 || ^3.0", "ext-simplexml": "*", "php": "^7.4 || ^8.0", "vimeo/psalm": "dev-master || ^5@beta || ^5.0"}, "conflict": {"phpunit/phpunit": "<7.5"}, "require-dev": {"codeception/codeception": "^4.0.3", "php": "^7.3 || ^8.0", "phpunit/phpunit": "^7.5 || ^8.0 || ^9.0", "squizlabs/php_codesniffer": "^3.3.1", "weirdan/codeception-psalm-module": "^0.11.0", "weirdan/prophecy-shim": "^1.0 || ^2.0"}, "type": "psalm-plugin", "extra": {"psalm": {"pluginClass": "Psalm\\PhpUnitPlugin\\Plugin"}}, "autoload": {"psr-4": {"Psalm\\PhpUnitPlugin\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Psalm plugin for PHPUnit", "support": {"issues": "https://github.com/psalm/psalm-plugin-phpunit/issues", "source": "https://github.com/psalm/psalm-plugin-phpunit/tree/0.19.0"}, "time": "2024-03-15T10:43:15+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/log", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "fe5ea303b0887d5caefd3d431c3e61ad47037001"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/fe5ea303b0887d5caefd3d431c3e61ad47037001", "reference": "fe5ea303b0887d5caefd3d431c3e61ad47037001", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.0"}, "time": "2021-07-14T16:46:02+00:00"}, {"name": "sebastian/cli-parser", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "reference": "c34583b87e7b7a8055bf6c450c2c77ce32a24084"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/c34583b87e7b7a8055bf6c450c2c77ce32a24084", "reference": "c34583b87e7b7a8055bf6c450c2c77ce32a24084", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for parsing CLI options", "homepage": "https://github.com/sebastian<PERSON>mann/cli-parser", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/2.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:12:49+00:00"}, {"name": "sebastian/code-unit", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "reference": "a81fee9eef0b7a76af11d121767abc44c104e503"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/a81fee9eef0b7a76af11d121767abc44c104e503", "reference": "a81fee9eef0b7a76af11d121767abc44c104e503", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/code-unit", "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/2.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:58:43+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "5e3a687f7d8ae33fb362c5c0743794bbb2420a1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/5e3a687f7d8ae33fb362c5c0743794bbb2420a1d", "reference": "5e3a687f7d8ae33fb362c5c0743794bbb2420a1d", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/3.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:59:15+00:00"}, {"name": "sebastian/comparator", "version": "5.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "2db5010a484d53ebf536087a70b4a5423c102372"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/2db5010a484d53ebf536087a70b4a5423c102372", "reference": "2db5010a484d53ebf536087a70b4a5423c102372", "shasum": ""}, "require": {"ext-dom": "*", "ext-mbstring": "*", "php": ">=8.1", "sebastian/diff": "^5.0", "sebastian/exporter": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.3"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/5.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-08-14T13:18:12+00:00"}, {"name": "sebastian/complexity", "version": "3.2.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/complexity.git", "reference": "68ff824baeae169ec9f2137158ee529584553799"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/complexity/zipball/68ff824baeae169ec9f2137158ee529584553799", "reference": "68ff824baeae169ec9f2137158ee529584553799", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for calculating the complexity of PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/complexity", "support": {"issues": "https://github.com/sebastian<PERSON>mann/complexity/issues", "security": "https://github.com/sebastian<PERSON>mann/complexity/security/policy", "source": "https://github.com/sebastian<PERSON>mann/complexity/tree/3.2.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-12-21T08:37:17+00:00"}, {"name": "sebastian/diff", "version": "5.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "c41e007b4b62af48218231d6c2275e4c9b975b2e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/diff/zipball/c41e007b4b62af48218231d6c2275e4c9b975b2e", "reference": "c41e007b4b62af48218231d6c2275e4c9b975b2e", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0", "symfony/process": "^6.4"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "security": "https://github.com/sebastian<PERSON>mann/diff/security/policy", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/5.1.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:15:17+00:00"}, {"name": "sebastian/environment", "version": "6.1.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "8074dbcd93529b357029f5cc5058fd3e43666984"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastianbergmann/environment/zipball/8074dbcd93529b357029f5cc5058fd3e43666984", "reference": "8074dbcd93529b357029f5cc5058fd3e43666984", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "https://github.com/sebastian<PERSON>mann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/environment/security/policy", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/6.1.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-23T08:47:14+00:00"}, {"name": "sebastian/exporter", "version": "5.1.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "955288482d97c19a372d3f31006ab3f37da47adf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/955288482d97c19a372d3f31006ab3f37da47adf", "reference": "955288482d97c19a372d3f31006ab3f37da47adf", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=8.1", "sebastian/recursion-context": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "https://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "security": "https://github.com/sebastian<PERSON>mann/exporter/security/policy", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/5.1.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:17:12+00:00"}, {"name": "sebastian/global-state", "version": "6.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "987bafff24ecc4c9ac418cab1145b96dd6e9cbd9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/987bafff24ecc4c9ac418cab1145b96dd6e9cbd9", "reference": "987bafff24ecc4c9ac418cab1145b96dd6e9cbd9", "shasum": ""}, "require": {"php": ">=8.1", "sebastian/object-reflector": "^3.0", "sebastian/recursion-context": "^5.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "https://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/global-state/security/policy", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/6.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:19:19+00:00"}, {"name": "sebastian/lines-of-code", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "reference": "856e7f6a75a84e339195d48c556f23be2ebf75d0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/lines-of-code/zipball/856e7f6a75a84e339195d48c556f23be2ebf75d0", "reference": "856e7f6a75a84e339195d48c556f23be2ebf75d0", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for counting the lines of code in PHP source code", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/2.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-12-21T08:38:20+00:00"}, {"name": "sebastian/object-enumerator", "version": "5.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "202d0e344a580d7f7d04b3fafce6933e59dae906"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/202d0e344a580d7f7d04b3fafce6933e59dae906", "reference": "202d0e344a580d7f7d04b3fafce6933e59dae906", "shasum": ""}, "require": {"php": ">=8.1", "sebastian/object-reflector": "^3.0", "sebastian/recursion-context": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/5.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:08:32+00:00"}, {"name": "sebastian/object-reflector", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "24ed13d98130f0e7122df55d06c5c4942a577957"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/object-reflector/zipball/24ed13d98130f0e7122df55d06c5c4942a577957", "reference": "24ed13d98130f0e7122df55d06c5c4942a577957", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/3.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:06:18+00:00"}, {"name": "sebastian/recursion-context", "version": "5.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "05909fb5bc7df4c52992396d0116aed689f93712"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/05909fb5bc7df4c52992396d0116aed689f93712", "reference": "05909fb5bc7df4c52992396d0116aed689f93712", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/5.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:05:40+00:00"}, {"name": "sebastian/type", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "462699a16464c3944eefc02ebdd77882bd3925bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/type/zipball/462699a16464c3944eefc02ebdd77882bd3925bf", "reference": "462699a16464c3944eefc02ebdd77882bd3925bf", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "support": {"issues": "https://github.com/sebastian<PERSON>mann/type/issues", "source": "https://github.com/sebastian<PERSON>mann/type/tree/4.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:10:45+00:00"}, {"name": "sebastian/version", "version": "4.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "c51fa83a5d8f43f1402e3f32a005e6262244ef17"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/c51fa83a5d8f43f1402e3f32a005e6262244ef17", "reference": "c51fa83a5d8f43f1402e3f32a005e6262244ef17", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/4.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-07T11:34:05+00:00"}, {"name": "slevomat/coding-standard", "version": "7.2.1", "source": {"type": "git", "url": "https://github.com/slevomat/coding-standard.git", "reference": "aff06ae7a84e4534bf6f821dc982a93a5d477c90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/slevomat/coding-standard/zipball/aff06ae7a84e4534bf6f821dc982a93a5d477c90", "reference": "aff06ae7a84e4534bf6f821dc982a93a5d477c90", "shasum": ""}, "require": {"dealerdirect/phpcodesniffer-composer-installer": "^0.6.2 || ^0.7", "php": "^7.2 || ^8.0", "phpstan/phpdoc-parser": "^1.5.1", "squizlabs/php_codesniffer": "^3.6.2"}, "require-dev": {"phing/phing": "2.17.3", "php-parallel-lint/php-parallel-lint": "1.3.2", "phpstan/phpstan": "1.4.10|1.7.1", "phpstan/phpstan-deprecation-rules": "1.0.0", "phpstan/phpstan-phpunit": "1.0.0|1.1.1", "phpstan/phpstan-strict-rules": "1.2.3", "phpunit/phpunit": "7.5.20|8.5.21|9.5.20"}, "type": "phpcodesniffer-standard", "extra": {"branch-alias": {"dev-master": "7.x-dev"}}, "autoload": {"psr-4": {"SlevomatCodingStandard\\": "SlevomatCodingStandard"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Slevomat Coding Standard for PHP_CodeSniffer complements Consistence Coding Standard by providing sniffs with additional checks.", "support": {"issues": "https://github.com/slevomat/coding-standard/issues", "source": "https://github.com/slevomat/coding-standard/tree/7.2.1"}, "funding": [{"url": "https://github.com/kukulich", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/slevomat/coding-standard", "type": "tidelift"}], "time": "2022-05-25T10:58:12+00:00"}, {"name": "spatie/array-to-xml", "version": "3.3.0", "source": {"type": "git", "url": "https://github.com/spatie/array-to-xml.git", "reference": "f56b220fe2db1ade4c88098d83413ebdfc3bf876"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/array-to-xml/zipball/f56b220fe2db1ade4c88098d83413ebdfc3bf876", "reference": "f56b220fe2db1ade4c88098d83413ebdfc3bf876", "shasum": ""}, "require": {"ext-dom": "*", "php": "^8.0"}, "require-dev": {"mockery/mockery": "^1.2", "pestphp/pest": "^1.21", "spatie/pest-plugin-snapshots": "^1.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Spatie\\ArrayToXml\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://freek.dev", "role": "Developer"}], "description": "Convert an array to xml", "homepage": "https://github.com/spatie/array-to-xml", "keywords": ["array", "convert", "xml"], "support": {"source": "https://github.com/spatie/array-to-xml/tree/3.3.0"}, "funding": [{"url": "https://spatie.be/open-source/support-us", "type": "custom"}, {"url": "https://github.com/spatie", "type": "github"}], "time": "2024-05-01T10:20:27+00:00"}, {"name": "squizlabs/php_codesniffer", "version": "3.10.2", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "reference": "86e5f5dd9a840c46810ebe5ff1885581c42a3017"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/86e5f5dd9a840c46810ebe5ff1885581c42a3017", "reference": "86e5f5dd9a840c46810ebe5ff1885581c42a3017", "shasum": ""}, "require": {"ext-simplexml": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.3.4"}, "bin": ["bin/phpcbf", "bin/phpcs"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "role": "Former lead"}, {"name": "<PERSON>", "role": "Current lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHP_CodeSniffer/graphs/contributors"}], "description": "PHP_CodeSniffer tokenizes PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "homepage": "https://github.com/PHPCSStandards/PHP_CodeSniffer", "keywords": ["phpcs", "standards", "static analysis"], "support": {"issues": "https://github.com/PHPCSStandards/PHP_CodeSniffer/issues", "security": "https://github.com/PHPCSStandards/PHP_CodeSniffer/security/policy", "source": "https://github.com/PHPCSStandards/PHP_CodeSniffer", "wiki": "https://github.com/PHPCSStandards/PHP_CodeSniffer/wiki"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}], "time": "2024-07-21T23:26:44+00:00"}, {"name": "symfony/console", "version": "v6.4.10", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "504974cbe43d05f83b201d6498c206f16fc0cdbc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/504974cbe43d05f83b201d6498c206f16fc0cdbc", "reference": "504974cbe43d05f83b201d6498c206f16fc0cdbc", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^5.4|^6.0|^7.0"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/dotenv": "<5.4", "symfony/event-dispatcher": "<5.4", "symfony/lock": "<5.4", "symfony/process": "<5.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v6.4.10"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-07-26T12:30:32+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.5.0", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1", "reference": "0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.5.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-18T09:32:20+00:00"}, {"name": "symfony/filesystem", "version": "v6.4.9", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "b51ef8059159330b74a4d52f68e671033c0fe463"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/b51ef8059159330b74a4d52f68e671033c0fe463", "reference": "b51ef8059159330b74a4d52f68e671033c0fe463", "shasum": ""}, "require": {"php": ">=8.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8"}, "require-dev": {"symfony/process": "^5.4|^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v6.4.9"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-06-28T09:49:33+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "0424dff1c58f028c451efff2045f5d92410bd540"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/0424dff1c58f028c451efff2045f5d92410bd540", "reference": "0424dff1c58f028c451efff2045f5d92410bd540", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "64647a7c30b2283f5d49b874d84a18fc22054b7a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/64647a7c30b2283f5d49b874d84a18fc22054b7a", "reference": "64647a7c30b2283f5d49b874d84a18fc22054b7a", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "a95281b0be0d9ab48050ebd988b967875cdb9fdb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/a95281b0be0d9ab48050ebd988b967875cdb9fdb", "reference": "a95281b0be0d9ab48050ebd988b967875cdb9fdb", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "fd22ab50000ef01661e2a31d850ebaa297f8e03c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/fd22ab50000ef01661e2a31d850ebaa297f8e03c", "reference": "fd22ab50000ef01661e2a31d850ebaa297f8e03c", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-06-19T12:30:46+00:00"}, {"name": "symfony/service-contracts", "version": "v3.5.0", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "bd1d9e59a81d8fa4acdcea3f617c581f7475a80f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/bd1d9e59a81d8fa4acdcea3f617c581f7475a80f", "reference": "bd1d9e59a81d8fa4acdcea3f617c581f7475a80f", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.5.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-18T09:32:20+00:00"}, {"name": "symfony/string", "version": "v6.4.10", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "ccf9b30251719567bfd46494138327522b9a9446"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/ccf9b30251719567bfd46494138327522b9a9446", "reference": "ccf9b30251719567bfd46494138327522b9a9446", "shasum": ""}, "require": {"php": ">=8.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.5"}, "require-dev": {"symfony/error-handler": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/intl": "^6.2|^7.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/var-exporter": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v6.4.10"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-07-22T10:21:14+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.2.3"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:36:25+00:00"}, {"name": "vimeo/psalm", "version": "5.25.0", "source": {"type": "git", "url": "https://github.com/vimeo/psalm.git", "reference": "01a8eb06b9e9cc6cfb6a320bf9fb14331919d505"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vimeo/psalm/zipball/01a8eb06b9e9cc6cfb6a320bf9fb14331919d505", "reference": "01a8eb06b9e9cc6cfb6a320bf9fb14331919d505", "shasum": ""}, "require": {"amphp/amp": "^2.4.2", "amphp/byte-stream": "^1.5", "composer-runtime-api": "^2", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^2.0 || ^3.0", "dnoegel/php-xdg-base-dir": "^0.1.1", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-tokenizer": "*", "felixfbecker/advanced-json-rpc": "^3.1", "felixfbecker/language-server-protocol": "^1.5.2", "fidry/cpu-core-counter": "^0.4.1 || ^0.5.1 || ^1.0.0", "netresearch/jsonmapper": "^1.0 || ^2.0 || ^3.0 || ^4.0", "nikic/php-parser": "^4.16", "php": "^7.4 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0", "sebastian/diff": "^4.0 || ^5.0 || ^6.0", "spatie/array-to-xml": "^2.17.0 || ^3.0", "symfony/console": "^4.1.6 || ^5.0 || ^6.0 || ^7.0", "symfony/filesystem": "^5.4 || ^6.0 || ^7.0"}, "conflict": {"nikic/php-parser": "4.17.0"}, "provide": {"psalm/psalm": "self.version"}, "require-dev": {"amphp/phpunit-util": "^2.0", "bamarni/composer-bin-plugin": "^1.4", "brianium/paratest": "^6.9", "ext-curl": "*", "mockery/mockery": "^1.5", "nunomaduro/mock-final-classes": "^1.1", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/phpdoc-parser": "^1.6", "phpunit/phpunit": "^9.6", "psalm/plugin-mockery": "^1.1", "psalm/plugin-phpunit": "^0.18", "slevomat/coding-standard": "^8.4", "squizlabs/php_codesniffer": "^3.6", "symfony/process": "^4.4 || ^5.0 || ^6.0 || ^7.0"}, "suggest": {"ext-curl": "In order to send data to shepherd", "ext-igbinary": "^2.0.5 is required, used to serialize caching data"}, "bin": ["psalm", "psalm-language-server", "psalm-plugin", "psalm-refactor", "psalter"], "type": "project", "extra": {"branch-alias": {"dev-master": "5.x-dev", "dev-4.x": "4.x-dev", "dev-3.x": "3.x-dev", "dev-2.x": "2.x-dev", "dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"Psalm\\": "src/Psalm/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A static analysis tool for finding errors in PHP applications", "keywords": ["code", "inspection", "php", "static analysis"], "support": {"docs": "https://psalm.dev/docs", "issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm"}, "time": "2024-06-16T15:08:35+00:00"}, {"name": "webimpress/coding-standard", "version": "1.3.2", "source": {"type": "git", "url": "https://github.com/webimpress/coding-standard.git", "reference": "710f71ac95d36d931e76b47132b599c39abfab11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webimpress/coding-standard/zipball/710f71ac95d36d931e76b47132b599c39abfab11", "reference": "710f71ac95d36d931e76b47132b599c39abfab11", "shasum": ""}, "require": {"php": "^7.3 || ^8.0", "squizlabs/php_codesniffer": "^3.7.2"}, "require-dev": {"phpunit/phpunit": "^9.6.15"}, "type": "phpcodesniffer-standard", "extra": {"dev-master": "1.2.x-dev", "dev-develop": "1.3.x-dev"}, "autoload": {"psr-4": {"WebimpressCodingStandard\\": "src/WebimpressCodingStandard/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "description": "Webimpress Coding Standard", "keywords": ["Coding Standard", "PSR-2", "phpcs", "psr-12", "webimpress"], "support": {"issues": "https://github.com/webimpress/coding-standard/issues", "source": "https://github.com/webimpress/coding-standard/tree/1.3.2"}, "funding": [{"url": "https://github.com/michalbundyra", "type": "github"}], "time": "2023-12-18T07:25:41+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "platform-dev": [], "platform-overrides": {"php": "8.1.99"}, "plugin-api-version": "2.6.0"}