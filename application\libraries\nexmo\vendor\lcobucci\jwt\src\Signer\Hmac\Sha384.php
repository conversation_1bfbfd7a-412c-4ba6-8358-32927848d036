<?php
/**
 * This file is part of Lcobucci\JWT, a simple library to handle JW<PERSON> and JWS
 *
 * @license http://opensource.org/licenses/BSD-3-Clause BSD-3-Clause
 */

namespace <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Hmac;

use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Hmac;

/**
 * Signer for HMAC SHA-384
 *
 * <AUTHOR> <<EMAIL>>
 * @since 0.1.0
 */
class Sha384 extends Hmac
{
    /**
     * {@inheritdoc}
     */
    public function getAlgorithmId()
    {
        return 'HS384';
    }

    /**
     * {@inheritdoc}
     */
    public function getAlgorithm()
    {
        return 'sha384';
    }
}
