{"name": "lightslider", "title": "j<PERSON><PERSON><PERSON> lightSlider", "description": "jQuery lightSlider is a lightweight responsive content slider - carousel with animated thumbnails navigation", "keywords": ["slider", "contentslider", "textslider", "slideshow", "gallery", "responsive", "carousel", "animation", "j<PERSON><PERSON><PERSON>", "video", "image", "CSS3", "touch", "swipe", "thumbnail"], "version": "1.1.5", "author": {"name": "Sachin N", "url": "https://github.com/sachinchoolur"}, "maintainers": [{"name": "Sachin N", "email": "<EMAIL>", "url": "https://github.com/sachinchoolur"}], "licenses": [{"type": "MLT License", "url": "http://opensource.org/licenses/mit-license.html"}], "bugs": "https://github.com/sachinchoolur/lightslider/issues", "homepage": "http://sachinchoolur.github.io/lightslider/", "docs": "http://sachinchoolur.github.io/lightslider/", "download": "http://sachinchoolur.github.io/lightslider/lightSlider.zip", "dependencies": {"jquery": ">=1.7.0"}}