<?php
/**
 * Html2Pdf Library - Tests
 *
 * HTML => PDF converter
 * distributed under the OSL-3.0 License
 *
 * @package   Html2pdf
 * <AUTHOR> MINGUET <<EMAIL>>
 * @copyright 2023 Laurent MINGUET
 */

namespace Spipu\Html2Pdf\Tests\Parsing;

use Spipu\Html2Pdf\Tests\CrossVersionCompatibility\HtmlTestCase;

/**
 * Class HtmlTest
 */
class HtmlTest extends HtmlTestCase
{
    /**
     * mock of prepareTxt method
     *
     * @param $txt
     * @param bool $spaces
     * @return mixed
     */
    public function mockPrepareTxt($txt, $spaces = true)
    {
        return $txt;
    }

    /**
     * Test the prepareHtml method
     */
    public function testPrepareHtml()
    {
        $result = $this->object->prepareHtml('Hello [[date_y]]-[[date_m]]-[[date_d]] World');
        $this->assertSame('Hello '.date('Y-m-d').' World', $result);

        $result = $this->object->prepareHtml('Hello [[date_h]]:[[date_i]]:[[date_s]] World');
        $this->assertSame('Hello '.date('H:i:s').' World', $result);

        $html  = '
<html>
    <head>
        <style type="text">.my-class { color: red; }</style>
        <link type="text/css" href="my-style.css"/>
    </head>
    <body class="my-class"><p>Hello World</p></body>
</html>';

        $expected='<style type="text">.my-class { color: red; }</style>'.
            '<link type="text/css" href="my-style.css"/>'.
            '<page class="my-class"><p>Hello World</p></page>';

        $result = $this->object->prepareHtml($html);
        $this->assertSame($expected, $result);
    }
}
