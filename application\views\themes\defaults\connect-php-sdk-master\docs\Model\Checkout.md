# Checkout

## Properties
Name | Getter | Setter | Type | Description | Notes
------------ | ------------- | ------------- | ------------- | ------------- | -------------
**id** | getId() | setId($value) | **string** | ID generated by Square Checkout when a new checkout is requested. | [optional] 
**checkout_page_url** | getCheckoutPageUrl() | setCheckoutPageUrl($value) | **string** | The URL that the buyer&#39;s browser should be redirected to after the checkout is completed. | [optional] 
**ask_for_shipping_address** | getAskForShippingAddress() | setAskForShippingAddress($value) | **bool** | If &#x60;true&#x60;, Square Checkout will collect shipping information on your behalf and store that information with the transaction information in your Square Dashboard.  Default: &#x60;false&#x60;. | [optional] 
**merchant_support_email** | getMerchantSupportEmail() | setMerchantSupportEmail($value) | **string** | The email address to display on the Square Checkout confirmation page and confirmation email that the buyer can use to contact the merchant.  If this value is not set, the confirmation page and email will display the primary email address associated with the merchant&#39;s Square account.  Default: none; only exists if explicitly set. | [optional] 
**pre_populate_buyer_email** | getPrePopulateBuyerEmail() | setPrePopulateBuyerEmail($value) | **string** | If provided, the buyer&#39;s email is pre-populated on the checkout page as an editable text field.  Default: none; only exists if explicitly set. | [optional] 
**pre_populate_shipping_address** | getPrePopulateShippingAddress() | setPrePopulateShippingAddress($value) | [**\SquareConnect\Model\Address**](Address.md) | If provided, the buyer&#39;s shipping info is pre-populated on the checkout page as editable text fields.  Default: none; only exists if explicitly set. | [optional] 
**redirect_url** | getRedirectUrl() | setRedirectUrl($value) | **string** | The URL to redirect to after checkout is completed with &#x60;checkoutId&#x60;, Square&#39;s &#x60;orderId&#x60;, &#x60;transactionId&#x60;, and &#x60;referenceId&#x60; appended as URL parameters. For example, if the provided redirect_url is &#x60;http://www.example.com/order-complete&#x60;, a successful transaction redirects the customer to:  &#x60;http://www.example.com/order-complete?checkoutId&#x3D;xxxxxx&amp;orderId&#x3D;xxxxxx&amp;referenceId&#x3D;xxxxxx&amp;transactionId&#x3D;xxxxxx&#x60;  If you do not provide a redirect URL, Square Checkout will display an order confirmation page on your behalf; however Square strongly recommends that you provide a redirect URL so you can verify the transaction results and finalize the order through your existing/normal confirmation workflow. | [optional] 
**order** | getOrder() | setOrder($value) | [**\SquareConnect\Model\Order**](Order.md) | Order to be checked out. | [optional] 
**created_at** | getCreatedAt() | setCreatedAt($value) | **string** | The time when the checkout was created, in RFC 3339 format. | [optional] 
**additional_recipients** | getAdditionalRecipients() | setAdditionalRecipients($value) | [**\SquareConnect\Model\AdditionalRecipient[]**](AdditionalRecipient.md) | Additional recipients (other than the merchant) receiving a portion of this checkout. For example, fees assessed on the purchase by a third party integration. | [optional] 

Note: All properties are protected and only accessed via getters and setters.

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)

