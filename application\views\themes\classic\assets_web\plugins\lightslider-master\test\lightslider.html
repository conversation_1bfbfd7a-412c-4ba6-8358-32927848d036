<!doctype html>
<html>
    <head>
        <meta charset="utf-8">
        <title>JQuery lightSlider Test Suite</title>
        <!-- Load local jQuery. -->
        <script src="../bower_components/jquery/jquery.js"></script>
        <!-- Load local QUnit. -->
        <link rel="stylesheet" href="../bower_components/qunit/qunit/qunit.css" media="screen">
        <script src="../bower_components/qunit/qunit/qunit.js"></script>
        <!-- Load local lib and tests. -->
        <script src="../src/js/lightslider.js"></script>
        <script src="lightslider_test.js"></script>
        <!-- Removing access to jQuery and $. But it'll still be available as _$, if
        you REALLY want to mess around with jQuery in the console. REMEMBER WE
        ARE TESTING A PLUGIN HERE, THIS HELPS ENSURE BEST PRACTICES. REALLY. -->
        <script>window._$ = jQuery.noConflict(true);</script>
    </head>
    <body>
        <div id="qunit"></div>
        <div id="qunit-fixture">
            <span>lame test markup</span>
            <span>normal test markup</span>
            <span>awesome test markup</span>
        </div>
    </body>
</html>