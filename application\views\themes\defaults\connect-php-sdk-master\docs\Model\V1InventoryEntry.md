# V1InventoryEntry

## Properties
Name | Getter | Setter | Type | Description | Notes
------------ | ------------- | ------------- | ------------- | ------------- | -------------
**variation_id** | getVariationId() | setVariationId($value) | **string** | The variation that the entry corresponds to. | [optional] 
**quantity_on_hand** | getQuantityOnHand() | setQuantityOnHand($value) | **float** | The current available quantity of the item variation. | [optional] 

Note: All properties are protected and only accessed via getters and setters.

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)

