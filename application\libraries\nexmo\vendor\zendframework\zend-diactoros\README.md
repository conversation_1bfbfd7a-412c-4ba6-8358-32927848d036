# zend-diactoros

Master:
[![Build status][Master image]][Master]
[![Coverage Status][Master coverage image]][Master coverage]
Develop:
[![Build status][Develop image]][Develop]
[![Coverage Status][Develop coverage image]][Develop coverage]

> <PERSON><PERSON><PERSON> (pronunciation: `/dɪʌktɒrɒs/`): an epithet for <PERSON><PERSON>, meaning literally, "the messenger."

This package supercedes and replaces [phly/http](https://github.com/phly/http).

`zend-diactoros` is a PHP package containing implementations of the [accepted PSR-7 HTTP message interfaces](https://github.com/php-fig/fig-standards/blob/master/accepted/PSR-7-http-message.md), as well as a "server" implementation similar to [node's http.Server](http://nodejs.org/api/http.html).

* File issues at https://github.com/zendframework/zend-diactoros/issues
* Issue patches to https://github.com/zendframework/zend-diactoros/pulls

## Documentation

Documentation is available at:

- https://zendframework.github.io/zend-diactoros/

Source files for documentation are [in the doc/ tree](doc/).

  [Master]: https://travis-ci.org/zendframework/zend-diactoros
  [Master image]: https://secure.travis-ci.org/zendframework/zend-diactoros.svg?branch=master
  [Master coverage image]: https://img.shields.io/coveralls/zendframework/zend-diactoros/master.svg
  [Master coverage]: https://coveralls.io/r/zendframework/zend-diactoros?branch=master
  [Develop]: https://github.com/zendframework/zend-diactoros/tree/develop
  [Develop image]:  https://secure.travis-ci.org/zendframework/zend-diactoros.svg?branch=develop
  [Develop coverage image]: https://coveralls.io/repos/zendframework/zend-diactoros/badge.svg?branch=develop
  [Develop coverage]: https://coveralls.io/r/zendframework/zend-diactoros?branch=develop
