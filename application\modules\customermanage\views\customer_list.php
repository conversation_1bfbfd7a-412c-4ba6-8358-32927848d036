<div class="row">
    <div class="col-sm-12 col-md-12">
        <div class="panel panel-bd lobidrag">
            <div class="panel-heading">
                <div class="panel-title">
                    <h4><?php echo display('customer_list') ?></h4>
                    <a href="<?php echo base_url('customermanage/customer/ranking') ?>" class="btn btn-success btn-sm pull-right">
                        <i class="fa fa-trophy"></i> <?php echo display('customer_ranking') ?>
                    </a>
                </div>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="form-group">
                            <input type="text" id="search_customer" class="form-control" placeholder="<?php echo display('search') ?> pelanggan..." />
                        </div>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th><?php echo display('sl') ?></th>
                                <th><?php echo display('customer_id') ?></th>
                                <th><?php echo display('customer_name') ?></th>
                                <th><?php echo display('email') ?></th>
                                <th><?php echo display('mobile') ?></th>
                                <th><?php echo display('address') ?></th>
                                <th><?php echo display('customer_type') ?></th>
                                <th><?php echo display('action') ?></th>
                            </tr>
                        </thead>
                        <tbody id="customer_list_tbody">
                            <?php if (!empty($customers)) { ?>
                                <?php $sl = $this->uri->segment(4) ? $this->uri->segment(4) : 0; ?>
                                <?php foreach ($customers as $customer) { ?>
                                    <tr>
                                        <td><?php echo ++$sl; ?></td>
                                        <td><?php echo $customer->cuntomer_no; ?></td>
                                        <td><?php echo $customer->customer_name; ?></td>
                                        <td><?php echo $customer->customer_email; ?></td>
                                        <td><?php echo $customer->customer_phone; ?></td>
                                        <td><?php echo $customer->customer_address; ?></td>
                                        <td><?php echo $customer->customer_type ? $customer->customer_type : 'Regular'; ?></td>
                                        <td>
                                            <a href="<?php echo base_url("customermanage/customer/customerdetails/$customer->customer_id") ?>" class="btn btn-info btn-sm" data-toggle="tooltip" data-placement="left" title="<?php echo display('view_details') ?>"><i class="fa fa-eye" aria-hidden="true"></i></a>
                                        </td>
                                    </tr>
                                <?php } ?>
                            <?php } else { ?>
                                <tr>
                                    <td colspan="8" class="text-center"><?php echo display('no_record_found'); ?></td>
                                </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>
                <div class="text-right"><?php echo $links; ?></div>
            </div>
        </div>
    </div>
</div>

<!-- CSRF tokens for AJAX requests -->
<input name="csrf_token_name" id="csrf_token_name" type="hidden" value="<?php echo $this->security->get_csrf_token_name(); ?>" />
<input name="csrf_hash" id="csrf_hash" type="hidden" value="<?php echo $this->security->get_csrf_hash(); ?>" />

<script>
    $(document).ready(function() {
        // Search customer
        $('#search_customer').on('keyup', function() {
            var search_term = $(this).val();

            if (search_term.length > 2) {
                // Get CSRF tokens
                var csrf_token_name = $('#csrf_token_name').val();
                var csrf_hash = $('#csrf_hash').val();

                var postData = {
                    search_term: search_term
                };
                postData[csrf_token_name] = csrf_hash;

                $.ajax({
                    url: '<?php echo base_url("customermanage/customer/search_customers") ?>',
                    type: 'POST',
                    data: postData,
                    dataType: 'json',
                    success: function(response) {
                        if (response.status === 'success') {
                            var html = '';
                            var sl = 0;

                            $.each(response.data, function(index, customer) {
                                html += '<tr>';
                                html += '<td>' + (++sl) + '</td>';
                                html += '<td>' + customer.cuntomer_no + '</td>';
                                html += '<td>' + customer.customer_name + '</td>';
                                html += '<td>' + (customer.customer_email || '') + '</td>';
                                html += '<td>' + (customer.customer_phone || '') + '</td>';
                                html += '<td>' + (customer.customer_address || '') + '</td>';
                                html += '<td>' + (customer.customer_type || 'Regular') + '</td>';
                                html += '<td>';
                                html += '<a href="<?php echo base_url("customermanage/customer/customerdetails/") ?>' + customer.customer_id + '" class="btn btn-info btn-sm" data-toggle="tooltip" data-placement="left" title="<?php echo display('view_details') ?>"><i class="fa fa-eye" aria-hidden="true"></i></a>';
                                html += '</td>';
                                html += '</tr>';
                            });

                            $('#customer_list_tbody').html(html);
                        } else {
                            $('#customer_list_tbody').html('<tr><td colspan="8" class="text-center"><?php echo display('no_record_found'); ?></td></tr>');
                        }
                    },
                    error: function() {
                        alert('Error occurred during search');
                    }
                });
            } else if (search_term.length === 0) {
                // Reload the page to show all customers
                location.reload();
            }
        });
    });
</script>