/*--------------------------------------------------------------------------------------
Theme Name: Hungry
Theme URI: http://bdtask.com
Author Name: BDTask
Author URI: http://bdtask.com
Version: 1.0
----------------------------------------------------------------------------------------
    
    Header
    Newsletter 

----------------------------------------------------------------------------------------*/


/*
@import url('https://fonts.googleapis.com/css?family=Montserrat:300,400,500,600,700,800,900|Niconne');
@import url('https://fonts.googleapis.com/css?family=Great+Vibes|Poppins:200,300,400,500,600,700,800,900');
*/
@import url('https://fonts.googleapis.com/css2?family=Cookie&family=Oswald:wght@200;300;400;500;600;700&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');

/*
font-family: 'Great Vibes', cursive;
font-family: 'Poppins', sans-serif;
font-family: 'Cookie', cursive;
font-family: 'Oswald', sans-serif;
font-family: 'Roboto', sans-serif;
*/
body {
    font-family: 'Roboto', sans-serif;
}

a {
    color: #3a3a3a;
    transition: all 200ms linear 0s;
}

a:hover,
a:focus {
    color: #e7272d;
    text-decoration: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: 'Roboto', sans-serif;
    letter-spacing: 0;
    font-weight: 600;
    color: #3a3a3a;
}

p {
    font-family: 'Roboto', sans-serif;
    font-size: 16px;
    color: #676767;
    margin-bottom: 5px;
}

.btn-link:hover,
.btn-link:focus {
    text-decoration: none;
}

.font-roboto {
    font-family: 'Roboto', sans-serif;
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}
.loader4 {
    width: 90px;
    height: 90px;
    display: inline-block;
    padding: 0px;
    border-radius: 100%;
    border: 2px solid;
    border-top-color: #e7272d;
    border-bottom-color: #ddd;
    border-left-color: #e7272d;
    border-right-color: #ddd;
    -webkit-animation: loader4 1s ease-in-out infinite;
    animation: loader4 1s ease-in-out infinite;
}

@keyframes loader4 {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

@-webkit-keyframes loader4 {
    from {
        -webkit-transform: rotate(0deg);
    }

    to {
        -webkit-transform: rotate(360deg);
    }
}

.preloader .loader-img{
    position: absolute;
}
.preloader {
    position: fixed;
    left: 50%;
    top: 50%;
    width: 100%;
    height: 100%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    background-color: #ffffff;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
/*
    background-position: center center;
    background-repeat: no-repeat;
    background-image: url(../img/loader.gif);
*/
}

.bg-gray{
    background-color: #f9f9f9;
}

.bg-img-hero {
    background-size: cover;
    background-repeat: no-repeat;
    background-position: top center;
    background-image: url(../img/shape/shape4.png);
}

.bg-hero-footer {
    color: #cacaca;
    letter-spacing: 1px;
    position: relative;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: top center;
    background-image: url(../img/shape/shape3.png);
}

.sect_mar {
    margin: 100px 0;
}

.sect_pad {
    padding: 100px 0;
}

.sect_pad2 {
    padding: 100px 0 50px;
}

.sect_pad3 {
    padding: 100px 0 0;
}

.sect_title {
    margin-bottom: 30px;
}

.sect_title h1,
.sect_title h2,
.sect_title h3,
.sect_title h4 {
    text-transform: uppercase;
}

.page_header_content {
    padding: 15px 0;
}

.shape {
    position: absolute;
    left: -224px;
    top: -140px;
}

.opacity-p6 {
    opacity: 0.6;
}

.sect_title .curve_title,
.main_slider .item_caption .curve_title {
    font-size: 40px;
    font-family: 'Cookie', cursive;
    color: #e7272d;
    line-height: 0.938;
    text-transform: capitalize;
    font-weight: 600;
}

.main_slider .item_caption .pre_title {
    font-size: 22px;
    color: #e7272d;
    text-transform: uppercase;
    font-weight: 200;
    letter-spacing: 2.5px;
}

.main_slider .owl-dots {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgb(255 255 255);
    padding: 0 15px;
    border-radius: 20px;
}

.main_slider .owl-dots .owl-dot {
    width: 7px;
    height: 7px;
    border-radius: 50%;
    background: #212121;
    display: inline-block;
    margin: 0 3px;
}

.main_slider .owl-dots .owl-dot.active {
    background: #e7272d;
}

.sect_title .big_title {
    font-size: 56px;
    color: rgb(33, 33, 33);
    font-weight: 600;
}

.about_us .sect_title .big_title {
    font-weight: 300;
}

.reservation-inner .sect_title .big_title {
    font-size: 40px;
}


.sect_title .big_title span {
    font-weight: 500;
}

button:focus,
.btn:focus,
.form-control:focus {
    outline: none;
    box-shadow: none;
    border: 1px solid #e7272d;
}

select {
    line-height: normal;
    border-radius: 0;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url(../../assets_web/images/arrow.svg);
    -webkit-background-size: 9px 6px;
    background-size: 9px 6px;
    background-position: right 0rem center;
    -webkit-background-origin: content-box;
    background-origin: content-box;
    background-repeat: no-repeat;
    max-width: 100%;
    background-color: #fff;
    height: 36px;
    border: 1px solid #dedede;
    width: 100%;
    font-size: 14px;
    color: #999;
    padding: 6px 12px;
}

.sect_title h3 {
    color: #0b0b0b;
    font-size: 57px;
    text-transform: capitalize;
    font-weight: 300;
    letter-spacing: 0;
    font-family: 'Oswald', sans-serif;
}

.sect_title h4 {
    font-size: 15px;
    letter-spacing: 6px;
    position: relative;
    display: inline-block;
    line-height: 20px;
    color: #162334;
    font-family: 'Oswald', sans-serif;
}

.sect_title h4:before {
    content: '';
    position: absolute;
    top: 50%;
    left: -120px;
    transform: translateY(-50%);
    width: 95px;
    height: 3px;
    display: block;
    background: #162334;
}

.sect_title h4:after {
    content: '';
    position: absolute;
    top: 50%;
    right: -120px;
    transform: translateY(-50%);
    width: 95px;
    height: 3px;
    display: block;
    background: #162334;
}

.sect_title p {
    max-width: 550px;
    margin: 0 auto;
}

.title_one {
    font-size: 21px;
    font-weight: 400;
    line-height: 25px;
    display: block;
    margin-bottom: 6px;
}

.btn-dark {
    color: #fff;
    background-color: #1d1d1d;
    border-color: #1d1d1d;
    padding: 0 30px;
    line-height: 45px;
}

.btn-dark.btn-sm {
    padding: 0 22px;
    line-height: 38px;
}

.simple_btn {
    position: relative;
    display: inline-block;
    font-size: 15px;
    margin-top: 30px;
    padding: 0 12px;
    text-transform: uppercase;
    line-height: 35px;
    font-weight: 600;
    cursor: pointer;
    background: #e7272d;
    border: 2px solid #e7272d;
    color: #fff;
}

/*-----------Cart Box CSS-----------*/

.cart-page-title {
    background: #1d1d1d;
    padding: 15px 25px;
    color: #fff;
    margin-bottom: 30px;
}

.shopping-cart-box {
    margin: 0;
    background: #fff;
    width: 100%;
    position: relative;
    border-radius: 3px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
    border-top: 3px solid #e7272d;
}

.shopping-cart-box .shopping-cart-header {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #E8E8E8;
    padding-bottom: 15px;
}

.shopping-cart-box .shopping-cart-items {
    padding-top: 20px;
    list-style: none;
    padding-left: 0;
}

.shopping-cart-box .shopping-cart-items li {
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    position: relative;
    border-bottom: 1px dashed #ddd;
    padding-bottom: 12px;
}

.shopping-cart-box .shopping-cart-items img {
    margin-right: 12px;
    max-width: 55px;
}

.shopping-cart .shopping-cart-items .item-name {
    display: block;
    font-size: 15px;
    margin-bottom: 2px;
}

.shopping-cart-box .shopping-cart-items .item-price {
    color: #e7272d;
    margin-right: 8px;
    font-size: 13px;
}

.shopping-cart-box:after {
    bottom: 100%;
    right: 20px;
    border: solid transparent;
    content: "";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border-bottom-color: #e7272d;
    border-width: 8px;
    margin-left: -8px;
}

.cart-icon {
    color: #515783;
    font-size: 24px;
    margin-right: 7px;
    float: left;
}


.product {
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    display: -o-flex;
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.product-image {
    float: left;
    width: 150px;
    padding-right: 20px;
}

.product-details {
    width: calc(70% - 190px);
}

.product-removal {
    width: 40px;
    text-align: right;
}

.product-line-price {
    width: 30%;
    text-align: center;
}

.totals-item p {
    margin-bottom: 0;
}

.product .product-image img {
    max-width: 100%;
    border: 1px solid #ddd;
    padding: 5px;
}

.product .product-details .product-title {
    margin-right: 20px;
}

.product .product-details .product-description {
    margin: 5px 20px 5px 0;
    line-height: 1.4em;
}

.product .product-quantity input {
    width: 60px;
    border: 1px solid #c7c7c7;
}

.product .remove-product {
    border: 0;
    background-color: #e7272d;
    color: #fff;
    font-size: 11px;
    width: 25px;
    border-radius: 50%;
    padding: 0;
    cursor: pointer;
}

.product .remove-product i {
    display: block;
    line-height: 25px;
}

.product .remove-product:hover {
    background-color: #000;
}

.totals .totals-item {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.totals .totals-item:last-child {
    margin-bottom: 0;
}

.coupon,
.totals_area {
    border: 1px solid #ddd;
    padding: 20px 15px;
}


/* Radio Buttons */


@media (max-width: 640px) {
    .radios {
        flex-direction: column;
    }
}

.radio input {
    position: absolute;
    pointer-events: none;
    visibility: hidden;
}

.radio input:focus + label {
    background: #eeeeff;
}

.radio input:focus + label .checker {
    border-color: #6666ff;
}

.radio input:checked + label .checker {
    box-shadow: inset 0 0 0 6px #e7272d;
}
.shipping_custom{border: 1px solid #ddd;}
.shipping_custom_heading{border-bottom: 1px solid #ddd;padding: 10px 15px; background:#f5f5f8;font-size: 18px;font-weight: 500;}
.shipping_custom_box{padding: 10px 15px;}
.shipping_box{padding: 10px 15px 25px;}
.shipping_custom_input{border: 1px solid #ccc;padding: 5px 10px; width: 100%;}
.radio label {
    display: flex;
    align-items: center;
    height: 28px;
    border-radius: 14px;
    margin: 10px 0;
    cursor: pointer;
    transition: background-color 0.3s ease;
}
.radio label.shipping {
    margin:0;
}

.radio label:hover .checker {
    box-shadow: inset 0 0 0 2px #e7272d;
}

.radio .checker {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    margin-right: 8px;
    box-shadow: inset 0 0 0 2px #ccc;
    transition: box-shadow 0.3s ease;
}

.button {
    background: #6394F8;
    color: white;
    text-align: center;
    padding: 12px;
    text-decoration: none;
    display: block;
    border-radius: 3px;
    font-size: 16px;
    margin: 25px 0 15px 0;
}

.button:hover {
    background: #729ef9;
}

.clearfix:after {
    content: "";
    display: table;
    clear: both;
}

.quantity-span {
    padding: 0 5px;
    line-height: 24px;
    display: inline-block;
}

.btn-quantity {
    line-height: 10px;
    padding: 5px;
    font-size: 11px;
    background: #ffd9d9;
    border-radius: 50%;
    color: #e7272d;
}

.btn-quantity:focus {
    background: #e7272d;
    color: #fff;
}

.btn-remove {
    background: transparent;
    border: 0;
    position: absolute;
    right: 0;
    font-size: 10px;
    top: calc(50% - 15px);
    cursor: pointer;
    transition-duration: 0.3s;
    transform-origin: 50% 50%;
    padding: 0;
}

.btn-remove:hover {
    transform: rotate(180deg);
}

.btn-dark:focus,
.btn-dark:hover {
    background-color: #e7272d;
    outline: none;
    box-shadow: none;
    color: #fff;
}

.simple_btn.btn2 {
    line-height: 50px;
    padding: 0 25px;
}

.wishlist_btn {
    display: inline-block;
    font-size: 15px;
    padding: 0 25px;
    text-transform: uppercase;
    line-height: 40px;
    font-weight: 600;
    cursor: pointer;
    border: 2px solid #e7272d;
    color: #e7272d;
}

.wishlist_btn:focus,
.wishlist_btn:hover {
    background: #e7272d;
    color: #fff;
}

.simple_btn:hover,
.simple_btn:focus {
    background: #212121;
    border-color: #212121;
    color: #fff;
}

.btn1 {
    border: 1px solid #ccc3c3;
    background: transparent;
    color: #0b0b0b;
    padding: 0 10px;
    line-height: 33px;
}

.btn:focus,
btn1:focus {
    outline: none;
    box-shadow: none;
    color: #fff;
}

.btn1:focus {
    border: 1px solid #dad;
}

/*--------- Btn Top -----------*/
.cd-top {
    display: inline-block;
    position: fixed;
    bottom: 50px;
    right: 20px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    white-space: nowrap;
    background: #e7272d;
    visibility: hidden;
    opacity: 0;
    -webkit-transition: opacity .3s 0s, visibility 0s .3s;
    -moz-transition: opacity .3s 0s, visibility 0s .3s;
    transition: opacity .3s 0s, visibility 0s .3s;
    text-align: center;
    text-decoration: none;
    z-index: 999;
}

.cd-top:focus {
    text-decoration: none;
}

.cd-top i {
    color: #fff;
    line-height: 45px;
    display: block;
    font-size: 17px;
}

.cd-top.cd-is-visible {
    visibility: visible;
    opacity: 1;
    width: 45px;
    height: 45px;
    border-radius: 0;
    bottom: 70px;
}

.cd-top.cd-fade-out {
    /*opacity: 1;
    z-index: 9999;*/
}
.modal{z-index:999999;}
.cd-top:hover {
    background-color: #212121;
    opacity: 1;
}

@media only screen and (min-width: 1024px) {
    .cd-top {
        height: 45px;
        width: 45px;
        border-radius: 0;
        right: 35px;
        bottom: 100px;
        transition: all 300ms linear 0s;
    }
}

.bg_two {
    background: #f9f9f9;
}

.bg_img_area {
    position: relative;
}

.bg_img_left {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: block;
    background: url(../images/background/1.png) no-repeat;
}

.bg_img_left.img_2 {
    background: url(../images/background/3.png) no-repeat;
}

.bg_overlay {
    opacity: 0.3;
}

.bg_img_right {
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    display: block;
    background: url(../images/background/2.png) no-repeat 100% 100% scroll;
}

.social-links a {
    width: 35px;
    height: 35px;
    line-height: 35px;
    color: #fff;
    background-color: #1b4e9b;
    margin: 0 2px;
    display: inline-block;
    text-align: center;
    font-size: 17px;
}

.social-links a.tw {
    background-color: #00aeef;
}

.social-links a.gp {
    background-color: #c00;
}

.social-links a.gp {
    background-color: #c00;
}

.social-links a.pr {
    background-color: #c8232c;
}

.search_box {
    position: relative;
    top: -44px;
    z-index: 1;
}

.search_box_inner {
    padding: 25px;
    background: #fff;
    -webkit-box-shadow: 0px 0px 17px 0px rgba(0, 0, 0, 0.15);
    -moz-box-shadow: 0px 0px 17px 0px rgba(0, 0, 0, 0.15);
    box-shadow: 0px 0px 17px 0px rgba(0, 0, 0, 0.15);
}

.search_box_inner .btn {
    border-radius: 0;
    font-size: 15px;
    display: block;
    line-height: 36px;
    padding: 0 14px;
    width: 100%;
}

.search_box_inner .form-control,
.search_box_inner .input-group-text {
    border-radius: 0;
}

.search_box_inner .input-group-text {
    background: #e7272d;
    border: 1px solid #e7272d;
}

.search_box_inner .form-control::placeholder {
    font-size: 14px;
}

.search_box_inner .form-control:focus {
    outline: none;
    box-shadow: none;
    border: 1px solid #e7272d;
}

.search_box_inner .input-group-text i {
    color: #fff;
}

/*--------------------
   Header 
----------------------*/
.header_top_area {
    position: relative;
    width: 100%;
    height: 100%;
}

.header_top {
    width: 100%;
    z-index: 999;
    text-transform: uppercase;
}

.header_top .navbar-nav .nav-link {
    color: #3f4242;
    font-size: 15px;
    font-weight: 600;
    padding: 0 20px;
    letter-spacing: 0.3px;
    line-height: 58px;
}

.header_top .navbar-nav .nav-link:hover {
    color: #e7272d;
}

.header_top .navbar-nav .nav-item.active .nav-link {
    color: #e7272d;
}

.header_top.menu_fixed {
    width: 100%;
    display: block;
    position: fixed;
    font-weight: bold;
    background: #ffffff;
    -webkit-box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.15);
    -moz-box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.15);
    box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.15);
/*    transition: all 300ms linear 0s;*/
    height: auto;
    z-index: 9999;
    color: #fff !important;
    top: 0;
}

@media (min-width: 992px) {
    .navbar-expand-lg .navbar-nav .dropdown-menu.search_box {
        position: absolute;
        top: 100%;
        right: 0;
        width: 100%;
        height: 0;
        margin: 0;
        padding: 0;
    }

    .navbar-expand-lg .navbar-nav .dropdown-menu {
        position: absolute;
        top: 72px;
        margin: 0;
    }
}

.cart-box,
.dropdown-menu {
    font-size: 14px;
    text-transform: capitalize;
}

.dropdown-item:focus,
.dropdown-item:hover {
    color: #ffffff;
    text-decoration: none;
    background-color: #e7272d;
}

.search_box .card {
    background: #272727;
    border-radius: 0;
    border: 0;
}

.search_box .card .form-control {
    background: #444;
    font-size: 15px;
    border: 0;
    border-radius: 0;
    color: #ececec;
}

.search_box .card .form-control::placeholder {
    color: #b3b3b3;
}

.search_box .card .btn {
    background: #e7272d;
    border-radius: 0;
    line-height: 30px;
    border: 1px solid #e7272d;
    font-size: 15px;
    color: #ffffff;
}

.navbar-expand-lg .navbar-nav .nav-item .cart_box {
    position: absolute;
    top: 100%;
    right: 0;
    width: 320px;
    margin: 0;
    margin-left: auto;
    padding: 10px 0;
    background: transparent;
    border: 0;
    color: #212529;
    text-transform: capitalize;
    z-index: 5;
    opacity: 1;
    transform-origin: top;
    transform: scaleY(0);
    transition: ease-out 0.4s;
}

.navbar-expand-lg .navbar-nav .nav-item:hover .cart_box {
    transform: scaleY(1);
}

.cart_btn_area .simple_btn {
    padding: 0 30px;
    line-height: 45px;
}

.coupon .simple_btn{
    display: block;
    width: 100%;
}

.cart_box p {
    color: #fff;
}

.navbar {
    border-bottom: 0;
    padding: 15px 0;
}

.main_slider .item {
    overflow: hidden;
    position: relative;
}

.main_slider .item:before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    display: block;
    background: rgba(0, 0, 0, 0.5);
}

.main_slider .item img {
    max-width: 100%;
}

.main_slider .item .item_caption {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60%;
    z-index: 2;
    text-align: center;
}

.main_slider .item .item_caption .under_title,
.main_slider .item .item_caption h2,
.main_slider .item .item_caption h3 {
    font-family: 'Oswald', sans-serif;
}

.main_slider .item .item_caption h3 {
    color: #fff;
    animation-duration: .9s;
}

.main_slider .item .item_caption h2 {
    color: #fff;
    font-size: 70px;
    margin: 20px 0 30px;
    font-weight: 800;
    text-transform: uppercase;
    animation-duration: .9s;
    animation-delay: .3s;
}

.main_slider .item .item_caption h2 span {
    color: #ffb400;
}

.main_slider .item .item_caption .under_title {
    color: #fff;
    margin-bottom: 35px;
    font-size: 19px;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 3.5px;
}

.main_slider .item .item_caption a {
    display: inline-block;
    font-size: 15px;
    line-height: 50px;
    border: 1px solid #e7272d;
    background: #e7272d;
    border-radius: 50px;
    min-width: 170px;
    text-align: center;
    color: #fff;
    letter-spacing: 1px;
    animation-duration: .9s;
    animation-delay: .5s;
    text-transform: uppercase;
}

.main_slider .item .item_caption a:hover {
    background: #ad0000;
    border: 1px solid #ad0000;
    color: #fff;
}

.main_slider .owl-nav {
    position: absolute;
    top: 50%;
    left: 0;
    color: #fff;
    right: 0;
}

.main_slider .owl-nav .owl-next,
.main_slider .owl-nav .owl-prev {
    width: 70px;
    height: 70px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 50%;
    text-align: center;
    position: absolute;
    display: inline-block;
    -webkit-transition-duration: 0.5s;
    -webkit-transition-timing-function: linear;
    transition: all 0.5s ease 0s;
    opacity: 0;
    border: 0;
}

.main_slider .owl-nav .owl-next:hover,
.main_slider .owl-nav .owl-prev:hover,
.main_slider .owl-nav .owl-next:focus,
.main_slider .owl-nav .owl-prev:focus {
    background: rgba(0, 0, 0, 1);
}

.main_slider .owl-nav .owl-prev {
    left: 0;
}

.main_slider .owl-nav .owl-next {
    right: 0;
}

.main_slider:hover .owl-nav .owl-prev {
    left: 30px;
    opacity: 1;
}

.main_slider:hover .owl-nav .owl-next {
    right: 30px;
    opacity: 1;
}

.main_slider .owl-nav .owl-next i,
.main_slider .owl-nav .owl-prev i {
    display: block;
    line-height: 70px;
    font-size: 21px;
    color: #fff;
}

.main_slider .owl-nav .owl-next:hover {
    -webkit-box-shadow: -100px 0 0 #e7272d inset;
}

.main_slider .owl-nav .owl-prev:hover {
    -webkit-box-shadow: -100px 0 0 #e7272d inset;
}

.main_slider .owl-nav .owl-next:hover i,
.main_slider .owl-nav .owl-prev:hover i,
.main_slider .owl-nav .owl-next:focus i,
.main_slider .owl-nav .owl-prev:focus i {
    color: #fff;
}

.main_slider .owl-nav .owl-next:hover,
.main_slider .owl-nav .owl-prev:hover,
.main_slider .owl-nav .owl-next:focus,
.main_slider .owl-nav .owl-prev:focus {
    border: 0;
}

.owl-nav {
    position: absolute;
    top: calc(50% - 25px);
    left: 0;
    color: #fff;
    right: 0;
}

.owl-nav .owl-next,
.owl-nav .owl-prev {
    width: 45px;
    height: 50px;
    text-align: center;
    position: absolute;
    display: inline-block;
    -webkit-transition-duration: 0.3s;
    -webkit-transition-timing-function: linear;
    transition: all 0.3s ease 0s;
    border: 1px solid #ddd;
}

.owl-nav .owl-prev {
    left: -70px;
    transform: translatey(-50%);
}

.owl-nav .owl-next {
    right: -70px;
    transform: translatey(-50%);
}

.owl-nav .owl-next i,
.owl-nav .owl-prev i {
    display: block;
    font-size: 21px;
    color: #ddd;
    line-height: 50px;
}

.owl-nav .owl-next:hover,
.owl-nav .owl-prev:hover,
.owl-nav .owl-next:focus,
.owl-nav .owl-prev:focus {
    background: #e7272d;
    border: 1px solid #e7272d;
}

.owl-nav .owl-next:hover i,
.owl-nav .owl-prev:hover i,
.owl-nav .owl-next:focus i,
.owl-nav .owl-prev:focus i {
    color: #fff;
}

/*----------------------------
    Cart Box
------------------------------*/
.cart-header {
    border-bottom: 1px solid #ddd;
}

.cart-box {
    padding: 15px;
    list-style: none;
    border: 1px solid #ddd;
}

.cart-content {
    padding: 15px;
}

.cart-content .img-box {
    max-width: 60px;
    margin-right: 10px;
}

.cart-content .delete_box {
    max-width: 20px;
    width: 100%;
    text-align: right;
}

.cart-content .content {
    max-width: calc(100% - 90px);
    width: 100%;
}

/*----------------------------
    Metis Menu
------------------------------*/
#sidebarCollapse {
    border-radius: 0;
    border: 0;
    background: #ffe7e7;
    padding: 0 10px;
}

#sidebarCollapse i {
    font-size: 19px;
    margin-left: 0;
    color: #e7272d;
    line-height: 35px;
    display: block;
}

@media(min-width: 992px) {
    .sidebar-toggle-btn {
        display: none;
    }
}

.sidebar-nav {
    position: fixed;
    width: 260px;
    top: 0;
    left: -100%;
    height: 100%;
    z-index: 999;
    background: #1d1d1d;
    transition: all 0.3s;
    overflow-y: scroll;
    padding: 0 20px 20px;
    transition-duration: 0.5s;
}

.sidebar-nav.active {
    left: 0;
}

#dismiss {
    width: 0;
    height: 0;
    line-height: 35px;
    text-align: center;
    position: absolute;
    top: 15px;
    right: 40px;
    cursor: pointer;
    -webkit-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
    color: #c1c1c1;
}

.metismenu {
    margin-top: 80px;
}

.metismenu > li {
    border-bottom: 1px solid #353535;
    padding: 12px 0;
}

.metismenu > li > a {
    font-size: 14px;
    display: block;
    padding-right: 20px;
    position: relative;
    -webkit-transition: none;
    transition: none;
    color: #c1c1c1;
    font-weight: 500;
}

.metismenu > li > a:hover,
.metismenu > li.active > a,
.metismenu > li > ul > li > a:hover,
.metismenu > li > ul > li > ul > li > a:hover,
.metismenu > li > ul > li > ul > li.active > a {
    color: #fff;
}

.metismenu > li > ul {
    padding-left: 15px;
    padding-top: 10px;
    list-style-type: none;
}

.metismenu > li > ul > li {
    padding: 6px 0;
}

.metismenu > li > ul > li > a {
    font-size: 14px;
    display: block;
    position: relative;
    color: #c1c1c1;
    font-weight: 500;
}

.metismenu > li > ul > li.active > a {
    color: #212121;
}

.metismenu > li > ul > li > ul {
    list-style-type: none;
    padding-left: 15px;
    padding-top: 10px;
}

.metismenu > li > ul > li > ul > li {
    padding: 6px 0;
}

.metismenu > li > ul > li > ul > li > a {
    font-size: 14px;
    display: block;
    color: #adbdbb;
}

.metismenu .arrow {
    font-size: 16px;
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    right: 0;
}

.metismenu li.active .fa.arrow::before {
    content: "\f107";
}

@media(min-width: 992px) {
    .sidebar-nav {
        display: none;
    }
}

/*----------------------------
    Page Banner
------------------------------*/

.page_header {
    position: relative;
    background: #f9f9f9;
}

.menu_banner_bg {
    background: url("../images/background/menu-banner.jpg") no-repeat scroll;
}

.page_header_content h1,
.page_header_content a {
    color: #3a3a3a;
    font-size: 14px;
}

.page_header_content a:hover {
    color: #e7272d;
}

.page_header_content li i {
    color: #3a3a3a;
    font-size: 14px;
    margin: 0 8px;
}

/*----------------------------
    ABOUT AREA
------------------------------*/

.about_us .area-title h2 {
    padding-bottom: 10px;
}

.about_us .area-title h2 span {
    color: #212324;
    font-family: lato;
    font-size: 48px;
    text-transform: uppercase;
}

.about_inner .img_part {
    transition-duration: 0.3s;
    position: relative;
}

.about_inner .img_part img {
    transition-duration: 0.4s;
    animation: rotateAnim 30s infinite;
}

@keyframes rotateAnim {
    50% {
        transform: rotate(180deg)
    }
}

.aboutus_text {
    color: #4a4d4f;
}

.aboutus_text .simple_btn {
    margin-top: 10px;
    min-width: 190px;
    text-align: center;
    line-height: 50px;
    font-size: 17px;
    letter-spacing: 0.5px;
    text-transform: capitalize;
}

.aboutus_text .simple_btn:focus,
.aboutus_text .simple_btn:hover {
    color: #fff;
}

.read-more:hover {
    background: #d0963e;
    color: #fff;
    border-color: #d0963e;
}

/*-----------------------------
    Gallery 
-------------------------------*/
.gallery_inner .item .img-fluid {
    width: 100%;
}


/*-----------------------------
    Offer 
-------------------------------*/
.offer_slider .owl-stage-outer {
    padding: 15px;
}

.offer_inner .item {
    background: #fff;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(99, 96, 96, 0.1);
    transition-duration: 0.5s;
}

.offer_inner .item .top_side {
    transform: rotate(-45deg);
    position: absolute;
    top: -14px;
    left: -62px;
    display: inline-block;
    width: 150px;
    padding-top: 28px;
    font-weight: 400;
    font-size: 14px;
    text-align: center;
    line-height: 30px;
    color: #ffffff;
    background: #e7272d;
}

.offer_inner .item .img_area {
    overflow: hidden;
}

.offer_inner .item img {
    text-align: center;
    margin: 0 auto;
    transition-duration: 0.5s;
}

.offer_inner .item_content h6 {
    margin-bottom: 5px;
    font-weight: 400;
    font-size: 15px;
}

.offer_inner .item_content .item_name {
    font-size: 18px;
    font-weight: 600;
    margin: 15px 0 25px;
    display: block;
    position: relative;
    font-family: 'Roboto', sans-serif;
}

.offer_inner .item_content .item_name:before {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    display: block;
    width: 55px;
    height: 1px;
    background: #e7272d;
    transition: all 200ms linear 0s;
}

.offer_inner .item:hover {
    box-shadow: 0px 1px 20px 0px rgba(0, 0, 0, 0.1);
}

.offer_inner .item:hover .item_content .item_name:before {
    width: 105px
}

.offer_inner .item_content .simple_btn {
    margin: 0;
}

.offer_inner .item:hover .item_content .simple_btn:hover {
    color: #fff;
}

.offer_inner .item:hover img {
    transform: scale(1.1);
}

.offer_inner .item_content .simple_btn:before {
    z-index: 0;
}

.offer_inner .item_content .simple_btn i,
.offer_inner .item_content .simple_btn span {
    position: relative;
}

.offer_inner .item_content p {
    letter-spacing: 1.3px;
    font-size: 15px;
    margin: 15px 0;
}

.offer_inner .item_content .number {
    max-width: 70px;
    display: inline-block;
    padding-left: 5px;
    height: 47px;
    border-radius: 0;
    border: 1px solid #e4e4e4;
    vertical-align: middle;
}

.number:focus {
    outline: none;
    box-shadow: none;
}

.post_slider .item .img_area .hover_item {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition-duration: 0.5s;
    display: block;
    z-index: 3;
}

.post_slider .item .img_area .hover_item i {
    font-size: 30px;
    color: #fff;
}

.post_slider .item .img_area:hover .hover_item {
    opacity: 1;
}

.post_slider .item .img_area:hover img {
    filter: grayscale(100%);
}

.hover_shadow {
    position: relative;
    display: block;
}

.hover_shadow:before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    display: block;
    background: rgba(0, 0, 0, 0.75);
    opacity: 0;
    transition-duration: 0.5s;
    z-index: 2;
}

.hover_shadow:hover:before {
    opacity: 1;
}

/*--------------------------
    RESERVATION AREA
---------------------------*/
.reservation-area {
    background: url(../img/reservation-bg.jpg);
    background-size: cover;
    position: relative;
}

.reservation-area:before {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    display: block;
    background: rgba(0, 0, 0, 0.65);
    z-index: 0;
}

.reservation-inner {
    position: relative;
    background-color: #fff;
    padding: 15px 0;
    align-items: center;
}

.main-reservaton-form {
    max-width: 450px;
    margin: 0 auto;
}

.main-reservaton-form input {
    padding: 10px 40px 10px 15px;
    position: relative;
    width: 100%;
    margin-bottom: 0;
    -webkit-transition: 0.3s;
    transition: 0.3s;
    background: transparent;
    border: 1px solid #c3c3c3;
}

.main-reservaton-form input:focus {
    border-color: #e7272d;
    box-shadow: 0 0 0;
    outline: none;
}

.main-reservaton-form label {
    position: absolute;
    right: 22px;
    line-height: 42px;
    text-align: center;
    top: 0;
    width: 30px;
    z-index: 9;
    margin: 0;
    cursor: pointer;
    font-size: 18px;
}

.main-reservaton-form .simple_btn {
    margin: 0;
    display: block;
    width: 100%;
    line-height: 50px;
    background: #e7272d;
    border-color: #e7272d;
    color: #fff;
    transition-duration: 0.3s;
}

.main-reservaton-form .simple_btn:hover {
    background: #212121;
    border-color: #212121;
}

.reservation-call-to-action,
.reservation-private-text {
    color: #212324;
    line-height: 2;
}

.reservation-call-to-action h4,
.reservation-private-text h4 {
    font-size: 20px;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.reservation-call-to-action a {
    color: #c1934c;
    font-size: 20px;
}

/*-----------------------------
    Menu AREA
-------------------------------*/
.rating_area .rate-container i {
    color: #04be30;
}

.food_menu_topper {
    background: url(../img/foodMenu/bg.jpg);
    background-size: cover;
    position: relative;
    padding-top: 90px;
}

.food_menu_topper:before {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    display: block;
    background: rgba(0, 0, 0, 0.65);
    z-index: 0;
}

.food_menu_title,
.food_menu_title2 {
    color: #fff;
    position: relative;
}

.food_menu_title {
    font-size: 50px;
    margin-bottom: 20px;
}

.food_menu_title2 {
    display: inline-block;
}

.food_menu_title2:after,
.food_menu_title2:before {
    content: '';
    position: absolute;
    width: 100px;
    display: block;
    height: 2px;
    background: #ffffff;
    top: 50%;
    transform: translateY(-50%);
}

.food_menu_title2:before {
    left: -120px;
}

.food_menu_title2:after {
    right: -120px;
}

.single_item .action_part {
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    display: -o-flex;
    display: flex;
    align-items: center;
}

.menu-tab-nav {
    background: rgb(46 46 46 / 0.65);
}

.menu_area .sect_title h1 {
    padding-bottom: 30px;
    border-bottom: 1px solid #ddd;
    margin-bottom: 30px;
}

.menu_area .card-header {
    padding: 10px 15px;
}

.menu_area .single_item {
    border-bottom: 1px dashed #ddd;
    margin-bottom: 15px;
}

.menu_area .single_item .item_img img {
    border: 1px solid #e7272d;
    padding: 5px;
}

.menu_area .item_details .simple_btn {
    font-size: 13px;
    padding: 0 10px;
    line-height: 30px;
}

.menu_area .item_details .simple_btn:hover,
.menu_area .item_details .simple_btn:focus {
    border: 2px solid #212121;
    color: #fff;
}

.menu_area .item_details .item_title {
    font-size: 21px;
    font-weight: 500;
}

.menu-tab-nav .nav-pills {
    margin-bottom: 80px;
    border-bottom: 4px solid #e7272d;
}

.menu-tab-nav .nav-pills .nav-item {
    width: 20%;
    text-align: center;
}

.menu-tab-nav .nav-pills .nav-link {
    padding: 30px 35px;
    border-radius: 0;
    position: relative;
}

.menu-tab-nav .nav-pills .nav-link:before {
    content: '';
    position: absolute;
    top: 90%;
    left: 50%;
    opacity: 0;
    transform: translateX(-50%);
    display: block;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 18px 20px 0 20px;
    transition-duration: 0.1s;
    border-color: #e7272d transparent transparent transparent;
}

.menu-tab-nav .nav-pills .nav-link.active:before {
    opacity: 1;
    top: 100%;
}

.menu-tab-nav .nav-pills .nav-link h6 {
    margin-top: 20px;
    margin-bottom: 10px;
    color: #fff;
}

.menu-tab-nav .nav-pills .nav-link.active {
    background-color: #e7272d;
}

.menu_area .item_info h4,
.menu_area .item_info p,
.menu_area .item_details p {
    margin: 0;
}

.menu_area .item_info h6 {
    font-weight: 500;
    font-size: 14px;
}

.menu_area .sidebar .form-control {
    width: calc(100% - 42px);
    border-radius: 0;
}

.menu_area .sidebar .form-control:focus {
    border: 1px solid #e7272d;
}

.menu_area .sidebar .btn-success {
    border-radius: 0;
    background-color: #e7272d;
    border-color: #e7272d;
}

.cart_counter .qty {
    width: 50px;
    border: 1px solid #ddd;
    text-align: center;
    height: 34px;
    vertical-align: top;
    margin: 0 -5px 0 -6px;
}

.cart_counter .qty:focus {
    outline: none;
}

.cart_counter .items-count {
    background: transparent;
    border: 1px solid #ddd;
    line-height: 32px;
    font-size: 10px;
    color: #9c9c9c;
    padding: 0 10px;
    transition-duration: 0.3s;
}

.cart_counter .items-count:hover {
    background: #e7272d;
    color: #fff;
    border: 1px solid #e7272d;
}

.cart_counter .items-count:focus,
.cart_counter .items-count:focus {
    outline: none;
}

.cart_image {
    width: 120px;
}

.menu_info {
    height: auto;
    width: 70%;
    background: #162334;
    padding: 70px;
}

.menu_info .simple_btn {
    border: 2px solid #fff;
    color: #fff;
    padding: 0 18px;
}

.menu_info .simple_btn:hover {
    border: 2px solid #e7272d;
}

.menu_info .simple_btn i {
    margin-right: 5px;
}

.menu_info .simple_btn i,
.menu_info .simple_btn span {
    position: relative;
    z-index: 2;
}

.menu_info .simple_btn:before {
    z-index: 1;
}

.menu_info .h1 {
    font-weight: 700;
}

.menu_info .h1,
.menu_info p,
.menu_info h2 {
    color: #fff;
}

.menu_info p {
    max-width: 550px;
    margin-bottom: 16px;
}

.menu_info .simple_btn {
    margin-top: 10px;
}

.menu_img {
    background: #fff;
    color: #1a1a1a;
    margin-top: -150px;
    padding: 15px;
    position: absolute;
    right: 0;
    top: 50%;
    width: 38%;
    z-index: 9;
    border: 1px solid #dedede;
}

.menu_img h3 {
    font-size: 30px;
}

.menu_img a.read-more {
    margin-top: 25px;
}

.menu_img a.read-more:hover {
    background: #c1934c none repeat scroll 0 0;
    border-color: #c1934c;
    color: #fff;
}

.menu-discount-offer {
    margin: auto;
}

.menu-discount-offer .owl-dots > div {
    background: #bcbfc1 none repeat scroll 0 0;
    border-radius: 50%;
    display: inline-block;
    height: 15px;
    margin: 0 5px;
    width: 15px;
}

.menu_slider .owl-nav {
    top: 0;
    left: -20px;
}

.menu_slider .owl-nav .owl-prev {
    left: -85px;
    transform: none;
}

.menu_slider .owl-nav .owl-next {
    transform: none;
    left: -40px;
}

.menu_slider .owl-nav .owl-next,
.menu_slider .owl-nav .owl-prev {
    width: 40px;
    height: 40px;
}

.menu_slider .owl-nav .owl-next i,
.menu_slider .owl-nav .owl-prev i {
    font-size: 15px;
    line-height: 40px;
}

.menu-discount-offer .owl-dots > div.active {
    background: #1a1a1a none repeat scroll 0 0;
}

/*-----------------------------
    Details
-------------------------------*/
.product-details-inner .product_img img {
    border: 1px solid #d6d6d6;
    padding: 10px;
}

.product_review_inner .nav-pills {
    max-width: 350px;
    margin: 0 auto 40px;
    border: 1px solid #3a3a3a;
    padding: 5px;
}

.product_review_inner .nav-pills li {
    width: 50%;
    text-align: center;
    line-height: 30px;
}

.product_review_inner .nav-pills .nav-link.active {
    background-color: #212121;
}

.product-meta {
    font-size: 18px;
    line-height: 25px;
}

.product-price del {
    font-size: 34px;
    color: #ddd;
    margin-left: 10px;
}

.action_btn .simple_btn {
    line-height: 40px;
}

.lSAction {
    top: 50%;
    position: absolute;
    left: 0;
    right: 0;
}

.lSAction > a {
    background-image: none;
    opacity: 1;
    background-color: #fff;
    border-radius: 50%;
    font-family: 'themify';
    display: flex;
    align-items: center;
    justify-content: center;
    color: #e7272d !important;
    width: 40px;
    height: 40px;
}

.lSAction > a:hover {
    background-color: #e7272d;
    color: #fff !important;
}

.lSAction > .lSPrev:before {
    content: "\e64a";
}

.lSAction > .lSNext:before {
    content: "\e649";
}

.gl-buttons .grid,
.gl-buttons .list {
    border: 1px solid #ddd;
    background: #f1f1f1;
    width: 40px;
    text-align: center;
    line-height: 35px;
    color: #585858;
    cursor: pointer;
}

.grid-container.grid .single_item {
    display: inline-block;
    width: calc(50% - 9px);
}

.xs_only,
.grid_only {
    display: none;
}

.grid-container.grid .single_item .grid_only {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.grid-container.grid .single_item:nth-child(2n+1) {
    margin-right: 14px;
}

.grid-container.grid .single_item .row {
    display: block;
    width: 100%;
    margin: 0;
}

.grid-container.grid .single_item .item_img img {
    border: 0;
    padding: 0;
    width: 100%;
}

.grid-container.grid .single_item .item_img {
    padding: 0;
}

.grid-container.grid .single_item .item_img,
.grid-container.grid .single_item .item_info,
.grid-container.grid .single_item .item_details {
    width: 100%;
}

.grid-container.grid .single_item .item_info {
    display: none;
}

.grid-container.grid .single_item .item_details {
    padding-right: 0;
    padding-top: 8px;
}

.grid-container .item_img {
    width: 25%;
    padding: 0 15px;
}

.grid-container .item_details {
    width: 50%;
}

.grid-container .item_info {
    width: 25%;
}

.rating-block {
    background-color: #fff;
    border: 1px solid #dedede;
    padding: 15px 15px 20px 15px;
    max-width: 400px;
}

.rating-point {
    position: relative;
}

.rating-point i {
    font-size: 100px;
    line-height: 97px;
    color: #e7272d;
}

.rating-count {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    margin: 0;
    color: #fff;
}

.rating-quantity,
.rating-percent,
.user-rating {
    display: inline-block;
}

.rating-quantity {
    width: 35px;
    text-align: right;
    margin-right: 5px;
}

.review-form .simple_btn {
    padding: 0 35px;
    min-width: 180px;
    text-transform: uppercase;
    line-height: 50px;
}

.user-rating {
    width: 45px;
    text-align: right;
}

.progress {
    width: 180px;
    border-radius: 0;
    height: 6px;
}

.progress-bar {
    background: #e7272d;
}

.review-form {
    font-size: 14px;
    line-height: 20px;
    margin-bottom: 5px;
    color: #424242;
    font-weight: 500;
}

.review-block {
    width: 75%;
}

.review-block .btn-success {
    border-radius: 0;
}

.review-block h6 {
    line-height: 30px;
}

.review-meta-inner i,
.review-meta-inner span {
    font-size: 13px;
    color: #8a8888;
}

.rating-area {
    display: block;
    overflow: hidden;
}

.rating {
    float: left;
}

.rating:not(:checked) > input {
    position: absolute;
    top: -9999px;
    clip: rect(0, 0, 0, 0);
}

.rating:not(:checked) > label {
    float: right;
    width: 1em;
    padding: 0 .1em;
    overflow: hidden;
    white-space: nowrap;
    cursor: pointer;
    font-size: 200%;
    line-height: 1.2;
    color: #b9b8b8;
    text-shadow: 0.1em 0.1em 0.2em rgba(0, 0, 0, .05);
}

.rating:not(:checked) > label:before {
    content: '★ ';
}

.rating > input:checked ~ label {
    color: #e7272d;
    text-shadow: 1px 1px #04c752;
}

.rating:not(:checked) > label:hover,
.rating:not(:checked) > label:hover ~ label {
    color: gold;
    text-shadow: 1px 1px goldenrod, 2px 2px #B57340, .1em .1em .2em rgba(0, 0, 0, .5);
}

.rating > label:active {
    position: relative;
    top: 2px;
    left: 2px;
}

.page-link {
    border: 0;
    color: #3a3a3a;
    margin: 0 2px;
}

.page-link:hover,
.page-link.active {
    color: #ffffff;
    background: #e7272d;
}

.table_chart a:focus img {
    filter: grayscale(100%);
}

.table_chart .modal-body label {
    font-size: 12px;
}

.table_chart .modal-body .form-control::placeholder {
    font-size: 12px;
}

.table_chart .modal-footer .btn {
    font-size: 13px;
}

.table-bordered td,
.table-bordered th {
    vertical-align: middle;
}

.modal-dialog {
    margin: 100px auto 20px;
}

.modal-open {
    padding-right: 0 !important;
}

.category_choose,
.need_booking,
.sidebar_box {
    background: #f1f1f1;
}

.need_booking a {
    font-size: 15px;
    font-weight: 600;
    color: #e7272d;
}

.popover.clockpicker-popover.bottom.clockpicker-align-left {
    border: 2px solid #e7272d;
}

.clockpicker-canvas-bg {
    fill: #e7272d;
}

.clockpicker-canvas line {
    stroke: #e7272d;
}

.clockpicker-canvas-bearing,
.clockpicker-canvas-fg {
    fill: #e7272d;
}

.clockpicker-tick.active,
.clockpicker-tick:hover {
    background-color: #e7272d;
    color: #fff;
}

.text-primary,
.clockpicker-popover .popover-title span {
    color: #e7272d !important;
}

.datepicker table tr td.day.focused,
.datepicker table tr td.day:hover {
    background: #e7272d;
    color: #fff;
}

.datepicker table tr td.active.active {
    background: #e7272d;
}

.datepicker table {
    margin: 10px;
}

.datepicker td,
.datepicker th {
    width: 25px;
    height: 25px;
}

.category_choose .panel-body a {
    display: block;
    font-size: 15px;
}

.category_choose .panel-body {
    margin-bottom: 10px;
}

.category_choose .panel-body a i {
    font-size: 10px;
}

.category_choose .panel-body a span {
    margin-left: 8px;
    float: right;
    font-size: 13px;
    font-weight: 600;
}

/*-----------------------------
    Contact Area
-------------------------------*/
.contact {
    background: url(../img/contact/bg.png);
    background-size: cover;
    position: relative;
}

.contact_title {
    font-size: 29px;
    color: #140e0a;
    margin-bottom: 40px;
}

.contact_inner {
    margin: 0;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.15);
    align-items: center;
}

.contact_form {
    padding: 50px;
}

.max-width-60 {
    max-width: 60px;
}

.office_area .address-inner {
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.07);
    padding: 35px 25px;
    border: 1px solid #ddd;
	min-height: 181px;
	vertical-align: middle;
}

.sidebar_box .contact_title {
    font-size: 22px;
    margin-bottom: 20px;
}

.schedul_footer {
    max-width: 300px;
    margin: 0 auto;
}

.sidebar_schedule {
    background: url(../img/sidebar-schedule.jpg);
    background-size: cover;
    position: relative;
}

.sidebar_schedule:before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    display: block;
    background: rgba(0, 0, 0, 0.85);
}

.sidebar_schedule p,
.sidebar_schedule .contact_title {
    color: #fff;
    position: relative;
}

.schedule {
    border: 1px solid #fff;
    position: relative;
    padding: 15px;
}

.contact p {
    font-size: 16px;
    color: #140e0a;
}

.contact h3 {
    color: #140e0a;
    font-weight: 500;
    font-size: 19px;
    font-family: 'Roboto', sans-serif;
}

.contact h3:first-child {
    color: #ff6600;
    font-size: 25px;
    font-weight: 800;
}

.contact_area .simple_btn {
    margin-top: 8px;
    width: 100%;
    line-height: 45px;
    font-size: 16px;
    text-transform: capitalize;
}

/*-----------------------------
    Team Area
-------------------------------*/

.member-social-bookmark ul {
    list-style: outside none none;
    margin: 0;
    padding: 0;
    text-align: center;
}

.member-social-bookmark ul li a {
    color: #fff;
    display: inline-block;
    font-size: 18px;
    height: 100%;
    padding-top: 8px;
    width: 100%;
}

.member-social-bookmark ul li a:hover {
    color: #d0963e;
}

.team-member-img {
    position: relative;
    -webkit-transition: all 0.3s ease 0s;
    transition: all 0.3s ease 0s;
    border-radius: 0;
    display: block;
    overflow: hidden;
}

.team-member-img:before,
.team-member-img:after {
    content: '';
    position: absolute;
    bottom: -18px;
    display: block;
    width: 100%;
    height: 48px;
    background: #fff;
}

.team-member-img:before {
    transform: rotate(15deg);
    left: -35%;
}

.team-member-img:after {
    right: -35%;
    transform: rotate(-15deg);
}

.team-member-img img {
    width: 100%;
}

.member-details {
    background: #fff;
    width: 82%;
    margin: 0 auto;
    position: relative;
    top: -50px;
    padding: 25px 20px;
    -webkit-box-shadow: 0px 1px 1px 1px rgba(88, 88, 88, 0.15);
    -moz-box-shadow: 0px 1px 1px 1px rgba(88, 88, 88, 0.15);
    box-shadow: 0px 1px 1px 1px rgba(88, 88, 88, 0.15);
}

.member-details .member_title {
    color: #e7272d;
}

.member-details h5:before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    height: 3px;
    display: block;
    background: #e7272d;
}

/*--------------------
   Newsletter 
----------------------*/

.newsletter_area {
    position: relative;
    background: url(../images/background/newsletter.jpg);
    background-size: cover;
    background-attachment: fixed;
    background-repeat: no-repeat;
}

.newsletter_area:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: block;
    background: rgba(0, 0, 0, 0.85);
}

.newsletter_area h2,
.newsletter_area p {
    color: #fff;
}

.newsletter_area .newsletter-form {
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    display: -o-flex;
    display: flex;
}

.newsletter_area .form-control {
    height: 50px;
    border-radius: 0;
    padding: 0 25px;
    margin: 0 auto;
}

.newsletter_area .btn {
    background: #e7272d;
    color: #fff;
    border-radius: 0;
    padding: 0 25px;
    text-transform: capitalize;
}

.newsletter_area .btn:hover {
    background: #06a548;
}

.map_area {
    position: relative;
}

.map_area .simple_btn:before {
    z-index: 0;
}

.map_area .simple_btn span {
    position: relative;
}

#map {
    width: 100%;
    height: 390px;
}

.office_address {
    position: absolute;
    right: 60px;
    width: 30%;
    height: 100%;
    background: #1a2636;
    padding: 80px 50px;
    border: 20px solid rgba(255, 255, 255, 0.05);
}

.office_address h2,
.office_address a,
.office_address address,
.office_address .simple_btn {
    color: #fff;
}

/*------------------------------
    Cart AREA
-------------------------------*/
.shopping_cart .table thead {
    background: #f1f1f1;
}

.cart-totals h2,
.shipping-form h2,
.coupon-form h2 {
    font-size: 19px;
    color: #212121;
    margin: 0 0 5px;
    font-weight: 700;
}

.shipping-form p,
.coupon-form p {
    color: rgba(0, 0, 0, 0.74);
    margin-bottom: 20px;
}

.cart-totals .cart-totals-border {
    border: 1px solid #dedede;
}

.cart-totals .totals-item {
    clear: both;
    width: 100%;
    margin-bottom: 15px;
    border-bottom: 1px solid #ebebeb;
    font-size: 14px;
}

.cart-totals .totals-item:last-child {
    margin-bottom: 0;
    border-bottom: 0;
}

.cart-totals .totals-item label {
    width: 79%;
}

.cart-totals .totals-item .totals-value {
    float: right;
    width: 21%;
    text-align: right;
}

.checkout {
    text-align: center;
    border: 0;
    padding: 5px 15px;
    color: #fff;
    display: block;
    border-radius: 4px;
    width: 100%;
    background: #e7272d;
    margin-top: 20px;
    line-height: 40px;
    font-size: 17px;
}

.checkout:hover {
    background: #212121;
    color: #fff;
}

.calculate-content .form-control {
    border-radius: 0;
    border: 1px solid #dedede;
    font-size: 14px;
    color: #999;
}

.calculate-content .btn {
    font-size: 13px;
    color: #fff;
    background-color: #e7272d;
    border-color: #e7272d;
}

.calculate-content .btn:hover {
    background-color: #0a9246;
    border-color: #0a9246;
}

.shipping-form .form-control::placeholder {
    font-size: 14px;
    color: #999;
}


/*------------------------------
    Checkout AREA
-------------------------------*/

.total-cost tbody tr td:last-child {
    text-align: right;
}

.checkout-box .simple_btn {
    display: block;
    text-align: center;
}

.checkout_area .panel-title a {
    font-size: 13px;
}

.check_order {
    padding: 25px;
    border: 1px solid #dee2e6;
}

.checkout_area .panel-heading {
    border-bottom: 1px solid #ddd;
}

.checkout_area .panel-heading .panel-title {
    margin: 0;
    padding: 10px;
}

.checkout_area .panel-heading .panel-title a {
    color: #e7272d;
}

.checkout_area .panel-collapse .panel-body {
    padding: 25px 10px;
}

.checkout_area label,
.checkout_area .form-group span,
.checkout_area .form-control {
    font-size: 14px;
}

/*------------------------------
    FOOTER AREA
-------------------------------*/

.footer-area {
    background: #1d1d1d;
    color: #cacaca;
    letter-spacing: 1px;
    position: relative;
}

.footer-inner {
    padding: 80px 0;
}

.footer_bottom {
    border-top: 1px solid #383739;
}

.footer-area a {
    color: #fafafa;
}

.footer-area p {
    color: #fafafa;
    font-size: 17px;
    font-weight: 600;
}

.footer-area a:hover {
    color: #e7272d;
}

.footer-logo,
.footer-address {
    margin-bottom: 40px;
}

.footer-init {
    max-width: 80%;
    margin-bottom: 25px;
}

.footer_widget h4 {
    color: #fff;
    margin: 20px 0 33px;
}

.footer-social-bookmark ul,
.footer-menu ul {
    list-style: outside none none;
    margin: 0;
    padding: 0;
}

.footer-social-bookmark ul li,
.footer-menu ul li {
    display: inline;
}

.footer-social-bookmark ul li a {
    display: inline-block;
    font-size: 23px;
    width: 35px;
    color: #c4c3c3;
}

.footer-social-bookmark a:hover {
    color: #e7272d;
}

.footer-menu ul {
    text-align: right;
}

.footer-menu ul li a {
    display: inline-block;
    padding: 0 5px;
    font-size: 14px;
}

.footer-menu {
    padding: 20px 0;
}

.footer-copyright {
    letter-spacing: 1px;
    padding: 20px 0;
}

.footer-copyright p {
    margin-bottom: 0;
}

.checkout_w_2{
    width:100px;
}

.default_w_120{
    width:120px;
}
.addonsitem_new_class{
    border-bottom: 1px solid #e9ecef;
    margin: 15px 0;
}
.classic-home-top{display:inline;color: #e7272d;}
.classic-badge{color: #fff; background: #28a745; border-radius: 50px; width: 22px;line-height: 22px; padding:0;}
.classic-badge2{color:#04be51; background:#f8f9fa; border-radius: 50px; width: 22px;line-height: 22px; padding:0; position: absolute; top: -2px;right: -20px;}
.badgedisplaynone{display:none !important}
.badgedisplayblock{display:block !important}
.btnposition {position:relative}
.classic-login{cursor:pointer; color:#FFF;}
.classic-lostpass{}
.cart_padding_15px{
 padding-left:15px;
}
.cart_w_96{
  width: 96%;
}
.cart_w_100{
  width: 100%;
}
.cartlist_display_none{
    display:none;
}
.cartlistqr{
    border-radius: 0;
}
.cartlistqrbtn{
   background:#686868;color:#fff;border-radius: 0;
}
.cartlist_d{
    display:flex; 
    align-items: center;
}
.completecss{
    text-align:center;
     display:none;
}

.details_w_50{
display:inline; width:50%;
}
.details_w_100{
    width:100%;
}
.details_w_80{
    width: 80%
}
.details_w_60{
    width: 60%
}
.details_w_40{
    width: 40%
}
.details_w_20{
    width: 20%
}

.nav_link_in{
  display:inline;color: #fff;
}
.badge_color_in{
    color: #fff; background: #28a745; 
    border-radius: 50px; 
    width: 22px;
    line-height: 22px;
     padding:0;
}
.login_pa_cursor{
    cursor:pointer;
    margin-left:30px; 
    color:#090 !important;
}
.login_pa_cursors{
cursor:pointer; 
color:#FFF !important;    
}

.pop_up_view_bottom{
  margin-bottom:0;
}
.pop_up_view_bottom_15px{
    margin-bottom:15px;
}

.updatecart_pointer{
cursor: pointer;
}

.updatecart_padding_0{
   padding:0;
}
.updatecart_border_none{
border:none;
}
.updatecart_padding_botom{
    padding-bottom: 65px;
}

.rma_display_none{
   display:none;
}
