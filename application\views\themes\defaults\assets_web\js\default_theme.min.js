"use strict";function checkavailablity(){var getdate=$("#reservation_date").val(),time=$("#reservation_time").val(),people=$("#reservation_person").val(),geturl=$("#checkurl").val();basicinfo.reservationopen;if(""==getdate)return alert(lang.select_date),!1;if(""==time)return alert(lang.select_time),!1;if(""==people||0==people)return alert(lang.enter_number_of_people),!1;var currentDate=new Date,intime=time.split(":"),day=currentDate.getDate(),month=currentDate.getMonth()+1,year=(currentDate.getHours(),currentDate.getFullYear());if(Date.parse(year+"-"+month+"-"+day)==Date.parse(getdate))var checkhour=currentDate.setHours(currentDate.getHours()+1),inputtime=new Date(checkhour).setHours(intime[0],intime[1],0);if(checkhour>=inputtime)return swal("Invalid",lang.select_after_hour_current_time,"warning"),!1;var dataString="getdate="+getdate+"&time="+time+"&people="+people+"&csrf_test_name="+basicinfo.csrftokeng;$.ajax({type:"POST",url:geturl,data:dataString}).done((function(data,textStatus,jQxhr){1==data?swal("Invalid",lang.no_free_seat_to_the_reservation,"warning"):2==data?swal("Closed",lang.our_service_is_closed_on_this_date_and_time,"warning"):$("#searchreservation").html(data)})).fail((function(jqXhr,textStatus,errorThrown){alert(lang.posting_failed),console.log(errorThrown)}))}function editreserveinfo(id){var myurl=$("#url_"+id).val()+"/"+id,dataString="id="+id+"&sdate="+$("#sldate").val()+"&sltime="+$("#sltime").val()+"&people="+$("#people").val()+"&csrf_test_name="+basicinfo.csrftokeng;$.ajax({type:"POST",url:myurl,data:dataString,success:function(data){$(".editinfo").html(data);$("#time, #reservation_time").clockpicker({placement:"bottom",align:"left",autoclose:!0,default:"now"});$("#edit").modal("show"),$(".datepicker4").datepicker({dateFormat:"dd-mm-yy"})}})}function addonsitem(id,sid,type){var myurl=basicinfo.baseurl+"hungry/addonsitem/"+id,dataString="pid="+id+"&sid="+sid+"&type="+type+"&csrf_test_name="+basicinfo.csrftokeng;$.ajax({type:"POST",url:myurl,data:dataString,success:function(data){$(".addonsinfo").html(data),$("#addons").modal("show")}})}function searchmenu(id){$("#loadingcon").show();var myurl=basicinfo.baseurl+"searchitem/",dataString="catid="+id+"&csrf_test_name="+basicinfo.csrftokeng;$.ajax({type:"POST",url:myurl,data:dataString,success:function(data){$("#loadingcon").hide(),$("#loaditem").html(data)}})}function addtocartitem(pid,id,type){var itemname=$("#itemname_"+id+type).val(),sizeid=$("#sizeid_"+id+type).val(),varientname=$("#varient_"+id+type).val(),qty=$("#sst6"+id+"_"+type).val(),price=$("#itemprice_"+id+type).val(),catid=$("#catid_"+id+type).val(),ismenupage=$("#cartpage"+id+type).val(),myurl=basicinfo.baseurl+"hungry/addtocart/",dataString="pid="+pid+"&itemname="+itemname+"&varientname="+varientname+"&qty="+qty+"&price="+price+"&catid="+catid+"&sizeid="+sizeid+"&csrf_test_name="+basicinfo.csrftokeng;$.ajax({type:"POST",url:myurl,data:dataString,success:function(data){if(0==ismenupage){$("#cartitem").html(data);var items=$("#totalitem").val();$(".my-cart-badge").html(items)}else{$("#cartitem").html(data);items=$("#totalitem").val();$(".my-cart-badge").html(items)}var x=document.getElementById("snackbar"+id);x.className="snackbar show",setTimeout((function(){x.className=x.className.replace("snackbar show","snackbar")}),3e3)}})}function addonsfoodtocart(pid,id,type){var addons=[],adonsqty=[],allprice=0,adonsprice=[],adonsname=[];$('input[name="addons"]:checked').each((function(){var adnsid=$(this).val(),adsqty=$("#addonqty_"+adnsid).val();adonsqty.push(adsqty),addons.push($(this).val()),allprice+=parseFloat($(this).attr("role"))*parseInt(adsqty),adonsprice.push($(this).attr("role")),adonsname.push($(this).attr("title"))}));var catid=$("#catid_"+id+type).val(),itemname=$("#itemname_"+id+type).val(),sizeid=$("#sizeid_"+id+type).val(),varientname=$("#varient_"+id+type).val(),qty=$("#sst6"+id+"_"+type).val(),price=$("#itemprice_"+id+type).val(),ismenupage=$("#cartpage"+id+type).val(),myurl=basicinfo.baseurl+"hungry/addtocart/",dataString="pid="+pid+"&itemname="+itemname+"&varientname="+varientname+"&qty="+qty+"&price="+price+"&catid="+catid+"&sizeid="+sizeid+"&addonsid="+addons+"&allprice="+allprice+"&adonsunitprice="+adonsprice+"&adonsqty="+adonsqty+"&adonsname="+adonsname+"&csrf_test_name="+basicinfo.csrftokeng;$.ajax({type:"POST",url:myurl,data:dataString,success:function(data){if(0==ismenupage){$("#cartitem").html(data),$("#addons").modal("hide");var items=$("#totalitem").val();$(".my-cart-badge").html(items)}else{$("#cartitem").html(data),$("#addons").modal("hide");items=$("#totalitem").val();$(".my-cart-badge").html(items)}var x=document.getElementById("snackbar"+id);x.className="snackbar show",setTimeout((function(){x.className=x.className.replace("snackbar show","snackbar")}),3e3)}})}function addonsfoodtocartmulti(pid,id,type){var addons=[],adonsqty=[],allprice=0,adonsprice=[],adonsname=[];$('input[name="addons"]:checked').each((function(){var adnsid=$(this).val(),adsqty=$("#addonqty_"+adnsid).val();adonsqty.push(adsqty),addons.push($(this).val()),allprice+=parseFloat($(this).attr("role"))*parseInt(adsqty),adonsprice.push($(this).attr("role")),adonsname.push($(this).attr("title"))}));var catid=$("#catid_"+id+type).val(),itemname=$("#itemname_"+id+type).val(),sizeid=$("#sizeid_"+id+type).val(),varientname=$("#varient_"+id+type).val(),qty=$("#sst6"+id+"_"+type).val(),price=$("#itemprice_"+id+type).val(),ismenupage=$("#cartpage"+id+type).val(),myurl=basicinfo.baseurl+"hungry/addtocart/",dataString="pid="+pid+"&itemname="+itemname+"&varientname="+varientname+"&qty="+qty+"&price="+price+"&catid="+catid+"&sizeid="+sizeid+"&addonsid="+addons+"&allprice="+allprice+"&adonsunitprice="+adonsprice+"&adonsqty="+adonsqty+"&adonsname="+adonsname+"&csrf_test_name="+basicinfo.csrftokeng;$.ajax({type:"POST",url:myurl,data:dataString,success:function(data){if(0==ismenupage){$("#cartitem").html(data);var items=$("#totalitem").val();$(".my-cart-badge").html(items)}else{$("#cartitem").html(data);items=$("#totalitem").val();$(".my-cart-badge").html(items)}var x=document.getElementById("snackbar"+id);x.className="snackbar show",setTimeout((function(){x.className=x.className.replace("snackbar show","snackbar")}),3e3)}})}function addonsitem2(id,sid,type){var myurl=basicinfo.baseurl+"hungry/addonsitem/"+id,dataString="pid="+id+"&sid="+sid+"&type="+type+"&csrf_test_name="+basicinfo.csrftokeng;$.ajax({type:"POST",url:myurl,data:dataString,success:function(data){$(".addonsinfo").html(data),$("#addons").modal("show")}})}function addtocartitem2(pid,id,type){var itemname=$("#itemname2_"+id+type).val(),sizeid=$("#sizeid2_"+id+type).val(),varientname=$("#varient2_"+id+type).val(),qty=$("#sst6"+id+"_"+type).val(),price=$("#itemprice2_"+id+type).val(),catid=$("#catid2_"+id+type).val(),ismenupage=$("#cartpage2"+id+type).val(),myurl=basicinfo.baseurl+"hungry/addtocart/",dataString="pid="+pid+"&itemname="+itemname+"&varientname="+varientname+"&qty="+qty+"&price="+price+"&catid="+catid+"&sizeid="+sizeid+"&csrf_test_name="+basicinfo.csrftokeng;$.ajax({type:"POST",url:myurl,data:dataString,success:function(data){if(0==ismenupage){$("#cartitem").html(data);var items=$("#totalitem").val();$(".my-cart-badge").html(items)}else{$("#cartitem").html(data);items=$("#totalitem").val();$(".my-cart-badge").html(items)}var x=document.getElementById("snackbar"+id);x.className="snackbar show",setTimeout((function(){x.className=x.className.replace("snackbar show","snackbar")}),3e3)}})}function removecart(rid){var geturl=basicinfo.baseurl+"hungry/removetocart",dataString="rowid="+rid+"&csrf_test_name="+basicinfo.csrftokeng;$.ajax({type:"POST",url:geturl,data:dataString,success:function(data){$("#cartitem").html(data);var items=$("#totalitem").val();$(".my-cart-badge").html(items)}})}function updatecart(id,qty,status){if("del"==status&&0==qty)return!1;var geturl=basicinfo.baseurl+"hungry/cartupdate",dataString="CartID="+id+"&qty="+qty+"&Udstatus="+status+"&csrf_test_name="+basicinfo.csrftokeng;$.ajax({type:"POST",url:geturl,data:dataString,success:function(data){$("#reloadcart").html(data)}})}function removetocart(rid){var geturl=basicinfo.baseurl+"hungry/removetocartdetails",dataString="rowid="+rid+"&csrf_test_name="+basicinfo.csrftokeng;$.ajax({type:"POST",url:geturl,data:dataString,success:function(data){$("#reloadcart").html(data)}})}function getcheckbox(price,name){var servicecharge=price;$("#scharge").text(servicecharge),$("#servicename").val(name),$("#getscharge").val(servicecharge);var vat=$("#vat").text(),discount=$("#discount").text(),totalprice=$("#subtotal").text(),coupondis=$("#coupdiscount").text(),grandtotal=parseFloat(totalprice)+parseFloat(vat)+parseFloat(servicecharge)-(parseFloat(discount)+parseFloat(coupondis));if(1==basicinfo.isvatinclusive)grandtotal=parseFloat(totalprice)+parseFloat(servicecharge)-(parseFloat(discount)+parseFloat(coupondis));else grandtotal=parseFloat(totalprice)+parseFloat(vat)+parseFloat(servicecharge)-(parseFloat(discount)+parseFloat(coupondis));grandtotal=grandtotal.toFixed(2);console.log({grandtotal:grandtotal}),$("#grtotal").text(grandtotal);var geturl=basicinfo.baseurl+"hungry/setshipping",dataString="shippingcharge="+price+"&shipname="+name+"&csrf_test_name="+basicinfo.csrftokeng;$.ajax({type:"POST",url:geturl,data:dataString,success:function(data){}})}function gotocheckout(){var error=0,getdate=$("#orderdate").val(),time=$("#reservation_time").val(),isopen=0,dataString="getdate="+getdate+"&time="+time+"&csrf_test_name="+basicinfo.csrftokeng;return $.ajax({async:!1,type:"POST",global:!1,dataType:"json",url:basicinfo.baseurl+"hungry/checkopenclose",data:dataString,success:function(data){isopen=data.isopen}}),0==isopen?(swal("Closed",lang.closed_msg+" "+basicinfo.opentime+" - "+basicinfo.closetime,"warning"),!1):0===$('input[name="payment_method"]:checked').length?(error=1,alert(lang.please_select_shipping_method),!1):void(0==error&&(window.location.href=basicinfo.baseurl+"checkout"))}function IsEmail(email){return/^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/.test(email)}function subscribeemail(){var email=$("#youremail").val();if(""==email)return alert(lang.please_enter_your_email),!1;if(!IsEmail(email))return alert(lang.please_enter_valid_email),!1;var geturl=basicinfo.baseurl+"hungry/subscribe",dataString="email="+email+"&csrf_test_name="+basicinfo.csrftokeng;$.ajax({type:"POST",url:geturl,data:dataString,success:function(data){swal("Success",lang.thanks_for_subscription,"success")}})}function itemnote(rowid,notes){$("#foodnote").val(notes),$("#foodcartid").val(rowid),$("#vieworder").modal("show")}function addnotetoitem(){var rowid=$("#foodcartid").val(),note=$("#foodnote").val(),geturl=basicinfo.baseurl+"hungry/additemnote",dataString="foodnote="+note+"&rowid="+rowid+"&csrf_test_name="+basicinfo.csrftokeng;$.ajax({type:"POST",url:geturl,data:dataString,success:function(data){alert(lang.note_added),$("#reloadcart").html(data),$("#vieworder").modal("hide")}})}$(document).on("click",".sa-clicon",(function(){swal.close()})),$(document).on("change","#varientinfo",(function(){var id=$("#varientinfo").val(),name=$("#varientinfo option:selected").data("title"),price=$("#varientinfo option:selected").data("price");$("#sizeid_1other").val(id),$("#size_1other").val(name),$("#sizeid_1menu").val(id),$("#size_1menu").val(name),$("#varient_1menu").val(name),$("#itemprice_1other").val(price),$("#itemprice_1menu").val(price),$("#vprice").text(price)})),$(document).on("change","#varientinfodt",(function(){var id=$("#varientinfodt").val(),name=$("#varientinfodt option:selected").data("title"),price=$("#varientinfodt option:selected").data("price"),pid=$("#dpid").val(),isaddons=$("#isaddons").val();$("#sizeid_999det").val(id),$("#varient_999det").val(name),$("#itemprice_999det").val(price),$("#vpricedt").text(price),1==isaddons&&$("#chng_"+pid).attr("onclick","addonsitem("+pid+","+id+',"other")')})),$(".leftSidebar, .mainContent, .rightSidebar").theiaStickySidebar(),$((function(){$("#navbarTogglerDemo03 ul li a").filter((function(){return this.href==location.href})).parent().addClass("active").siblings().removeClass("active"),$("#navbarTogglerDemo03 ul li").click((function(){$(this).parent().addClass("active").siblings().removeClass("active")}))}));