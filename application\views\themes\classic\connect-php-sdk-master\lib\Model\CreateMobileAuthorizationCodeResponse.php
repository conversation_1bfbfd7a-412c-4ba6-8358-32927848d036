<?php
/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace SquareConnect\Model;

use \ArrayAccess;
/**
 * CreateMobileAuthorizationCodeResponse Class Doc Comment
 *
 * @category Class
 * @package  SquareConnect
 * <AUTHOR> Inc.
 * @license  http://www.apache.org/licenses/LICENSE-2.0 Apache License v2
 * @link     https://squareup.com/developers
 */
class CreateMobileAuthorizationCodeResponse implements ArrayAccess
{
    /**
      * Array of property to type mappings. Used for (de)serialization 
      * @var string[]
      */
    static $swaggerTypes = array(
        'authorization_code' => 'string',
        'expires_at' => 'string',
        'error' => '\SquareConnect\Model\Error'
    );
  
    /** 
      * Array of attributes where the key is the local name, and the value is the original name
      * @var string[] 
      */
    static $attributeMap = array(
        'authorization_code' => 'authorization_code',
        'expires_at' => 'expires_at',
        'error' => 'error'
    );
  
    /**
      * Array of attributes to setter functions (for deserialization of responses)
      * @var string[]
      */
    static $setters = array(
        'authorization_code' => 'setAuthorizationCode',
        'expires_at' => 'setExpiresAt',
        'error' => 'setError'
    );
  
    /**
      * Array of attributes to getter functions (for serialization of requests)
      * @var string[]
      */
    static $getters = array(
        'authorization_code' => 'getAuthorizationCode',
        'expires_at' => 'getExpiresAt',
        'error' => 'getError'
    );
  
    /**
      * $authorization_code Generated authorization code that connects a mobile application instance to a Square account.
      * @var string
      */
    protected $authorization_code;
    /**
      * $expires_at The timestamp when `authorization_code` expires in [RFC 3339](https://tools.ietf.org/html/rfc3339) format, e.g., \"2016-09-04T23:59:33.123Z\".
      * @var string
      */
    protected $expires_at;
    /**
      * $error An error object that provides details about how creation of authorization code failed.
      * @var \SquareConnect\Model\Error
      */
    protected $error;

    /**
     * Constructor
     * @param mixed[] $data Associated array of property value initializing the model
     */
    public function __construct(array $data = null)
    {
        if ($data != null) {
            if (isset($data["authorization_code"])) {
              $this->authorization_code = $data["authorization_code"];
            } else {
              $this->authorization_code = null;
            }
            if (isset($data["expires_at"])) {
              $this->expires_at = $data["expires_at"];
            } else {
              $this->expires_at = null;
            }
            if (isset($data["error"])) {
              $this->error = $data["error"];
            } else {
              $this->error = null;
            }
        }
    }
    /**
     * Gets authorization_code
     * @return string
     */
    public function getAuthorizationCode()
    {
        return $this->authorization_code;
    }
  
    /**
     * Sets authorization_code
     * @param string $authorization_code Generated authorization code that connects a mobile application instance to a Square account.
     * @return $this
     */
    public function setAuthorizationCode($authorization_code)
    {
        $this->authorization_code = $authorization_code;
        return $this;
    }
    /**
     * Gets expires_at
     * @return string
     */
    public function getExpiresAt()
    {
        return $this->expires_at;
    }
  
    /**
     * Sets expires_at
     * @param string $expires_at The timestamp when `authorization_code` expires in [RFC 3339](https://tools.ietf.org/html/rfc3339) format, e.g., \"2016-09-04T23:59:33.123Z\".
     * @return $this
     */
    public function setExpiresAt($expires_at)
    {
        $this->expires_at = $expires_at;
        return $this;
    }
    /**
     * Gets error
     * @return \SquareConnect\Model\Error
     */
    public function getError()
    {
        return $this->error;
    }
  
    /**
     * Sets error
     * @param \SquareConnect\Model\Error $error An error object that provides details about how creation of authorization code failed.
     * @return $this
     */
    public function setError($error)
    {
        $this->error = $error;
        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     * @param  integer $offset Offset 
     * @return boolean
     */
    public function offsetExists($offset)
    {
        return isset($this->$offset);
    }
  
    /**
     * Gets offset.
     * @param  integer $offset Offset 
     * @return mixed 
     */
    public function offsetGet($offset)
    {
        return $this->$offset;
    }
  
    /**
     * Sets value based on offset.
     * @param  integer $offset Offset 
     * @param  mixed   $value  Value to be set
     * @return void
     */
    public function offsetSet($offset, $value)
    {
        $this->$offset = $value;
    }
  
    /**
     * Unsets offset.
     * @param  integer $offset Offset 
     * @return void
     */
    public function offsetUnset($offset)
    {
        unset($this->$offset);
    }
  
    /**
     * Gets the string presentation of the object
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) {
            return json_encode(\SquareConnect\ObjectSerializer::sanitizeForSerialization($this), JSON_PRETTY_PRINT);
        } else {
            return json_encode(\SquareConnect\ObjectSerializer::sanitizeForSerialization($this));
        }
    }
}
