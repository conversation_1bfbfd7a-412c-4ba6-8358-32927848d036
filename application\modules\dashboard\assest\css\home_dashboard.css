body {
  font-family: "Poppins", sans-serif;
}
.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Poppins", sans-serif;
}
.lifeord {
  font-size: 15px;
}
.home-panel-bd {
  height: 100px;
}
.inbox-item .inbox-item-customer-name {
  margin: 0;
  padding: 0;
}
.order_status-new {
  margin-top: -30px;
}
.bg-alice-blue {
  background-color: #f5f7fa;
}
.text-orange {
  color: #ff7b4e;
}
.text-violet {
  color: #9052f5;
}
.text-red {
  color: #e3647d;
}
.text-green {
  color: #00a653;
}
.fs-24 {
  font-size: 24px;
}
.rounded-15 {
  border-radius: 15px;
}
.rounded-10 {
  border-radius: 10px;
}
.bg-soft-green {
  background-color: #f2f9f6;
}
.bg-soft-warning {
  background-color: #fff8ed;
}
.shadow-1 {
  box-shadow: 0px 0px 6px 0px #0000001a;
}
.fw-600 {
  font-weight: 600;
}
.p-12 {
  padding: 12px;
}
.p-15 {
  padding: 15px;
}
.justify-content-between {
  justify-content: space-between;
}
.custom-date-control {
  background-color: #fff !important;
  border-radius: 6px;
}


.top-sell-table{
  height: 418px;
  overflow-y: auto;
  overflow-x: hidden;
}

.top-sell-table::-webkit-scrollbar {
  width: 3px;
}


.top-sell-table::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px white;
  border-radius: 5px;
}


.top-sell-table::-webkit-scrollbar-thumb {
  background: #999999;
  border-radius: 5px;
}