<?php
/**
 * NOTE: This class is auto generated by the swagger code generator program. 
 * https://github.com/swagger-api/swagger-codegen 
 * Do not edit the class manually.
 */

namespace SquareConnect\Api;

use \SquareConnect\Configuration;
use \SquareConnect\ApiClient;
use \SquareConnect\ApiException;
use \SquareConnect\ObjectSerializer;

/**
 * V1ItemsApi Class Doc Comment
 *
 * @category Class
 * @package  SquareConnect
 * <AUTHOR> Inc.
 * @license  http://www.apache.org/licenses/LICENSE-2.0 Apache Licene v2
 * @link     https://squareup.com/developers
 */
class V1ItemsApi
{

    /**
     * API Client
     * @var \SquareConnect\ApiClient instance of the ApiClient
     */
    protected $apiClient;
  
    /**
     * Constructor
     * @param \SquareConnect\ApiClient|null $apiClient The api client to use
     */
    function __construct($apiClient = null)
    {
        if ($apiClient == null) {
            $apiClient = new ApiClient();
            $apiClient->getConfig()->setHost('https://connect.squareup.com');
        }
  
        $this->apiClient = $apiClient;
    }
  
    /**
     * Get API client
     * @return \SquareConnect\ApiClient get the API client
     */
    public function getApiClient()
    {
        return $this->apiClient;
    }
  
    /**
     * Set the API client
     * @param \SquareConnect\ApiClient $apiClient set the API client
     * @return V1ItemsApi
     */
    public function setApiClient(ApiClient $apiClient)
    {
        $this->apiClient = $apiClient;
        return $this;
    }
  
    /**
     * adjustInventory
     *
     * Adjusts an item variation's current available inventory.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $variation_id The ID of the variation to adjust inventory information for. (required)
     * @param \SquareConnect\Model\V1AdjustInventoryRequest $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return \SquareConnect\Model\V1InventoryEntry
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function adjustInventory($location_id, $variation_id, $body)
    {
        list($response, $statusCode, $httpHeader) = $this->adjustInventoryWithHttpInfo ($location_id, $variation_id, $body);
        return $response; 
    }


    /**
     * adjustInventoryWithHttpInfo
     *
     * Adjusts an item variation's current available inventory.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $variation_id The ID of the variation to adjust inventory information for. (required)
     * @param \SquareConnect\Model\V1AdjustInventoryRequest $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return Array of \SquareConnect\Model\V1InventoryEntry, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function adjustInventoryWithHttpInfo($location_id, $variation_id, $body)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling adjustInventory');
        }
        // verify the required parameter 'variation_id' is set
        if ($variation_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $variation_id when calling adjustInventory');
        }
        // verify the required parameter 'body' is set
        if ($body === null) {
            throw new \InvalidArgumentException('Missing the required parameter $body when calling adjustInventory');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/inventory/{variation_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }// path params
        if ($variation_id !== null) {
            $resourcePath = str_replace(
                "{" . "variation_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($variation_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        // body params
        $_tempBody = null;
        if (isset($body)) {
            $_tempBody = $body;
        }
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'POST',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1InventoryEntry'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1InventoryEntry', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1InventoryEntry', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * applyFee
     *
     * Associates a fee with an item, meaning the fee is automatically applied to the item in Square Register.
     *
     * @param string $location_id The ID of the fee&#39;s associated location. (required)
     * @param string $item_id The ID of the item to add the fee to. (required)
     * @param string $fee_id The ID of the fee to apply. (required)
     * @return \SquareConnect\Model\V1Item
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function applyFee($location_id, $item_id, $fee_id)
    {
        list($response, $statusCode, $httpHeader) = $this->applyFeeWithHttpInfo ($location_id, $item_id, $fee_id);
        return $response; 
    }


    /**
     * applyFeeWithHttpInfo
     *
     * Associates a fee with an item, meaning the fee is automatically applied to the item in Square Register.
     *
     * @param string $location_id The ID of the fee&#39;s associated location. (required)
     * @param string $item_id The ID of the item to add the fee to. (required)
     * @param string $fee_id The ID of the fee to apply. (required)
     * @return Array of \SquareConnect\Model\V1Item, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function applyFeeWithHttpInfo($location_id, $item_id, $fee_id)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling applyFee');
        }
        // verify the required parameter 'item_id' is set
        if ($item_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $item_id when calling applyFee');
        }
        // verify the required parameter 'fee_id' is set
        if ($fee_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $fee_id when calling applyFee');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/items/{item_id}/fees/{fee_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }// path params
        if ($item_id !== null) {
            $resourcePath = str_replace(
                "{" . "item_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($item_id),
                $resourcePath
            );
        }// path params
        if ($fee_id !== null) {
            $resourcePath = str_replace(
                "{" . "fee_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($fee_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'PUT',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Item'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Item', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Item', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * applyModifierList
     *
     * Associates a modifier list with an item, meaning modifier options from the list can be applied to the item.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $modifier_list_id The ID of the modifier list to apply. (required)
     * @param string $item_id The ID of the item to add the modifier list to. (required)
     * @return \SquareConnect\Model\V1Item
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function applyModifierList($location_id, $modifier_list_id, $item_id)
    {
        list($response, $statusCode, $httpHeader) = $this->applyModifierListWithHttpInfo ($location_id, $modifier_list_id, $item_id);
        return $response; 
    }


    /**
     * applyModifierListWithHttpInfo
     *
     * Associates a modifier list with an item, meaning modifier options from the list can be applied to the item.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $modifier_list_id The ID of the modifier list to apply. (required)
     * @param string $item_id The ID of the item to add the modifier list to. (required)
     * @return Array of \SquareConnect\Model\V1Item, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function applyModifierListWithHttpInfo($location_id, $modifier_list_id, $item_id)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling applyModifierList');
        }
        // verify the required parameter 'modifier_list_id' is set
        if ($modifier_list_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $modifier_list_id when calling applyModifierList');
        }
        // verify the required parameter 'item_id' is set
        if ($item_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $item_id when calling applyModifierList');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/items/{item_id}/modifier-lists/{modifier_list_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }// path params
        if ($modifier_list_id !== null) {
            $resourcePath = str_replace(
                "{" . "modifier_list_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($modifier_list_id),
                $resourcePath
            );
        }// path params
        if ($item_id !== null) {
            $resourcePath = str_replace(
                "{" . "item_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($item_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'PUT',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Item'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Item', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Item', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * createCategory
     *
     * Creates an item category.
     *
     * @param string $location_id The ID of the location to create an item for. (required)
     * @param \SquareConnect\Model\V1Category $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return \SquareConnect\Model\V1Category
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function createCategory($location_id, $body)
    {
        list($response, $statusCode, $httpHeader) = $this->createCategoryWithHttpInfo ($location_id, $body);
        return $response; 
    }


    /**
     * createCategoryWithHttpInfo
     *
     * Creates an item category.
     *
     * @param string $location_id The ID of the location to create an item for. (required)
     * @param \SquareConnect\Model\V1Category $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return Array of \SquareConnect\Model\V1Category, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function createCategoryWithHttpInfo($location_id, $body)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling createCategory');
        }
        // verify the required parameter 'body' is set
        if ($body === null) {
            throw new \InvalidArgumentException('Missing the required parameter $body when calling createCategory');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/categories";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        // body params
        $_tempBody = null;
        if (isset($body)) {
            $_tempBody = $body;
        }
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'POST',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Category'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Category', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Category', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * createDiscount
     *
     * Creates a discount.
     *
     * @param string $location_id The ID of the location to create an item for. (required)
     * @param \SquareConnect\Model\V1Discount $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return \SquareConnect\Model\V1Discount
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function createDiscount($location_id, $body)
    {
        list($response, $statusCode, $httpHeader) = $this->createDiscountWithHttpInfo ($location_id, $body);
        return $response; 
    }


    /**
     * createDiscountWithHttpInfo
     *
     * Creates a discount.
     *
     * @param string $location_id The ID of the location to create an item for. (required)
     * @param \SquareConnect\Model\V1Discount $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return Array of \SquareConnect\Model\V1Discount, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function createDiscountWithHttpInfo($location_id, $body)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling createDiscount');
        }
        // verify the required parameter 'body' is set
        if ($body === null) {
            throw new \InvalidArgumentException('Missing the required parameter $body when calling createDiscount');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/discounts";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        // body params
        $_tempBody = null;
        if (isset($body)) {
            $_tempBody = $body;
        }
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'POST',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Discount'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Discount', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Discount', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * createFee
     *
     * Creates a fee (tax).
     *
     * @param string $location_id The ID of the location to create a fee for. (required)
     * @param \SquareConnect\Model\V1Fee $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return \SquareConnect\Model\V1Fee
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function createFee($location_id, $body)
    {
        list($response, $statusCode, $httpHeader) = $this->createFeeWithHttpInfo ($location_id, $body);
        return $response; 
    }


    /**
     * createFeeWithHttpInfo
     *
     * Creates a fee (tax).
     *
     * @param string $location_id The ID of the location to create a fee for. (required)
     * @param \SquareConnect\Model\V1Fee $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return Array of \SquareConnect\Model\V1Fee, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function createFeeWithHttpInfo($location_id, $body)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling createFee');
        }
        // verify the required parameter 'body' is set
        if ($body === null) {
            throw new \InvalidArgumentException('Missing the required parameter $body when calling createFee');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/fees";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        // body params
        $_tempBody = null;
        if (isset($body)) {
            $_tempBody = $body;
        }
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'POST',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Fee'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Fee', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Fee', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * createItem
     *
     * Creates an item and at least one variation for it.
     *
     * @param string $location_id The ID of the location to create an item for. (required)
     * @param \SquareConnect\Model\V1Item $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return \SquareConnect\Model\V1Item
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function createItem($location_id, $body)
    {
        list($response, $statusCode, $httpHeader) = $this->createItemWithHttpInfo ($location_id, $body);
        return $response; 
    }


    /**
     * createItemWithHttpInfo
     *
     * Creates an item and at least one variation for it.
     *
     * @param string $location_id The ID of the location to create an item for. (required)
     * @param \SquareConnect\Model\V1Item $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return Array of \SquareConnect\Model\V1Item, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function createItemWithHttpInfo($location_id, $body)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling createItem');
        }
        // verify the required parameter 'body' is set
        if ($body === null) {
            throw new \InvalidArgumentException('Missing the required parameter $body when calling createItem');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/items";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        // body params
        $_tempBody = null;
        if (isset($body)) {
            $_tempBody = $body;
        }
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'POST',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Item'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Item', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Item', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * createModifierList
     *
     * Creates an item modifier list and at least one modifier option for it.
     *
     * @param string $location_id The ID of the location to create a modifier list for. (required)
     * @param \SquareConnect\Model\V1ModifierList $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return \SquareConnect\Model\V1ModifierList
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function createModifierList($location_id, $body)
    {
        list($response, $statusCode, $httpHeader) = $this->createModifierListWithHttpInfo ($location_id, $body);
        return $response; 
    }


    /**
     * createModifierListWithHttpInfo
     *
     * Creates an item modifier list and at least one modifier option for it.
     *
     * @param string $location_id The ID of the location to create a modifier list for. (required)
     * @param \SquareConnect\Model\V1ModifierList $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return Array of \SquareConnect\Model\V1ModifierList, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function createModifierListWithHttpInfo($location_id, $body)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling createModifierList');
        }
        // verify the required parameter 'body' is set
        if ($body === null) {
            throw new \InvalidArgumentException('Missing the required parameter $body when calling createModifierList');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/modifier-lists";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        // body params
        $_tempBody = null;
        if (isset($body)) {
            $_tempBody = $body;
        }
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'POST',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1ModifierList'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1ModifierList', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1ModifierList', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * createModifierOption
     *
     * Creates an item modifier option and adds it to a modifier list.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $modifier_list_id The ID of the modifier list to edit. (required)
     * @param \SquareConnect\Model\V1ModifierOption $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return \SquareConnect\Model\V1ModifierOption
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function createModifierOption($location_id, $modifier_list_id, $body)
    {
        list($response, $statusCode, $httpHeader) = $this->createModifierOptionWithHttpInfo ($location_id, $modifier_list_id, $body);
        return $response; 
    }


    /**
     * createModifierOptionWithHttpInfo
     *
     * Creates an item modifier option and adds it to a modifier list.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $modifier_list_id The ID of the modifier list to edit. (required)
     * @param \SquareConnect\Model\V1ModifierOption $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return Array of \SquareConnect\Model\V1ModifierOption, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function createModifierOptionWithHttpInfo($location_id, $modifier_list_id, $body)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling createModifierOption');
        }
        // verify the required parameter 'modifier_list_id' is set
        if ($modifier_list_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $modifier_list_id when calling createModifierOption');
        }
        // verify the required parameter 'body' is set
        if ($body === null) {
            throw new \InvalidArgumentException('Missing the required parameter $body when calling createModifierOption');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/modifier-lists/{modifier_list_id}/modifier-options";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }// path params
        if ($modifier_list_id !== null) {
            $resourcePath = str_replace(
                "{" . "modifier_list_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($modifier_list_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        // body params
        $_tempBody = null;
        if (isset($body)) {
            $_tempBody = $body;
        }
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'POST',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1ModifierOption'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1ModifierOption', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1ModifierOption', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * createPage
     *
     * Creates a Favorites page in Square Register.
     *
     * @param string $location_id The ID of the location to create an item for. (required)
     * @param \SquareConnect\Model\V1Page $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return \SquareConnect\Model\V1Page
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function createPage($location_id, $body)
    {
        list($response, $statusCode, $httpHeader) = $this->createPageWithHttpInfo ($location_id, $body);
        return $response; 
    }


    /**
     * createPageWithHttpInfo
     *
     * Creates a Favorites page in Square Register.
     *
     * @param string $location_id The ID of the location to create an item for. (required)
     * @param \SquareConnect\Model\V1Page $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return Array of \SquareConnect\Model\V1Page, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function createPageWithHttpInfo($location_id, $body)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling createPage');
        }
        // verify the required parameter 'body' is set
        if ($body === null) {
            throw new \InvalidArgumentException('Missing the required parameter $body when calling createPage');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/pages";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        // body params
        $_tempBody = null;
        if (isset($body)) {
            $_tempBody = $body;
        }
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'POST',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Page'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Page', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Page', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * createVariation
     *
     * Creates an item variation for an existing item.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $item_id The item&#39;s ID. (required)
     * @param \SquareConnect\Model\V1Variation $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return \SquareConnect\Model\V1Variation
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function createVariation($location_id, $item_id, $body)
    {
        list($response, $statusCode, $httpHeader) = $this->createVariationWithHttpInfo ($location_id, $item_id, $body);
        return $response; 
    }


    /**
     * createVariationWithHttpInfo
     *
     * Creates an item variation for an existing item.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $item_id The item&#39;s ID. (required)
     * @param \SquareConnect\Model\V1Variation $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return Array of \SquareConnect\Model\V1Variation, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function createVariationWithHttpInfo($location_id, $item_id, $body)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling createVariation');
        }
        // verify the required parameter 'item_id' is set
        if ($item_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $item_id when calling createVariation');
        }
        // verify the required parameter 'body' is set
        if ($body === null) {
            throw new \InvalidArgumentException('Missing the required parameter $body when calling createVariation');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/items/{item_id}/variations";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }// path params
        if ($item_id !== null) {
            $resourcePath = str_replace(
                "{" . "item_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($item_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        // body params
        $_tempBody = null;
        if (isset($body)) {
            $_tempBody = $body;
        }
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'POST',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Variation'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Variation', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Variation', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * deleteCategory
     *
     * Deletes an existing item category.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $category_id The ID of the category to delete. (required)
     * @return \SquareConnect\Model\V1Category
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function deleteCategory($location_id, $category_id)
    {
        list($response, $statusCode, $httpHeader) = $this->deleteCategoryWithHttpInfo ($location_id, $category_id);
        return $response; 
    }


    /**
     * deleteCategoryWithHttpInfo
     *
     * Deletes an existing item category.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $category_id The ID of the category to delete. (required)
     * @return Array of \SquareConnect\Model\V1Category, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function deleteCategoryWithHttpInfo($location_id, $category_id)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling deleteCategory');
        }
        // verify the required parameter 'category_id' is set
        if ($category_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $category_id when calling deleteCategory');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/categories/{category_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }// path params
        if ($category_id !== null) {
            $resourcePath = str_replace(
                "{" . "category_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($category_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'DELETE',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Category'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Category', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Category', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * deleteDiscount
     *
     * Deletes an existing discount.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $discount_id The ID of the discount to delete. (required)
     * @return \SquareConnect\Model\V1Discount
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function deleteDiscount($location_id, $discount_id)
    {
        list($response, $statusCode, $httpHeader) = $this->deleteDiscountWithHttpInfo ($location_id, $discount_id);
        return $response; 
    }


    /**
     * deleteDiscountWithHttpInfo
     *
     * Deletes an existing discount.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $discount_id The ID of the discount to delete. (required)
     * @return Array of \SquareConnect\Model\V1Discount, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function deleteDiscountWithHttpInfo($location_id, $discount_id)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling deleteDiscount');
        }
        // verify the required parameter 'discount_id' is set
        if ($discount_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $discount_id when calling deleteDiscount');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/discounts/{discount_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }// path params
        if ($discount_id !== null) {
            $resourcePath = str_replace(
                "{" . "discount_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($discount_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'DELETE',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Discount'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Discount', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Discount', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * deleteFee
     *
     * Deletes an existing fee (tax).
     *
     * @param string $location_id The ID of the fee&#39;s associated location. (required)
     * @param string $fee_id The ID of the fee to delete. (required)
     * @return \SquareConnect\Model\V1Fee
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function deleteFee($location_id, $fee_id)
    {
        list($response, $statusCode, $httpHeader) = $this->deleteFeeWithHttpInfo ($location_id, $fee_id);
        return $response; 
    }


    /**
     * deleteFeeWithHttpInfo
     *
     * Deletes an existing fee (tax).
     *
     * @param string $location_id The ID of the fee&#39;s associated location. (required)
     * @param string $fee_id The ID of the fee to delete. (required)
     * @return Array of \SquareConnect\Model\V1Fee, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function deleteFeeWithHttpInfo($location_id, $fee_id)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling deleteFee');
        }
        // verify the required parameter 'fee_id' is set
        if ($fee_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $fee_id when calling deleteFee');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/fees/{fee_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }// path params
        if ($fee_id !== null) {
            $resourcePath = str_replace(
                "{" . "fee_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($fee_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'DELETE',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Fee'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Fee', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Fee', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * deleteItem
     *
     * Deletes an existing item and all item variations associated with it.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $item_id The ID of the item to modify. (required)
     * @return \SquareConnect\Model\V1Item
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function deleteItem($location_id, $item_id)
    {
        list($response, $statusCode, $httpHeader) = $this->deleteItemWithHttpInfo ($location_id, $item_id);
        return $response; 
    }


    /**
     * deleteItemWithHttpInfo
     *
     * Deletes an existing item and all item variations associated with it.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $item_id The ID of the item to modify. (required)
     * @return Array of \SquareConnect\Model\V1Item, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function deleteItemWithHttpInfo($location_id, $item_id)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling deleteItem');
        }
        // verify the required parameter 'item_id' is set
        if ($item_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $item_id when calling deleteItem');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/items/{item_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }// path params
        if ($item_id !== null) {
            $resourcePath = str_replace(
                "{" . "item_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($item_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'DELETE',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Item'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Item', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Item', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * deleteModifierList
     *
     * Deletes an existing item modifier list and all modifier options associated with it.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $modifier_list_id The ID of the modifier list to delete. (required)
     * @return \SquareConnect\Model\V1ModifierList
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function deleteModifierList($location_id, $modifier_list_id)
    {
        list($response, $statusCode, $httpHeader) = $this->deleteModifierListWithHttpInfo ($location_id, $modifier_list_id);
        return $response; 
    }


    /**
     * deleteModifierListWithHttpInfo
     *
     * Deletes an existing item modifier list and all modifier options associated with it.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $modifier_list_id The ID of the modifier list to delete. (required)
     * @return Array of \SquareConnect\Model\V1ModifierList, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function deleteModifierListWithHttpInfo($location_id, $modifier_list_id)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling deleteModifierList');
        }
        // verify the required parameter 'modifier_list_id' is set
        if ($modifier_list_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $modifier_list_id when calling deleteModifierList');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/modifier-lists/{modifier_list_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }// path params
        if ($modifier_list_id !== null) {
            $resourcePath = str_replace(
                "{" . "modifier_list_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($modifier_list_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'DELETE',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1ModifierList'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1ModifierList', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1ModifierList', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * deleteModifierOption
     *
     * Deletes an existing item modifier option from a modifier list.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $modifier_list_id The ID of the modifier list to delete. (required)
     * @param string $modifier_option_id The ID of the modifier list to edit. (required)
     * @return \SquareConnect\Model\V1ModifierOption
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function deleteModifierOption($location_id, $modifier_list_id, $modifier_option_id)
    {
        list($response, $statusCode, $httpHeader) = $this->deleteModifierOptionWithHttpInfo ($location_id, $modifier_list_id, $modifier_option_id);
        return $response; 
    }


    /**
     * deleteModifierOptionWithHttpInfo
     *
     * Deletes an existing item modifier option from a modifier list.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $modifier_list_id The ID of the modifier list to delete. (required)
     * @param string $modifier_option_id The ID of the modifier list to edit. (required)
     * @return Array of \SquareConnect\Model\V1ModifierOption, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function deleteModifierOptionWithHttpInfo($location_id, $modifier_list_id, $modifier_option_id)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling deleteModifierOption');
        }
        // verify the required parameter 'modifier_list_id' is set
        if ($modifier_list_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $modifier_list_id when calling deleteModifierOption');
        }
        // verify the required parameter 'modifier_option_id' is set
        if ($modifier_option_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $modifier_option_id when calling deleteModifierOption');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/modifier-lists/{modifier_list_id}/modifier-options/{modifier_option_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }// path params
        if ($modifier_list_id !== null) {
            $resourcePath = str_replace(
                "{" . "modifier_list_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($modifier_list_id),
                $resourcePath
            );
        }// path params
        if ($modifier_option_id !== null) {
            $resourcePath = str_replace(
                "{" . "modifier_option_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($modifier_option_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'DELETE',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1ModifierOption'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1ModifierOption', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1ModifierOption', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * deletePage
     *
     * Deletes an existing Favorites page and all of its cells.
     *
     * @param string $location_id The ID of the Favorites page&#39;s associated location. (required)
     * @param string $page_id The ID of the page to delete. (required)
     * @return \SquareConnect\Model\V1Page
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function deletePage($location_id, $page_id)
    {
        list($response, $statusCode, $httpHeader) = $this->deletePageWithHttpInfo ($location_id, $page_id);
        return $response; 
    }


    /**
     * deletePageWithHttpInfo
     *
     * Deletes an existing Favorites page and all of its cells.
     *
     * @param string $location_id The ID of the Favorites page&#39;s associated location. (required)
     * @param string $page_id The ID of the page to delete. (required)
     * @return Array of \SquareConnect\Model\V1Page, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function deletePageWithHttpInfo($location_id, $page_id)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling deletePage');
        }
        // verify the required parameter 'page_id' is set
        if ($page_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $page_id when calling deletePage');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/pages/{page_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }// path params
        if ($page_id !== null) {
            $resourcePath = str_replace(
                "{" . "page_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($page_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'DELETE',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Page'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Page', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Page', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * deletePageCell
     *
     * Deletes a cell from a Favorites page in Square Register.
     *
     * @param string $location_id The ID of the Favorites page&#39;s associated location. (required)
     * @param string $page_id The ID of the page to delete. (required)
     * @param string $row The row of the cell to clear. Always an integer between 0 and 4, inclusive. Row 0 is the top row. (optional)
     * @param string $column The column of the cell to clear. Always an integer between 0 and 4, inclusive. Column 0 is the leftmost column. (optional)
     * @return \SquareConnect\Model\V1Page
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function deletePageCell($location_id, $page_id, $row = null, $column = null)
    {
        list($response, $statusCode, $httpHeader) = $this->deletePageCellWithHttpInfo ($location_id, $page_id, $row, $column);
        return $response; 
    }


    /**
     * deletePageCellWithHttpInfo
     *
     * Deletes a cell from a Favorites page in Square Register.
     *
     * @param string $location_id The ID of the Favorites page&#39;s associated location. (required)
     * @param string $page_id The ID of the page to delete. (required)
     * @param string $row The row of the cell to clear. Always an integer between 0 and 4, inclusive. Row 0 is the top row. (optional)
     * @param string $column The column of the cell to clear. Always an integer between 0 and 4, inclusive. Column 0 is the leftmost column. (optional)
     * @return Array of \SquareConnect\Model\V1Page, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function deletePageCellWithHttpInfo($location_id, $page_id, $row = null, $column = null)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling deletePageCell');
        }
        // verify the required parameter 'page_id' is set
        if ($page_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $page_id when calling deletePageCell');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/pages/{page_id}/cells";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        // query params
        if ($row !== null) {
            $queryParams['row'] = $this->apiClient->getSerializer()->toQueryValue($row);
        }// query params
        if ($column !== null) {
            $queryParams['column'] = $this->apiClient->getSerializer()->toQueryValue($column);
        }
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }// path params
        if ($page_id !== null) {
            $resourcePath = str_replace(
                "{" . "page_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($page_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'DELETE',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Page'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Page', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Page', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * deleteVariation
     *
     * Deletes an existing item variation from an item.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $item_id The ID of the item to delete. (required)
     * @param string $variation_id The ID of the variation to delete. (required)
     * @return \SquareConnect\Model\V1Variation
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function deleteVariation($location_id, $item_id, $variation_id)
    {
        list($response, $statusCode, $httpHeader) = $this->deleteVariationWithHttpInfo ($location_id, $item_id, $variation_id);
        return $response; 
    }


    /**
     * deleteVariationWithHttpInfo
     *
     * Deletes an existing item variation from an item.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $item_id The ID of the item to delete. (required)
     * @param string $variation_id The ID of the variation to delete. (required)
     * @return Array of \SquareConnect\Model\V1Variation, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function deleteVariationWithHttpInfo($location_id, $item_id, $variation_id)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling deleteVariation');
        }
        // verify the required parameter 'item_id' is set
        if ($item_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $item_id when calling deleteVariation');
        }
        // verify the required parameter 'variation_id' is set
        if ($variation_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $variation_id when calling deleteVariation');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/items/{item_id}/variations/{variation_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }// path params
        if ($item_id !== null) {
            $resourcePath = str_replace(
                "{" . "item_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($item_id),
                $resourcePath
            );
        }// path params
        if ($variation_id !== null) {
            $resourcePath = str_replace(
                "{" . "variation_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($variation_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'DELETE',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Variation'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Variation', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Variation', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * listCategories
     *
     * Lists all of a location's item categories.
     *
     * @param string $location_id The ID of the location to list categories for. (required)
     * @return \SquareConnect\Model\V1Category[]
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function listCategories($location_id)
    {
        list($response, $statusCode, $httpHeader) = $this->listCategoriesWithHttpInfo ($location_id);
        return $response; 
    }


    /**
     * listCategoriesWithHttpInfo
     *
     * Lists all of a location's item categories.
     *
     * @param string $location_id The ID of the location to list categories for. (required)
     * @return Array of \SquareConnect\Model\V1Category[], HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function listCategoriesWithHttpInfo($location_id)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling listCategories');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/categories";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'GET',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Category[]'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Category[]', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Category[]', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * listDiscounts
     *
     * Lists all of a location's discounts.
     *
     * @param string $location_id The ID of the location to list categories for. (required)
     * @return \SquareConnect\Model\V1Discount[]
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function listDiscounts($location_id)
    {
        list($response, $statusCode, $httpHeader) = $this->listDiscountsWithHttpInfo ($location_id);
        return $response; 
    }


    /**
     * listDiscountsWithHttpInfo
     *
     * Lists all of a location's discounts.
     *
     * @param string $location_id The ID of the location to list categories for. (required)
     * @return Array of \SquareConnect\Model\V1Discount[], HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function listDiscountsWithHttpInfo($location_id)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling listDiscounts');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/discounts";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'GET',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Discount[]'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Discount[]', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Discount[]', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * listFees
     *
     * Lists all of a location's fees (taxes).
     *
     * @param string $location_id The ID of the location to list fees for. (required)
     * @return \SquareConnect\Model\V1Fee[]
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function listFees($location_id)
    {
        list($response, $statusCode, $httpHeader) = $this->listFeesWithHttpInfo ($location_id);
        return $response; 
    }


    /**
     * listFeesWithHttpInfo
     *
     * Lists all of a location's fees (taxes).
     *
     * @param string $location_id The ID of the location to list fees for. (required)
     * @return Array of \SquareConnect\Model\V1Fee[], HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function listFeesWithHttpInfo($location_id)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling listFees');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/fees";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'GET',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Fee[]'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Fee[]', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Fee[]', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * listInventory
     *
     * Provides inventory information for all of a merchant's inventory-enabled item variations.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param int $limit The maximum number of inventory entries to return in a single response. This value cannot exceed 1000. (optional)
     * @param string $batch_token A pagination cursor to retrieve the next set of results for your original query to the endpoint. (optional)
     * @return \SquareConnect\Model\V1InventoryEntry[]
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function listInventory($location_id, $limit = null, $batch_token = null)
    {
        list($response, $statusCode, $httpHeader) = $this->listInventoryWithHttpInfo ($location_id, $limit, $batch_token);
        return $response; 
    }


    /**
     * listInventoryWithHttpInfo
     *
     * Provides inventory information for all of a merchant's inventory-enabled item variations.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param int $limit The maximum number of inventory entries to return in a single response. This value cannot exceed 1000. (optional)
     * @param string $batch_token A pagination cursor to retrieve the next set of results for your original query to the endpoint. (optional)
     * @return Array of \SquareConnect\Model\V1InventoryEntry[], HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function listInventoryWithHttpInfo($location_id, $limit = null, $batch_token = null)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling listInventory');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/inventory";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        // query params
        if ($limit !== null) {
            $queryParams['limit'] = $this->apiClient->getSerializer()->toQueryValue($limit);
        }// query params
        if ($batch_token !== null) {
            $queryParams['batch_token'] = $this->apiClient->getSerializer()->toQueryValue($batch_token);
        }
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'GET',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1InventoryEntry[]'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1InventoryEntry[]', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1InventoryEntry[]', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * listItems
     *
     * Provides summary information for all of a location's items.
     *
     * @param string $location_id The ID of the location to list items for. (required)
     * @param string $batch_token A pagination cursor to retrieve the next set of results for your original query to the endpoint. (optional)
     * @return \SquareConnect\Model\V1Item[]
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function listItems($location_id, $batch_token = null)
    {
        list($response, $statusCode, $httpHeader) = $this->listItemsWithHttpInfo ($location_id, $batch_token);
        return $response; 
    }


    /**
     * listItemsWithHttpInfo
     *
     * Provides summary information for all of a location's items.
     *
     * @param string $location_id The ID of the location to list items for. (required)
     * @param string $batch_token A pagination cursor to retrieve the next set of results for your original query to the endpoint. (optional)
     * @return Array of \SquareConnect\Model\V1Item[], HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function listItemsWithHttpInfo($location_id, $batch_token = null)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling listItems');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/items";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        // query params
        if ($batch_token !== null) {
            $queryParams['batch_token'] = $this->apiClient->getSerializer()->toQueryValue($batch_token);
        }
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'GET',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Item[]'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Item[]', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Item[]', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * listModifierLists
     *
     * Lists all of a location's modifier lists.
     *
     * @param string $location_id The ID of the location to list modifier lists for. (required)
     * @return \SquareConnect\Model\V1ModifierList[]
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function listModifierLists($location_id)
    {
        list($response, $statusCode, $httpHeader) = $this->listModifierListsWithHttpInfo ($location_id);
        return $response; 
    }


    /**
     * listModifierListsWithHttpInfo
     *
     * Lists all of a location's modifier lists.
     *
     * @param string $location_id The ID of the location to list modifier lists for. (required)
     * @return Array of \SquareConnect\Model\V1ModifierList[], HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function listModifierListsWithHttpInfo($location_id)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling listModifierLists');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/modifier-lists";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'GET',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1ModifierList[]'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1ModifierList[]', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1ModifierList[]', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * listPages
     *
     * Lists all of a location's Favorites pages in Square Register.
     *
     * @param string $location_id The ID of the location to list Favorites pages for. (required)
     * @return \SquareConnect\Model\V1Page[]
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function listPages($location_id)
    {
        list($response, $statusCode, $httpHeader) = $this->listPagesWithHttpInfo ($location_id);
        return $response; 
    }


    /**
     * listPagesWithHttpInfo
     *
     * Lists all of a location's Favorites pages in Square Register.
     *
     * @param string $location_id The ID of the location to list Favorites pages for. (required)
     * @return Array of \SquareConnect\Model\V1Page[], HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function listPagesWithHttpInfo($location_id)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling listPages');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/pages";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'GET',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Page[]'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Page[]', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Page[]', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * removeFee
     *
     * Removes a fee assocation from an item, meaning the fee is no longer automatically applied to the item in Square Register.
     *
     * @param string $location_id The ID of the fee&#39;s associated location. (required)
     * @param string $item_id The ID of the item to add the fee to. (required)
     * @param string $fee_id The ID of the fee to apply. (required)
     * @return \SquareConnect\Model\V1Item
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function removeFee($location_id, $item_id, $fee_id)
    {
        list($response, $statusCode, $httpHeader) = $this->removeFeeWithHttpInfo ($location_id, $item_id, $fee_id);
        return $response; 
    }


    /**
     * removeFeeWithHttpInfo
     *
     * Removes a fee assocation from an item, meaning the fee is no longer automatically applied to the item in Square Register.
     *
     * @param string $location_id The ID of the fee&#39;s associated location. (required)
     * @param string $item_id The ID of the item to add the fee to. (required)
     * @param string $fee_id The ID of the fee to apply. (required)
     * @return Array of \SquareConnect\Model\V1Item, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function removeFeeWithHttpInfo($location_id, $item_id, $fee_id)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling removeFee');
        }
        // verify the required parameter 'item_id' is set
        if ($item_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $item_id when calling removeFee');
        }
        // verify the required parameter 'fee_id' is set
        if ($fee_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $fee_id when calling removeFee');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/items/{item_id}/fees/{fee_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }// path params
        if ($item_id !== null) {
            $resourcePath = str_replace(
                "{" . "item_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($item_id),
                $resourcePath
            );
        }// path params
        if ($fee_id !== null) {
            $resourcePath = str_replace(
                "{" . "fee_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($fee_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'DELETE',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Item'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Item', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Item', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * removeModifierList
     *
     * Removes a modifier list association from an item, meaning modifier options from the list can no longer be applied to the item.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $modifier_list_id The ID of the modifier list to remove. (required)
     * @param string $item_id The ID of the item to remove the modifier list from. (required)
     * @return \SquareConnect\Model\V1Item
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function removeModifierList($location_id, $modifier_list_id, $item_id)
    {
        list($response, $statusCode, $httpHeader) = $this->removeModifierListWithHttpInfo ($location_id, $modifier_list_id, $item_id);
        return $response; 
    }


    /**
     * removeModifierListWithHttpInfo
     *
     * Removes a modifier list association from an item, meaning modifier options from the list can no longer be applied to the item.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $modifier_list_id The ID of the modifier list to remove. (required)
     * @param string $item_id The ID of the item to remove the modifier list from. (required)
     * @return Array of \SquareConnect\Model\V1Item, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function removeModifierListWithHttpInfo($location_id, $modifier_list_id, $item_id)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling removeModifierList');
        }
        // verify the required parameter 'modifier_list_id' is set
        if ($modifier_list_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $modifier_list_id when calling removeModifierList');
        }
        // verify the required parameter 'item_id' is set
        if ($item_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $item_id when calling removeModifierList');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/items/{item_id}/modifier-lists/{modifier_list_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }// path params
        if ($modifier_list_id !== null) {
            $resourcePath = str_replace(
                "{" . "modifier_list_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($modifier_list_id),
                $resourcePath
            );
        }// path params
        if ($item_id !== null) {
            $resourcePath = str_replace(
                "{" . "item_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($item_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'DELETE',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Item'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Item', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Item', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * retrieveItem
     *
     * Provides the details for a single item, including associated modifier lists and fees.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $item_id The item&#39;s ID. (required)
     * @return \SquareConnect\Model\V1Item
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function retrieveItem($location_id, $item_id)
    {
        list($response, $statusCode, $httpHeader) = $this->retrieveItemWithHttpInfo ($location_id, $item_id);
        return $response; 
    }


    /**
     * retrieveItemWithHttpInfo
     *
     * Provides the details for a single item, including associated modifier lists and fees.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $item_id The item&#39;s ID. (required)
     * @return Array of \SquareConnect\Model\V1Item, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function retrieveItemWithHttpInfo($location_id, $item_id)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling retrieveItem');
        }
        // verify the required parameter 'item_id' is set
        if ($item_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $item_id when calling retrieveItem');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/items/{item_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }// path params
        if ($item_id !== null) {
            $resourcePath = str_replace(
                "{" . "item_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($item_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'GET',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Item'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Item', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Item', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * retrieveModifierList
     *
     * Provides the details for a single modifier list.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $modifier_list_id The modifier list&#39;s ID. (required)
     * @return \SquareConnect\Model\V1ModifierList
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function retrieveModifierList($location_id, $modifier_list_id)
    {
        list($response, $statusCode, $httpHeader) = $this->retrieveModifierListWithHttpInfo ($location_id, $modifier_list_id);
        return $response; 
    }


    /**
     * retrieveModifierListWithHttpInfo
     *
     * Provides the details for a single modifier list.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $modifier_list_id The modifier list&#39;s ID. (required)
     * @return Array of \SquareConnect\Model\V1ModifierList, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function retrieveModifierListWithHttpInfo($location_id, $modifier_list_id)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling retrieveModifierList');
        }
        // verify the required parameter 'modifier_list_id' is set
        if ($modifier_list_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $modifier_list_id when calling retrieveModifierList');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/modifier-lists/{modifier_list_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }// path params
        if ($modifier_list_id !== null) {
            $resourcePath = str_replace(
                "{" . "modifier_list_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($modifier_list_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'GET',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1ModifierList'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1ModifierList', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1ModifierList', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * updateCategory
     *
     * Modifies the details of an existing item category.
     *
     * @param string $location_id The ID of the category&#39;s associated location. (required)
     * @param string $category_id The ID of the category to edit. (required)
     * @param \SquareConnect\Model\V1Category $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return \SquareConnect\Model\V1Category
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function updateCategory($location_id, $category_id, $body)
    {
        list($response, $statusCode, $httpHeader) = $this->updateCategoryWithHttpInfo ($location_id, $category_id, $body);
        return $response; 
    }


    /**
     * updateCategoryWithHttpInfo
     *
     * Modifies the details of an existing item category.
     *
     * @param string $location_id The ID of the category&#39;s associated location. (required)
     * @param string $category_id The ID of the category to edit. (required)
     * @param \SquareConnect\Model\V1Category $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return Array of \SquareConnect\Model\V1Category, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function updateCategoryWithHttpInfo($location_id, $category_id, $body)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling updateCategory');
        }
        // verify the required parameter 'category_id' is set
        if ($category_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $category_id when calling updateCategory');
        }
        // verify the required parameter 'body' is set
        if ($body === null) {
            throw new \InvalidArgumentException('Missing the required parameter $body when calling updateCategory');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/categories/{category_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }// path params
        if ($category_id !== null) {
            $resourcePath = str_replace(
                "{" . "category_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($category_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        // body params
        $_tempBody = null;
        if (isset($body)) {
            $_tempBody = $body;
        }
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'PUT',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Category'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Category', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Category', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * updateDiscount
     *
     * Modifies the details of an existing discount.
     *
     * @param string $location_id The ID of the category&#39;s associated location. (required)
     * @param string $discount_id The ID of the discount to edit. (required)
     * @param \SquareConnect\Model\V1Discount $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return \SquareConnect\Model\V1Discount
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function updateDiscount($location_id, $discount_id, $body)
    {
        list($response, $statusCode, $httpHeader) = $this->updateDiscountWithHttpInfo ($location_id, $discount_id, $body);
        return $response; 
    }


    /**
     * updateDiscountWithHttpInfo
     *
     * Modifies the details of an existing discount.
     *
     * @param string $location_id The ID of the category&#39;s associated location. (required)
     * @param string $discount_id The ID of the discount to edit. (required)
     * @param \SquareConnect\Model\V1Discount $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return Array of \SquareConnect\Model\V1Discount, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function updateDiscountWithHttpInfo($location_id, $discount_id, $body)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling updateDiscount');
        }
        // verify the required parameter 'discount_id' is set
        if ($discount_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $discount_id when calling updateDiscount');
        }
        // verify the required parameter 'body' is set
        if ($body === null) {
            throw new \InvalidArgumentException('Missing the required parameter $body when calling updateDiscount');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/discounts/{discount_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }// path params
        if ($discount_id !== null) {
            $resourcePath = str_replace(
                "{" . "discount_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($discount_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        // body params
        $_tempBody = null;
        if (isset($body)) {
            $_tempBody = $body;
        }
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'PUT',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Discount'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Discount', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Discount', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * updateFee
     *
     * Modifies the details of an existing fee (tax).
     *
     * @param string $location_id The ID of the fee&#39;s associated location. (required)
     * @param string $fee_id The ID of the fee to edit. (required)
     * @param \SquareConnect\Model\V1Fee $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return \SquareConnect\Model\V1Fee
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function updateFee($location_id, $fee_id, $body)
    {
        list($response, $statusCode, $httpHeader) = $this->updateFeeWithHttpInfo ($location_id, $fee_id, $body);
        return $response; 
    }


    /**
     * updateFeeWithHttpInfo
     *
     * Modifies the details of an existing fee (tax).
     *
     * @param string $location_id The ID of the fee&#39;s associated location. (required)
     * @param string $fee_id The ID of the fee to edit. (required)
     * @param \SquareConnect\Model\V1Fee $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return Array of \SquareConnect\Model\V1Fee, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function updateFeeWithHttpInfo($location_id, $fee_id, $body)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling updateFee');
        }
        // verify the required parameter 'fee_id' is set
        if ($fee_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $fee_id when calling updateFee');
        }
        // verify the required parameter 'body' is set
        if ($body === null) {
            throw new \InvalidArgumentException('Missing the required parameter $body when calling updateFee');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/fees/{fee_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }// path params
        if ($fee_id !== null) {
            $resourcePath = str_replace(
                "{" . "fee_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($fee_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        // body params
        $_tempBody = null;
        if (isset($body)) {
            $_tempBody = $body;
        }
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'PUT',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Fee'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Fee', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Fee', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * updateItem
     *
     * Modifies the core details of an existing item.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $item_id The ID of the item to modify. (required)
     * @param \SquareConnect\Model\V1Item $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return \SquareConnect\Model\V1Item
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function updateItem($location_id, $item_id, $body)
    {
        list($response, $statusCode, $httpHeader) = $this->updateItemWithHttpInfo ($location_id, $item_id, $body);
        return $response; 
    }


    /**
     * updateItemWithHttpInfo
     *
     * Modifies the core details of an existing item.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $item_id The ID of the item to modify. (required)
     * @param \SquareConnect\Model\V1Item $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return Array of \SquareConnect\Model\V1Item, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function updateItemWithHttpInfo($location_id, $item_id, $body)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling updateItem');
        }
        // verify the required parameter 'item_id' is set
        if ($item_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $item_id when calling updateItem');
        }
        // verify the required parameter 'body' is set
        if ($body === null) {
            throw new \InvalidArgumentException('Missing the required parameter $body when calling updateItem');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/items/{item_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }// path params
        if ($item_id !== null) {
            $resourcePath = str_replace(
                "{" . "item_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($item_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        // body params
        $_tempBody = null;
        if (isset($body)) {
            $_tempBody = $body;
        }
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'PUT',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Item'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Item', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Item', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * updateModifierList
     *
     * Modifies the details of an existing item modifier list.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $modifier_list_id The ID of the modifier list to edit. (required)
     * @param \SquareConnect\Model\V1UpdateModifierListRequest $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return \SquareConnect\Model\V1ModifierList
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function updateModifierList($location_id, $modifier_list_id, $body)
    {
        list($response, $statusCode, $httpHeader) = $this->updateModifierListWithHttpInfo ($location_id, $modifier_list_id, $body);
        return $response; 
    }


    /**
     * updateModifierListWithHttpInfo
     *
     * Modifies the details of an existing item modifier list.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $modifier_list_id The ID of the modifier list to edit. (required)
     * @param \SquareConnect\Model\V1UpdateModifierListRequest $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return Array of \SquareConnect\Model\V1ModifierList, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function updateModifierListWithHttpInfo($location_id, $modifier_list_id, $body)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling updateModifierList');
        }
        // verify the required parameter 'modifier_list_id' is set
        if ($modifier_list_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $modifier_list_id when calling updateModifierList');
        }
        // verify the required parameter 'body' is set
        if ($body === null) {
            throw new \InvalidArgumentException('Missing the required parameter $body when calling updateModifierList');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/modifier-lists/{modifier_list_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }// path params
        if ($modifier_list_id !== null) {
            $resourcePath = str_replace(
                "{" . "modifier_list_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($modifier_list_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        // body params
        $_tempBody = null;
        if (isset($body)) {
            $_tempBody = $body;
        }
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'PUT',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1ModifierList'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1ModifierList', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1ModifierList', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * updateModifierOption
     *
     * Modifies the details of an existing item modifier option.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $modifier_list_id The ID of the modifier list to edit. (required)
     * @param string $modifier_option_id The ID of the modifier list to edit. (required)
     * @param \SquareConnect\Model\V1ModifierOption $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return \SquareConnect\Model\V1ModifierOption
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function updateModifierOption($location_id, $modifier_list_id, $modifier_option_id, $body)
    {
        list($response, $statusCode, $httpHeader) = $this->updateModifierOptionWithHttpInfo ($location_id, $modifier_list_id, $modifier_option_id, $body);
        return $response; 
    }


    /**
     * updateModifierOptionWithHttpInfo
     *
     * Modifies the details of an existing item modifier option.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $modifier_list_id The ID of the modifier list to edit. (required)
     * @param string $modifier_option_id The ID of the modifier list to edit. (required)
     * @param \SquareConnect\Model\V1ModifierOption $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return Array of \SquareConnect\Model\V1ModifierOption, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function updateModifierOptionWithHttpInfo($location_id, $modifier_list_id, $modifier_option_id, $body)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling updateModifierOption');
        }
        // verify the required parameter 'modifier_list_id' is set
        if ($modifier_list_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $modifier_list_id when calling updateModifierOption');
        }
        // verify the required parameter 'modifier_option_id' is set
        if ($modifier_option_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $modifier_option_id when calling updateModifierOption');
        }
        // verify the required parameter 'body' is set
        if ($body === null) {
            throw new \InvalidArgumentException('Missing the required parameter $body when calling updateModifierOption');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/modifier-lists/{modifier_list_id}/modifier-options/{modifier_option_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }// path params
        if ($modifier_list_id !== null) {
            $resourcePath = str_replace(
                "{" . "modifier_list_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($modifier_list_id),
                $resourcePath
            );
        }// path params
        if ($modifier_option_id !== null) {
            $resourcePath = str_replace(
                "{" . "modifier_option_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($modifier_option_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        // body params
        $_tempBody = null;
        if (isset($body)) {
            $_tempBody = $body;
        }
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'PUT',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1ModifierOption'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1ModifierOption', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1ModifierOption', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * updatePage
     *
     * Modifies the details of a Favorites page in Square Register.
     *
     * @param string $location_id The ID of the Favorites page&#39;s associated location (required)
     * @param string $page_id The ID of the page to modify. (required)
     * @param \SquareConnect\Model\V1Page $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return \SquareConnect\Model\V1Page
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function updatePage($location_id, $page_id, $body)
    {
        list($response, $statusCode, $httpHeader) = $this->updatePageWithHttpInfo ($location_id, $page_id, $body);
        return $response; 
    }


    /**
     * updatePageWithHttpInfo
     *
     * Modifies the details of a Favorites page in Square Register.
     *
     * @param string $location_id The ID of the Favorites page&#39;s associated location (required)
     * @param string $page_id The ID of the page to modify. (required)
     * @param \SquareConnect\Model\V1Page $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return Array of \SquareConnect\Model\V1Page, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function updatePageWithHttpInfo($location_id, $page_id, $body)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling updatePage');
        }
        // verify the required parameter 'page_id' is set
        if ($page_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $page_id when calling updatePage');
        }
        // verify the required parameter 'body' is set
        if ($body === null) {
            throw new \InvalidArgumentException('Missing the required parameter $body when calling updatePage');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/pages/{page_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }// path params
        if ($page_id !== null) {
            $resourcePath = str_replace(
                "{" . "page_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($page_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        // body params
        $_tempBody = null;
        if (isset($body)) {
            $_tempBody = $body;
        }
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'PUT',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Page'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Page', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Page', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * updatePageCell
     *
     * Modifies a cell of a Favorites page in Square Register.
     *
     * @param string $location_id The ID of the Favorites page&#39;s associated location. (required)
     * @param string $page_id The ID of the page the cell belongs to. (required)
     * @param \SquareConnect\Model\V1PageCell $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return \SquareConnect\Model\V1Page
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function updatePageCell($location_id, $page_id, $body)
    {
        list($response, $statusCode, $httpHeader) = $this->updatePageCellWithHttpInfo ($location_id, $page_id, $body);
        return $response; 
    }


    /**
     * updatePageCellWithHttpInfo
     *
     * Modifies a cell of a Favorites page in Square Register.
     *
     * @param string $location_id The ID of the Favorites page&#39;s associated location. (required)
     * @param string $page_id The ID of the page the cell belongs to. (required)
     * @param \SquareConnect\Model\V1PageCell $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return Array of \SquareConnect\Model\V1Page, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function updatePageCellWithHttpInfo($location_id, $page_id, $body)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling updatePageCell');
        }
        // verify the required parameter 'page_id' is set
        if ($page_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $page_id when calling updatePageCell');
        }
        // verify the required parameter 'body' is set
        if ($body === null) {
            throw new \InvalidArgumentException('Missing the required parameter $body when calling updatePageCell');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/pages/{page_id}/cells";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }// path params
        if ($page_id !== null) {
            $resourcePath = str_replace(
                "{" . "page_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($page_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        // body params
        $_tempBody = null;
        if (isset($body)) {
            $_tempBody = $body;
        }
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'PUT',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Page'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Page', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Page', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * updateVariation
     *
     * Modifies the details of an existing item variation.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $item_id The ID of the item to modify. (required)
     * @param string $variation_id The ID of the variation to modify. (required)
     * @param \SquareConnect\Model\V1Variation $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return \SquareConnect\Model\V1Variation
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function updateVariation($location_id, $item_id, $variation_id, $body)
    {
        list($response, $statusCode, $httpHeader) = $this->updateVariationWithHttpInfo ($location_id, $item_id, $variation_id, $body);
        return $response; 
    }


    /**
     * updateVariationWithHttpInfo
     *
     * Modifies the details of an existing item variation.
     *
     * @param string $location_id The ID of the item&#39;s associated location. (required)
     * @param string $item_id The ID of the item to modify. (required)
     * @param string $variation_id The ID of the variation to modify. (required)
     * @param \SquareConnect\Model\V1Variation $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return Array of \SquareConnect\Model\V1Variation, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function updateVariationWithHttpInfo($location_id, $item_id, $variation_id, $body)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling updateVariation');
        }
        // verify the required parameter 'item_id' is set
        if ($item_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $item_id when calling updateVariation');
        }
        // verify the required parameter 'variation_id' is set
        if ($variation_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $variation_id when calling updateVariation');
        }
        // verify the required parameter 'body' is set
        if ($body === null) {
            throw new \InvalidArgumentException('Missing the required parameter $body when calling updateVariation');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/items/{item_id}/variations/{variation_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }// path params
        if ($item_id !== null) {
            $resourcePath = str_replace(
                "{" . "item_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($item_id),
                $resourcePath
            );
        }// path params
        if ($variation_id !== null) {
            $resourcePath = str_replace(
                "{" . "variation_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($variation_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        // body params
        $_tempBody = null;
        if (isset($body)) {
            $_tempBody = $body;
        }
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'PUT',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Variation'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Variation', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Variation', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
}
