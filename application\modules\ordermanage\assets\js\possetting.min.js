var editpos=0;function leftArrowPressed(id){"ongoingorder"==id&&$("#fhome").trigger("click"),"kitchenorder"==id&&$("#ongoingorder").trigger("click"),"todayonlieorder"==id&&$("#kitchenorder").trigger("click"),"todayorder"==id&&$("#todayonlieorder").trigger("click")}function rightArrowPressed(id){"fhome"==id&&$("#ongoingorder").trigger("click"),"ongoingorder"==id&&$("#kitchenorder").trigger("click"),"kitchenorder"==id&&$("#todayonlieorder").trigger("click"),"todayonlieorder"==id&&$("#todayorder").trigger("click")}function topArrowPressed(id){}function downArrowPressed(id){}function getslcategory(carid){var product_name=$("#product_name").val(),csrf=$("#csrfhashresarvation").val(),category_id=carid,myurl=$("#posurl").val();$.ajax({type:"post",async:!1,url:myurl,data:{product_name:product_name,category_id:category_id,isuptade:0,csrf_test_name:csrf},success:function(data){"420"==data?$("#product_search").html("Product not found !"):$("#product_search").html(data)},error:function(){alert(lang.req_failed)}})}function printRawHtml(view){printJS({printable:view,type:"raw-html"})}function placeorder(){var ctypeid=$("#ctypeid").val(),waiter="",isdelivary="",thirdinvoiceid="",tableid="",customer_name=$("#customer_name").val(),order_date=$("#order_date").val(),grandtotal=$("#grandtotal").val(),customernote="",invoice_discount=$("#invoice_discount").val(),service_charge=$("#service_charge").val(),vat=$("#vat").val(),orggrandTotal=$("#subtotal").val(),isonline=$("#isonline").val(),isitem=$("#totalitem").val(),cookedtime=$("#cookedtime").val(),multiplletaxvalue=$("#multiplletaxvalue").val(),csrf=$("#csrfhashresarvation").val(),errormessage="";if(""==customer_name)return errormessage+="<span>Please Select Customer Name.</span>",alert("Please Select Customer Name!!!"),!1;if(""==ctypeid)return errormessage+="<span>Please Select Customer Type.</span>",alert("Please Select Customer Type!!!"),!1;if(""==isitem||0==isitem)return errormessage+="<span>Please add Some Food</span>",alert("Please add Some Food!!!"),!1;if(3==ctypeid){isdelivary=$("#delivercom").val(),thirdinvoiceid=$("#thirdinvoiceid").val();if(""==isdelivary)return errormessage+="<span>Please Select Customer Type.</span>",alert("Please Select Delivar Company!!!"),!1}else if(4==ctypeid||2==ctypeid){if(1==possetting.waiter)if(""==(waiter=$("#waiter").val()))return errormessage+="<span>Please Select Waiter.</span>",alert("Please Select Waiter!!!"),!1}else{waiter=$("#waiter").val(),tableid=$("#tableid").val();var table_member_multi=$("#table_member_multi").val(),table_member_multi_person=$("#table_member_multi_person").val(),table_member=$("#table_member").val();if(1==possetting.waiter&&""==waiter)return errormessage+="<span>Please Select Waiter.</span>",$("#waiter").select2("open"),!1;if(1==possetting.tableid){if(""==tableid)return $("#tableid").select2("open"),toastr.warning("Please Select Table","Warning"),!1;if(1==possetting.tablemaping&&(""==tableid||!$.isNumeric($("#table_person").val())))return toastr.warning("Please Select Table or number person","Warning"),!1}}if(""==errormessage){errormessage='<span style="color:#060;">Signup Completed Successfully.</span>';var dataString="customer_name="+customer_name+"&ctypeid="+ctypeid+"&waiter="+waiter+"&tableid="+tableid+"&card_type=4&isonline="+isonline+"&order_date="+(order_date=encodeURIComponent(order_date))+"&grandtotal="+grandtotal+"&customernote="+(customernote=encodeURIComponent(customernote))+"&invoice_discount="+invoice_discount+"&service_charge="+service_charge+"&vat="+vat+"&subtotal="+orggrandTotal+"&assigncard_terminal=&assignbank=&assignlastdigit=&delivercom="+isdelivary+"&thirdpartyinvoice="+thirdinvoiceid+"&cookedtime="+cookedtime+"&tablemember="+table_member+"&table_member_multi="+table_member_multi+"&table_member_multi_person="+table_member_multi_person+"&multiplletaxvalue="+multiplletaxvalue+"&csrf_test_name="+csrf;$.ajax({type:"POST",url:basicinfo.baseurl+"ordermanage/order/pos_order",data:dataString,success:function(data){$("#addfoodlist").empty(),$("#getitemp").val("0"),$("#calvat").text("0"),$("#vat").val("0"),$("#invoice_discount").val("0"),$("#caltotal").text(""),$("#grandtotal").val(""),$("#thirdinvoiceid").val(""),$("#orggrandTotal").val(""),$("#waiter").select2("data",null),$("#tableid").select2("data",null),$("#waiter").val(""),$("#table_member").val(""),$("#table_person").val(lang.person),$("#table_member_multi").val(0),$("#table_member_multi_person").val(0),"error"==data?swal({title:lang.ord_failed,text:lang.failed_msg,type:"warning",showCancelButton:!0,confirmButtonColor:"#DD6B55",confirmButtonText:lang.yes+", "+lang.cancel+"!",closeOnConfirm:!0},(function(){})):1==basicinfo.printtype?swal({title:lang.ord_succ,text:"",type:"success",showCancelButton:!1,confirmButtonColor:"#28a745",confirmButtonText:"Done",closeOnConfirm:!0},(function(){})):swal({title:lang.ord_succ,text:"Do you Want to Print Token No.???",type:"success",showCancelButton:!0,confirmButtonColor:"#28a745",confirmButtonText:lang.yes,cancelButtonText:lang.no,closeOnConfirm:!0,closeOnCancel:!0},(function(isConfirm){isConfirm?printRawHtml(data):($("#waiter").select2("data",null),$("#tableid").select2("data",null),$("#waiter").val(""),$("#tableid").val(""))}))}})}}function postokenprint(id){var csrf=$("#csrfhashresarvation").val(),url="paidtoken/"+id+"/";$.ajax({type:"POST",url:url,data:{csrf_test_name:csrf},success:function(data){printRawHtml(data)}})}function editposorder(id,view){var url="updateorder/"+id,csrf=$("#csrfhashresarvation").val();if(1==view){editpos=1;var vid=$("#onprocesslist")}else if(2==view)vid=$("#messages");else if(4==view)vid=$("#qrorder");else vid=$("#settings");$.ajax({type:"GET",url:url,data:{csrf_test_name:csrf},success:function(data){vid.html(data),$(".listcat3").on("click",(function(event){var spid=$(this).next(".dropcat").attr("id");$("#"+spid).hasClass("display-none")?($("#"+spid).removeClass("display-none"),$("#"+spid).addClass("display-block")):($("#"+spid).removeClass("display-block"),$("#"+spid).addClass("display-none"))}))}})}function quickorder(){var ctypeid=$("#ctypeid").val(),waiter="",isdelivary="",thirdinvoiceid="",tableid="",customer_name=$("#customer_name").val(),order_date=$("#order_date").val(),grandtotal=$("#grandtotal").val(),customernote="",invoice_discount=$("#invoice_discount").val(),service_charge=$("#service_charge").val(),vat=$("#vat").val(),orggrandTotal=$("#subtotal").val(),isitem=$("#totalitem").val(),cookedtime=$("#cookedtime").val(),multiplletaxvalue=$("#multiplletaxvalue").val(),csrf=$("#csrfhashresarvation").val(),errormessage="";if(""==customer_name)return errormessage+="<span>Please Select Customer Name.</span>",alert("Please Select Customer Name!!!"),!1;if(""==ctypeid)return errormessage+="<span>Please Select Customer Type.</span>",alert("Please Select Customer Type!!!"),!1;if(""==isitem||0==isitem)return errormessage+="<span>Please add Some Food</span>",alert("Please add Some Food!!!"),!1;if(3==ctypeid){isdelivary=$("#delivercom").val(),thirdinvoiceid=$("#thirdinvoiceid").val();if(""==isdelivary)return errormessage+="<span>Please Select Customer Type.</span>",alert("Please Select Delivar Company!!!"),!1}else if(4==ctypeid||2==ctypeid){waiter=$("#waiter").val();if(1==quickordersetting.waiter&&""==waiter)return errormessage+="<span>Please Select Waiter.</span>",$("#waiter").select2("open"),!1}else{waiter=$("#waiter").val(),tableid=$("#tableid").val();var table_member_multi=$("#table_member_multi").val(),table_member_multi_person=$("#table_member_multi_person").val(),table_member=$("#table_member").val();if(1==quickordersetting.waiter&&""==waiter)return errormessage+="<span>Please Select Waiter.</span>",$("#waiter").select2("open"),!1;if(1==quickordersetting.tableid){if(""==tableid)return $("#tableid").select2("open"),toastr.warning("Please Select Table","Warning"),!1;if(1==quickordersetting.tablemaping&&(""==tableid||!$.isNumeric($("#table_person").val())))return toastr.warning("Please Select Table or number person","Warning"),!1}}if(""==errormessage){errormessage='<span style="color:#060;">Signup Completed Successfully.</span>';var dataString="customer_name="+customer_name+"&ctypeid="+ctypeid+"&waiter="+waiter+"&tableid="+tableid+"&card_type=4&isonline=0&order_date="+(order_date=encodeURIComponent(order_date))+"&grandtotal="+grandtotal+"&customernote="+(customernote=encodeURIComponent(customernote))+"&invoice_discount="+invoice_discount+"&service_charge="+service_charge+"&vat="+vat+"&subtotal="+orggrandTotal+"&assigncard_terminal=&assignbank=&assignlastdigit=&delivercom="+isdelivary+"&thirdpartyinvoice="+thirdinvoiceid+"&cookedtime="+cookedtime+"&tablemember="+table_member+"&table_member_multi="+table_member_multi+"&table_member_multi_person="+table_member_multi_person+"&multiplletaxvalue="+multiplletaxvalue+"&csrf_test_name="+csrf;$.ajax({type:"POST",url:basicinfo.baseurl+"ordermanage/order/pos_order/1",data:dataString,success:function(data){$("#addfoodlist").empty(),$("#getitemp").val("0"),$("#calvat").text("0"),$("#vat").val("0"),$("#invoice_discount").val("0"),$("#caltotal").text(""),$("#grandtotal").val(""),$("#thirdinvoiceid").val(""),$("#orggrandTotal").val(""),$("#waiter").select2("data",null),$("#tableid").select2("data",null),$("#waiter").val(""),$("#table_member").val(""),$("#table_person").val(lang.person),$("#table_member_multi").val(0),$("#table_member_multi_person").val(0),"error"==data?swal({title:lang.ord_failed,text:lang.failed_msg,type:"warning",showCancelButton:!0,confirmButtonColor:"#DD6B55",confirmButtonText:lang.yes+", "+lang.cancel+"!",closeOnConfirm:!0},(function(){})):swal({title:lang.ord_places,text:lang.do_print_in,type:"success",showCancelButton:!0,confirmButtonColor:"#28a745",confirmButtonText:lang.yes,cancelButtonText:lang.no,closeOnConfirm:!0,closeOnCancel:!0},(function(isConfirm){isConfirm?createMargeorder(data,1):($("#waiter").select2("data",null),$("#tableid").select2("data",null),$("#waiter").val(""),$("#tableid").val(""))}))}})}}function printJobComplete(){$("#kotenpr").empty()}function printRawHtmlupdate(view,id){printJS({printable:view,type:"raw-html",onPrintDialogClose:function(){$.ajax({type:"GET",url:"tokenupdate/"+id,data:{csrf_test_name:csrftokeng},success:function(data){console.log("done")}})}})}function postupdateorder_ajax(){var form=$("#insert_purchase"),url=form.attr("action"),data=form.serialize();$.ajax({url:url,type:"POST",data:data,dataType:"json",beforeSend:function(xhr){$("span.error").html("")},success:function(result){swal({title:result.msg,text:result.tokenmsg,type:"success",showCancelButton:!0,confirmButtonColor:"#28a745",confirmButtonText:lang.yes,cancelButtonText:lang.no,closeOnConfirm:!0,closeOnCancel:!0},(function(isConfirm){isConfirm?$.ajax({type:"GET",url:"postokengenerateupdate/"+result.orderid+"/1",success:function(data){printRawHtml(data),$(".maindashboard").removeClass("disabled"),$("#fhome").removeClass("disabled"),$("#kitchenorder").removeClass("disabled"),$("#todayqrorder").removeClass("disabled"),$("#todayonlieorder").removeClass("disabled"),$("#todayorder").removeClass("disabled"),$("#ongoingorder").removeClass("disabled")}}):$.ajax({type:"GET",url:"tokenupdate/"+result.orderid,success:function(data){$(".maindashboard").removeClass("disabled"),$("#fhome").removeClass("disabled"),$("#kitchenorder").removeClass("disabled"),$("#todayqrorder").removeClass("disabled"),$("#todayonlieorder").removeClass("disabled"),$("#todayorder").removeClass("disabled"),$("#ongoingorder").removeClass("disabled")}})})),setTimeout((function(){toastr.options={closeButton:!0,progressBar:!0,showMethod:"slideDown",timeOut:4e3},toastr.success(result.msg,"Success"),prevsltab.trigger("click")}),300)},error:function(a){}})}function payorderbill(status,orderid,totalamount){$("#paidbill").attr("onclick","orderconfirmorcancel("+status+","+orderid+")"),$("#maintotalamount").val(totalamount),$("#totalamount").val(totalamount),$("#paidamount").attr("max",totalamount),$("#payprint").modal("show")}function onlinepay(){$("#onlineordersubmit").submit()}function orderconfirmorcancel(status,orderid){if(mystatus=status,9==status||10==status){status=4;var pval=$("#paidamount").val();if(pval<1||""==pval)return alert("Please Insert Paid Amount!!!"),!1}var carttype="",cterminal="",mybank="",mydigit="",paid="";if(4==status){carttype=$("#card_typesl").val(),cterminal=$("#card_terminal").val(),mybank=$("#bank").val(),mydigit=$("#last4digit").val(),paid=$("#paidamount").val();if(""==carttype)return alert("Please Select Payment Method!!!"),!1;if(1==carttype&&""==cterminal)return alert(lang.crd_terminal_message),!1}var csrf=$("#csrfhashresarvation").val(),dataString="status="+status+"&orderid="+orderid+"&paytype="+carttype+"&cterminal="+cterminal+"&mybank="+mybank+"&mydigit="+mydigit+"&paid="+paid+"&csrf_test_name="+csrf;$.ajax({type:"POST",url:basicinfo.baseurl+"ordermanage/order/changestatus",data:dataString,success:function(data){$("#onprocesslist").html(data),"9"==mystatus?window.location.href=basicinfo.baseurl+"ordermanage/order/orderinvoice/"+orderid:"10"==mystatus?($("#payprint").modal("hide"),prevsltab.trigger("click")):4==mystatus&&swal({title:lang.ord_complte,text:lang.ord_com_sucs,type:"success",showCancelButton:!1,confirmButtonColor:"#28a745",confirmButtonText:lang.yes,closeOnConfirm:!0},(function(){prevsltab.trigger("click"),$("#paidamount").val(""),$("#payprint").modal("hide")}))}})}function paysound(){var filename=basicinfo.baseurl+basicinfo.nofitysound;new Audio(filename).play()}function load_unseen_notification(view=""){var csrf=$("#csrfhashresarvation").val(),myAudio=document.getElementById("myAudio"),soundenable=possetting.soundenable;$.ajax({url:"notification",method:"POST",data:{csrf_test_name:csrf,view:view},dataType:"json",success:function(data){data.unseen_notification>0?($(".count").html(data.unseen_notification),1==soundenable&&myAudio.play()):(1==soundenable&&myAudio.pause(),$(".count").html(data.unseen_notification))}})}$(document).ready((function(){"use strict";null!=basicinfo.segment4&&swal({title:lang.ord_uodate_success,text:lang.do_print_token,type:"success",showCancelButton:!0,confirmButtonColor:"#28a745",confirmButtonText:lang.yes,cancelButtonText:lang.no,closeOnConfirm:!1,closeOnCancel:!0},(function(isConfirm){window.location.href=isConfirm?basicinfo.baseurl+"ordermanage/order/postokengenerate/"+basicinfo.segment4+"/1":basicinfo.baseurl+"ordermanage/order/pos_invoice"}))})),document.onkeydown=function(evt){var id=$("li.active a").attr("id");switch((evt=evt||window.event).keyCode){case 37:leftArrowPressed(id);break;case 38:topArrowPressed(id);break;case 39:rightArrowPressed(id);break;case 40:downArrowPressed(id)}},$(window).on("load",(function(){"use strict";$(".sidebar-mini").addClass("sidebar-collapse");var myurl=basicinfo.baseurl+"ordermanage/order/cashregister";$("#csrfhashresarvation").val();$.ajax({type:"GET",async:!1,url:myurl,success:function(data){if(1==data)return!1;$("#openclosecash").html(data),$("#openregister").modal({backdrop:"static",keyboard:!1})}});var filename=basicinfo.baseurl+basicinfo.nofitysound;new Audio(filename)})),$(document).ready((function(){"use strict";$("select.form-control:not(.dont-select-me)").select2({placeholder:lang.sl_option,allowClear:!0}),$("#validate").validate(),$("#add_category").validate(),$("#customer_name").validate(),$(".product-list").slimScroll({size:"3px",height:"345px",allowPageScroll:!0,railVisible:!0}),$(".product-grid").slimScroll({size:"3px",height:"calc(100vh - 180px)",allowPageScroll:!0,railVisible:!0}),$(".cat-grid").slimScroll({size:"3px",height:"calc(100vh - 180px)",allowPageScroll:!0,railVisible:!0});new Audio(basicinfo.baseurl+"assets/beep-08b.mp3")})),$("body").on("click","#search_button",(function(){var product_name=$("#product_name").val(),category_id=$("#category_id").val(),csrf=$("#csrfhashresarvation").val(),myurl=$("#posurl").val();$.ajax({type:"post",async:!1,url:myurl,data:{product_name:product_name,category_id:category_id,csrf_test_name:csrf},success:function(data){"420"==data?$("#product_search").html("Product not found !"):$("#product_search").html(data)},error:function(){alert(lang.req_failed)}})})),$("body").on("click",".select_product",(function(e){e.preventDefault();var panel=$(this),pid=panel.find(".panel-body input[name=select_product_id]").val(),sizeid=panel.find(".panel-body input[name=select_product_size]").val(),totalvarient=panel.find(".panel-body input[name=select_totalvarient]").val(),customqty=panel.find(".panel-body input[name=select_iscustomeqty]").val(),isgroup=panel.find(".panel-body input[name=select_product_isgroup]").val(),catid=panel.find(".panel-body input[name=select_product_cat]").val(),itemname=panel.find(".panel-body input[name=select_product_name]").val(),varientname=panel.find(".panel-body input[name=select_varient_name]").val(),price=panel.find(".panel-body input[name=select_product_price]").val(),hasaddons=panel.find(".panel-body input[name=select_addons]").val(),csrf=$("#csrfhashresarvation").val();if(0==hasaddons&&1==totalvarient&&0==customqty){if(1==$("#production_setting").val()){if(1==$("#productionsetting-"+pid+"-"+sizeid).length)var checkqty=parseInt($("#productionsetting-"+pid+"-"+sizeid).text())+1;else checkqty=1;if(0==checkproduction(pid,sizeid,checkqty))return!1}var mysound=basicinfo.baseurl+"assets/";new Audio(mysound+"beep-08b.mp3").play();var dataString="pid="+pid+"&itemname="+itemname+"&varientname="+varientname+"&qty=1&price="+price+"&catid="+catid+"&sizeid="+sizeid+"&isgroup="+isgroup+"&csrf_test_name="+csrf,myurl=$("#carturl").val();$.ajax({type:"POST",url:myurl,data:dataString,success:function(data){$("#addfoodlist").html(data);$("#grtotal").val();var totalitem=$("#totalitem").val();$("#item-number").text(totalitem),$("#getitemp").val(totalitem);var tax=$("#tvat").val()||0;$("#vat").val(tax);var discount=$("#tdiscount").val()||0,tgtotal=$("#tgtotal").val()||0;$("#calvat").text(tax),$("#invoice_discount").val(discount);var sc=$("#sc").val();$("#service_charge").val(sc),1==basicinfo.isvatinclusive?$("#caltotal").text(tgtotal-tax):$("#caltotal").text(tgtotal),$("#grandtotal").val(tgtotal),$("#orggrandTotal").val(tgtotal),$("#orginattotal").val(tgtotal)}})}else{var geturl=$("#addonexsurl").val();myurl=geturl+"/"+pid;console.log({myurl:myurl});dataString="pid="+pid+"&sid="+sizeid+"&csrf_test_name="+csrf;$.ajax({type:"POST",url:geturl,data:dataString,success:function(data){$(".addonsinfo").html(data),$("#edit").modal("show");var totalitem=$(".totalitem").val()||0,tax=$("#tvat").val()||0,discount=$("#tdiscount").val()||0,tgtotal=$("#tgtotal").val()||0;$("#vat").val(tax),$("#calvat").text(tax),$("#getitemp").val(totalitem),$("#invoice_discount").val(discount),1==basicinfo.isvatinclusive?$("#caltotal").text(tgtotal-tax):$("#caltotal").text(tgtotal),$("#grandtotal").val(tgtotal),$("#orggrandTotal").val(tgtotal),$("#orginattotal").val(tgtotal)}})}})),$(document).ready((function(){"use strict";$("#nonthirdparty").show(),$("#thirdparty").hide(),$("#delivercom").prop("disabled",!0),$("#waiter").prop("disabled",!1),$("#tableid").prop("disabled",!1),$("#cookingtime").prop("disabled",!1),$("#cardarea").hide(),$("#paidamount").on("keyup",(function(){var maintotalamount=$("#maintotalamount").val(),paidamount=$("#paidamount").val(),changes=(parseFloat(paidamount)-parseFloat(maintotalamount)).toFixed(2);$("#change").val(changes)})),$(".payment_button").click((function(){$(".payment_method").toggle(),$("select.form-control:not(.dont-select-me)").select2({placeholder:lang.sl_option,allowClear:!0})})),$("#card_typesl").on("change",(function(){var cardtype=$("#card_typesl").val();$("#card_type").val(cardtype),4==cardtype?($("#isonline").val(0),$("#cardarea").hide(),$("#assigncard_terminal").val(""),$("#assignbank").val(""),$("#assignlastdigit").val("")):1==cardtype?($("#isonline").val(0),$("#cardarea").show()):($("#isonline").val(1),$("#cardarea").hide(),$("#assigncard_terminal").val(""),$("#assignbank").val(""),$("#assignlastdigit").val(""))})),$("#ctypeid").on("change",(function(){var customertype=$("#ctypeid").val();3==customertype?($("#delivercom").prop("disabled",!1),$("#waiter").prop("disabled",!0),$("#tableid").prop("disabled",!0),$("#cookingtime").prop("disabled",!0),$("#nonthirdparty").hide(),$("#thirdparty").show()):4==customertype?($("#nonthirdparty").show(),$("#thirdparty").hide(),$("#tblsec").hide(),$("#tblsecp").hide(),$("#delivercom").prop("disabled",!0),$("#waiter").prop("disabled",!1),$("#tableid").prop("disabled",!0),$("#cookingtime").prop("disabled",!0)):2==customertype?($("#nonthirdparty").show(),$("#tblsecp").hide(),$("#tblsec").hide(),$("#thirdparty").hide(),$("#waiter").prop("disabled",!1),$("#tableid").prop("disabled",!1),$("#cookingtime").prop("disabled",!1),$("#delivercom").prop("disabled",!0)):($("#nonthirdparty").show(),$("#tblsecp").show(),$("#tblsec").show(),$("#thirdparty").hide(),$("#waiter").prop("disabled",!1),$("#tableid").prop("disabled",!1),$("#cookingtime").prop("disabled",!1),$("#delivercom").prop("disabled",!0))})),$('[data-toggle="popover"]').popover({container:"body"}),Mousetrap.bind("shift+p",(function(){placeorder()})),Mousetrap.bind("shift+q",(function(){quickorder()})),Mousetrap.bind("shift+c",(function(){$("#customer_name").select2("open")})),Mousetrap.bind("shift+y",(function(){$("#ctypeid").select2("open")})),Mousetrap.bind("shift+d",(function(){return $("#invoice_discount").focus(),!1})),Mousetrap.bind("shift+r",(function(){return $("#service_charge").focus(),!1})),Mousetrap.bind("shift+g",(function(){$(".ongord").trigger("click")})),Mousetrap.bind("shift+t",(function(){$(".torder").trigger("click")})),Mousetrap.bind("shift+o",(function(){$(".comorder").trigger("click")})),Mousetrap.bind("shift+n",(function(){$(".home").trigger("click")})),Mousetrap.bind("shift+s",(function(){$("#product_name").select2("open")})),Mousetrap.bind("alt+q",(function(){return $("#itemqty_1").focus(),!1})),Mousetrap.bind("shift+a",(function(){$("#add_to_cart").trigger("click")})),Mousetrap.bind("shift+e",(function(e){$("[id*=table-]").focus()})),Mousetrap.bind("shift+x",(function(e){return $("input[aria-controls=onprocessing]").focus(),!1})),Mousetrap.bind("shift+v",(function(e){return $("input[aria-controls=Onlineorder]").focus(),!1})),Mousetrap.bind("shift+m",(function(e){$("[id*=table-today-]").focus()})),Mousetrap.bind("alt+k",(function(){return $("#cookedtime").focus(),!1})),Mousetrap.bind("shift+w",(function(){return $("#waiter").select2("open"),!1})),Mousetrap.bind("shift+b",(function(){return $("#tableid").select2("open"),!1})),Mousetrap.bind("alt+t",(function(){$("#ongoingtable_name").select2("open")})),Mousetrap.bind("alt+s",(function(){$("#update_product_name").select2("open")})),Mousetrap.bind("alt+c",(function(){$("#customer_name_update").select2("open")})),Mousetrap.bind("alt+y",(function(){$("#ctypeid_update").select2("open")})),Mousetrap.bind("alt+w",(function(){return $("#waiter_update").select2("open"),!1})),Mousetrap.bind("alt+b",(function(){return $("#tableid_update").select2("open"),!1})),Mousetrap.bind("alt+d",(function(){return $("#invoice_discount_update").focus(),!1})),Mousetrap.bind("alt+r",(function(){return $("#service_charge_update").focus(),!1})),Mousetrap.bind("alt+u",(function(){$("#update_order_confirm").trigger("click")})),Mousetrap.bind("alt+m",(function(){$(".card_typesl").select2("open")})),Mousetrap.bind("alt+a",(function(){return $(".number").focus(),!1})),Mousetrap.bind("alt+p",(function(){$("#pay_bill").trigger("click")})),Mousetrap.bind("alt+x",(function(){$(".close").trigger("click")})),$(".search-field").select2({placeholder:lang.sl_product,minimumInputLength:1,ajax:{url:"getitemlistdroup",dataType:"json",delay:250,processResults:function(data){return{results:$.map(data,(function(item){return{text:item.text+"-"+item.variantName,id:item.id+"-"+item.variantid}}))}},cache:!0}}),$(document).on("click","#ongoingorder",(function(){var csrf=$("#csrfhashresarvation").val();$.ajax({type:"GET",url:"getongoingorder",data:{csrf_test_name:csrf},success:function(data){$("#onprocesslist").html(data)}})})),$(document).on("click","#kitchenorder",(function(){var csrf=$("#csrfhashresarvation").val();$.ajax({type:"GET",url:"kitchenstatus",data:{csrf_test_name:csrf},success:function(data){$("#kitchen").html(data)}})})),$(document).on("click","#todayorder",(function(){var csrf=$("#csrfhashresarvation").val();$.ajax({type:"GET",url:"showtodayorder",data:{csrf_test_name:csrf},success:function(data){$("#messages").html(data)}})})),$(document).on("click","#todayonlieorder",(function(){var csrf=$("#csrfhashresarvation").val();$.ajax({type:"GET",url:"showonlineorder",data:{csrf_test_name:csrf},success:function(data){$("#settings").html(data)}})})),$(document).on("click","#todayqrorder",(function(){var csrf=$("#csrfhashresarvation").val();$.ajax({type:"GET",url:"showqrorder",data:{csrf_test_name:csrf},success:function(data){$("#qrorder").html(data)}})}))})),$(document).on("change","#ongoingtable_name",(function(){var id=$(this).children("option:selected").val(),url="getongoingorder/"+id,csrf=$("#csrfhashresarvation").val();$.ajax({type:"GET",url:url,data:{csrf_test_name:csrf},success:function(data){$("#onprocesslist").html(data)}}),$("#table-"+id).focus()})),$(document).on("change","#ongoingtable_sr",(function(){var id=$(this).children("option:selected").val(),url="getongoingorder/"+id+"/table",csrf=$("#csrfhashresarvation").val();$.ajax({type:"GET",url:url,data:{csrf_test_name:csrf},success:function(data){$("#onprocesslist").html(data)}}),$("#table-"+id).focus()})),$(document).on("change","#product_name",(function(){var idvid=$(this).children("option:selected").val().split("-"),id=idvid[0],vid=idvid[1],url="srcposaddcart/"+id,csrf=$("#csrfhashresarvation").val();if(1==$("#production_setting").val()){if(0==checkproduction(id,vid,1))return $("#product_name").html(""),!1}$.ajax({type:"GET",url:url,data:{csrf_test_name:csrf},success:function(data){var myurl="adonsproductadd/"+id;$.ajax({type:"GET",url:myurl,data:{csrf_test_name:csrf},success:function(data){$(".addonsinfo").html(data),$("#edit").modal("show");var totalitem=$("#totalitem").val(),tax=$("#tvat").val(),discount=$("#tdiscount").val(),tgtotal=$("#tgtotal").val();$("#vat").val(tax),$("#calvat").text(tax);var sc=$("#sc").val();$("#service_charge").val(sc),$("#getitemp").val(totalitem),$("#invoice_discount").val(discount),1==basicinfo.isvatinclusive?$("#caltotal").text(tgtotal-tax):$("#caltotal").text(tgtotal),$("#grandtotal").val(tgtotal),$("#orggrandTotal").val(tgtotal),$("#orginattotal").val(tgtotal),$("#product_name").html("")}})}})})),$(document).on("keypress","#itemqty_1",(function(e){13==e.which&&$(".asingle").trigger("click")})),$("#edit").on("shown.bs.modal",(function(){$("#varientinfo").focus()}));var intervalc=0;function load_unseen_notificationqr(view=""){var csrf=$("#csrfhashresarvation").val(),myAudio=document.getElementById("myAudio"),soundenable=possetting.soundenable;$.ajax({url:basicinfo.baseurl+"ordermanage/order/notificationqr",method:"POST",data:{csrf_test_name:csrf,view:view},dataType:"json",success:function(data){data.unseen_notificationqr>0?($(".count2").html(data.unseen_notificationqr),1==soundenable&&myAudio.play()):(1==soundenable&&myAudio.pause(),$(".count2").html(data.unseen_notification))}})}function detailspop(orderid){var csrf=$("#csrfhashresarvation").val(),myurl=basicinfo.baseurl+"ordermanage/order/orderdetailspop/"+orderid,dataString="orderid="+orderid+"&csrf_test_name="+csrf;$.ajax({type:"POST",url:myurl,data:dataString,success:function(data){$(".orddetailspop").html(data),$("#orderdetailsp").modal("show")}})}function pospageprint(orderid){var csrf=$("#csrfhashresarvation").val(),datavalue="customer_name="+customer_name+"&csrf_test_name="+csrf;$.ajax({type:"POST",url:basicinfo.baseurl+"ordermanage/order/posprintview/"+orderid,data:datavalue,success:function(printdata){if(1!=basicinfo.printtype){$("#kotenpr").html(printdata);printJS({printable:"kotenpr",onPrintDialogClose:printJobComplete,type:"html",font_size:"25px",style:"@page { margin:0px;font-size:18px; }",scanStyles:!1})}}})}function printPosinvoice(id){var csrf=$("#csrfhashresarvation").val(),url="posorderinvoice/"+id;$.ajax({type:"GET",url:url,data:{csrf_test_name:csrf},success:function(data){printRawHtml(data)}})}function pos_order_invoice(id){var csrf=$("#csrfhashresarvation").val(),url="pos_order_invoice/"+id;$.ajax({type:"GET",url:url,data:{csrf_test_name:csrf},success:function(data){$("#messages").html(data)}})}function orderdetails_post(id){var csrf=$("#csrfhashresarvation").val(),url="orderdetails_post/"+id;$.ajax({type:"GET",url:url,data:{csrf_test_name:csrf},success:function(data){$("#messages").html(data)}})}function orderdetails_onlinepost(id){var url="orderdetails_post/"+id,csrf=$("#csrfhashresarvation").val();$.ajax({type:"GET",url:url,data:{csrf_test_name:csrf},success:function(data){$("#settings").html(data)}})}function createMargeorder(orderid,value=null){$("#csrfhashresarvation").val();var url="showpaymentmodal/"+orderid;callback=function(a){$("#modal-ajaxview").html(a),$("#get-order-flag").val("2")},null==value?getAjaxModal(url):getAjaxModal(url,callback)}function printmergeinvoice(id){id=atob(id);var csrf=$("#csrfhashresarvation").val(),url=basicinfo.baseurl+"ordermanage/order/checkprint/"+id;$.ajax({type:"GET",url:url,data:{csrf_test_name:csrf},success:function(data){printRawHtml(data)}})}function showhidecard(element){var cardtype=$(element).val();$(element).closest("div.row").next().find("div.cardarea");4==cardtype?($("#isonline").val(0),$(element).closest("div.row").next().find("div.cardarea").addClass("display-none"),$("#assigncard_terminal").val(""),$("#assignbank").val(""),$("#assignlastdigit").val("")):1==cardtype?($("#isonline").val(0),$(element).closest("div.row").next().find("div.cardarea").removeClass("display-none")):($("#isonline").val(1),$(element).closest("div.row").next().find("div.cardarea").addClass("display-none"),$("#assigncard_terminal").val(""),$("#assignbank").val(""),$("#assignlastdigit").val(""))}function submitmultiplepay(){var thisForm=$("#paymodal-multiple-form"),inputval=parseFloat(0),maintotalamount=$("#due-amount").text();if($(".number").each((function(){var inputdata=parseFloat($(this).val());inputval+=inputdata})),inputval<parseFloat(maintotalamount))return setTimeout((function(){toastr.options={closeButton:!0,progressBar:!0,showMethod:"slideDown",timeOut:4e3},toastr.error("Pay full amount ","Error")}),100),!1;var formdata=new FormData(thisForm[0]);$.ajax({type:"POST",url:basicinfo.baseurl+"ordermanage/order/paymultiple",data:formdata,processData:!1,contentType:!1,success:function(data){1==$("#get-order-flag").val()?setTimeout((function(){toastr.options={closeButton:!0,progressBar:!0,showMethod:"slideDown",timeOut:4e3},toastr.success("payment taken successfully","Success"),$("#payprint_marge").modal("hide"),$("#modal-ajaxview").empty(),prevsltab.trigger("click")}),100):($("#payprint_marge").modal("hide"),$("#modal-ajaxview").empty(),1!=basicinfo.printtype&&printRawHtml(data),prevsltab.trigger("click"))}})}function changedueamount(){var inputval=parseFloat(0),maintotalamount=$("#due-amount").text();$(".number").each((function(){var inputdata=parseFloat($(this).val());inputval+=inputdata})),restamount=parseFloat(maintotalamount)-parseFloat(inputval);var changes=restamount.toFixed(3);changes<=0?($("#change-amount").text(Math.abs(changes)),$("#pay-amount").text(0)):($("#change-amount").text(0),$("#pay-amount").text(changes))}function mergeorderlist(){var dataString="orderid="+$('input[name="margeorder"]:checked').map((function(){return $(this).val()})).get().join(",")+"&csrf_test_name="+$("#csrfhashresarvation").val();$.ajax({url:basicinfo.baseurl+"ordermanage/order/mergemodal",method:"POST",data:dataString,success:function(data){$("#payprint_marge").modal("show"),$("#modal-ajaxview").html(data),$("#get-order-flag").val("2")}})}function duemergeorder(orderid,mergeid){var dataString="orderid="+orderid+"&mergeid="+mergeid+"&allorderid="+$("#allmerge_"+mergeid).val()+"&csrf_test_name="+$("#csrfhashresarvation").val();$.ajax({url:basicinfo.baseurl+"ordermanage/order/duemergemodal",method:"POST",data:dataString,success:function(data){$("#payprint_marge").modal("show"),$("#modal-ajaxview").html(data),$("#get-order-flag").val("2")}})}function margeorderconfirmorcancel(){var thisForm=$("#paymodal-multiple-form"),formdata=new FormData(thisForm[0]);$.ajax({type:"POST",url:basicinfo.baseurl+"ordermanage/order/changeMargeorder",data:formdata,processData:!1,contentType:!1,success:function(data){$("#payprint_marge").modal("hide"),1!=basicinfo.printtype&&printRawHtml(data),prevsltab.trigger("click")}})}function duemargebill(){var thisForm=$("#paymodal-multiple-form"),formdata=new FormData(thisForm[0]);$.ajax({type:"POST",url:basicinfo.baseurl+"ordermanage/order/changeMargedue",data:formdata,processData:!1,contentType:!1,success:function(data){$("#payprint_marge").modal("hide"),1!=basicinfo.printtype&&printRawHtml(data),prevsltab.trigger("click")}})}function margeorder(){var totaldue=0;$(".marg-check").each((function(){if($(this).is(":checked")){var id=$(this).val();totaldue=parseFloat($("#due-"+id).text())+totaldue}$("#due-amount").text(totaldue),$("#totalamount_marge").text(totaldue),$("#paidamount_marge").val(totaldue)}))}function checktable(id=null){if(null!=id){var valu=$("#person-"+id).val();$("#table_member").val(valu);var url="checktablecap/"+id}else{idd=$("#tableid").val();url="checktablecap/"+idd}var order_person=$("#table_member").val();if(""!=order_person){var csrf=$("#csrfhashresarvation").val();$.ajax({type:"GET",url:url,data:{csrf_test_name:csrf},success:function(data){if(!(order_person>data))return null!=id&&($("#tableid").val(id).trigger("change"),$("#table_member_multi").val(0),$("#table_member_multi_person").val(0),$("#table_person").val(order_person),$("#tablemodal").modal("hide")),!1;setTimeout((function(){toastr.options={closeButton:!0,progressBar:!0,showMethod:"slideDown",timeOut:4e3},toastr.warning("table capacity overflow","Warning")}),300)}})}else setTimeout((function(){$("#table_member").focus(),toastr.options={closeButton:!0,progressBar:!0,showMethod:"slideDown",timeOut:4e3},toastr.error("Please type Number of person","Error")}),300)}function showTablemodal(){getAjaxModal("showtablemodal",!1,"#table-ajaxview","#tablemodal")}function showfloor(floorid){var csrf=$("#csrfhashresarvation").val(),dataString="floorid="+floorid+"&csrf_test_name="+csrf;$.ajax({type:"POST",url:"fllorwisetable",data:dataString,success:function(data){$("#floor"+floorid).html(data)}})}function deleterow_table(id,tableid=null){var dataString="csrf_test_name="+$("#csrfhashresarvation").val();if(null==tableid){var url="delete_table_details/"+id;$.ajax({type:"GET",url:url,data:dataString,success:function(data){1==data&&$("#table-tr-"+id).remove()}})}else{url="delete_table_details_all/"+tableid;$.ajax({type:"GET",url:url,data:dataString,success:function(data){1==data&&$("#table-tbody-"+tableid).empty()}})}}function multi_table(){var arr=$('input[name="add_table[]"]').map((function(){return this.value})).get();$("#table_member_multi").val(arr);var value=[],order_person_t=0;for(i=0;i<arr.length;i++){value[i]=$("#person-"+arr[i]).val();let parsedValue=parseInt(value[i],10);isNaN(parsedValue)||(order_person_t+=parsedValue)}$("#table_member").val($("#person-"+arr[0]).val()),$("#table_person").val(order_person_t),$("#table_member_multi_person").val(value),$("#tablemodal").modal("hide"),$("#tableid").val(arr[0]).trigger("change")}function showsplitmodal(orderid,option=null){var url="showsplitorder/"+orderid;callback=function(a){$("#modal-ajaxview").html(a)},null==option?getAjaxModal(url,!1,"#table-ajaxview","#tablemodal"):getAjaxModal(url,callback)}function showsuborder(element){var val=$(element).val(),url=$(element).attr("data-url")+val,orderid=$(element).attr("data-value");$("#csrfhashresarvation").val();getAjaxView(url,"show-sub-order",!1,"orderid="+orderid,"post")}function getAjaxView(url,ajaxclass,callback=!1,data="",method="get"){var fulldata=data+"&csrf_test_name="+$("#csrfhashresarvation").val();return $.ajax({url:url,type:method,data:fulldata,beforeSend:function(xhr){},success:function(result){callback?callback(result):$("#"+ajaxclass).html(result)},error:function(a){}}),!1}function selectelement(element){$(".split-item").each((function(index){$(this).removeClass("split-selected")})),$(element).toggleClass("split-selected")}function addintosuborder(menuid,orderid,element){var presentvalue=$(element).find("td:eq(1)").text(),isselected=$(".split-selected").length;if(0!=presentvalue&&1==isselected){var suborderid=$(".split-selected").attr("data-value"),service_chrg=$("#service-"+suborderid).val(),datavalue=($("#csrfhashresarvation").val(),"orderid="+orderid+"&menuid="+menuid+"&suborderid="+suborderid+"&qty=1&service_chrg="+service_chrg);getAjaxView($(element).attr("data-url"),"table-tbody-"+orderid+"-"+suborderid,!1,datavalue,"post");var nowvalue=parseInt(presentvalue)-1;$(element).find("td:eq(1)").text(nowvalue)}}function paySuborder(element){var id=$(element).attr("id").replace("subpay-",""),url=$(element).attr("data-url"),vat=$("#vat-"+id).val();if(!$("#vat-"+id).length)return!1;var service=$("#service-"+id).val(),total=$("#total-sub-"+id).val(),customerid=$("#customer-"+id).val();$("#tablemodal").modal("hide"),$("#modal-ajaxview").empty(),getAjaxModal(url,!1,"#modal-ajaxview-split","#payprint_split","sub_id="+id+"&vat="+vat+"&service="+service+"&total="+total+"&customerid="+customerid,"post")}function submitmultiplepaysub(subid){var thisForm=$("#paymodal-multiple-form"),inputval=parseFloat(0),maintotalamount=$("#due-amount").text();if($(".number").each((function(){var inputdata=parseFloat($(this).val());inputval+=inputdata})),inputval<parseFloat(maintotalamount))return setTimeout((function(){toastr.options={closeButton:!0,progressBar:!0,showMethod:"slideDown",timeOut:4e3},toastr.error("Pay full amount ","Error")}),100),!1;var formdata=new FormData(thisForm[0]);$.ajax({type:"POST",url:basicinfo.baseurl+"ordermanage/order/paymultiplsub",data:formdata,processData:!1,contentType:!1,success:function(data){$("#get-order-flag").val();setTimeout((function(){toastr.options={closeButton:!0,progressBar:!0,showMethod:"slideDown",timeOut:4e3},toastr.success("payment taken successfully","Success"),$("#payprint_split").modal("hide"),$("#subpay-"+subid).hide(),$("#modal-ajaxview-split").empty(),1!=basicinfo.printtype&&printRawHtml(data),prevsltab.trigger("click")}),100)}})}function showsplit(orderid){var url=basicinfo.baseurl+"ordermanage/order/showsplitorderlist/"+orderid;getAjaxModal(url,!1,"#modal-ajaxview-split","#payprint_split")}function possubpageprint(orderid){var csrf=$("#csrfhashresarvation").val();$.ajax({type:"GET",url:basicinfo.baseurl+"ordermanage/order/posprintdirectsub/"+orderid,data:{csrf_test_name:csrf},success:function(printdata){printRawHtml(printdata)}})}function itemnote(rowid,notes,qty,isupdate,isgroup=null){$("#foodnote").val(notes),$("#foodqty").val(qty),$("#foodcartid").val(rowid),$("#foodgroup").val(isgroup),1==isupdate?($("#notesmbt").text("Update Note"),$("#notesmbt").attr("onclick","addnotetoupdate()")):($("#notesmbt").text("Update Note"),$("#notesmbt").attr("onclick","addnotetoitem()")),$("#vieworder").modal("show")}function addnotetoitem(){var rowid=$("#foodcartid").val(),note=$("#foodnote").val(),foodqty=$("#foodqty").val(),csrf=$("#csrfhashresarvation").val(),geturl=basicinfo.baseurl+"ordermanage/order/additemnote",dataString="foodnote="+note+"&rowid="+rowid+"&qty="+foodqty+"&csrf_test_name="+csrf;$.ajax({type:"POST",url:geturl,data:dataString,success:function(data){setTimeout((function(){toastr.options={closeButton:!0,progressBar:!0,showMethod:"slideDown",timeOut:4e3},toastr.success("Note Added Successfully","Success"),$("#addfoodlist").html(data),$("#vieworder").modal("hide")}),100)}})}function addnotetoupdate(){var rowid=$("#foodcartid").val(),note=$("#foodnote").val(),orderid=$("#foodqty").val(),group=$("#foodgroup").val(),csrf=$("#csrfhashresarvation").val(),geturl=basicinfo.baseurl+"ordermanage/order/addnotetoupdate",dataString="foodnote="+note+"&rowid="+rowid+"&orderid="+orderid+"&group="+group+"&csrf_test_name="+csrf;$.ajax({type:"POST",url:geturl,data:dataString,success:function(data){setTimeout((function(){toastr.options={closeButton:!0,progressBar:!0,showMethod:"slideDown",timeOut:4e3},toastr.success("Note Added Successfully","Success"),$("#updatefoodlist").html(data),$("#vieworder").modal("hide")}),100)}})}function opencashregister(){var form=$("#cashopenfrm")[0],formdata=new FormData(form);$.ajax({type:"POST",url:basicinfo.baseurl+"ordermanage/order/addcashregister",data:formdata,processData:!1,contentType:!1,success:function(data){1==data?$("#openregister").modal("hide"):alert("Something Wrong!!! .Please Select Counter Number!!")}})}function closeopenresister(){var closeurl=basicinfo.baseurl+"ordermanage/order/cashregisterclose",csrf=$("#csrfhashresarvation").val();$.ajax({type:"GET",async:!1,url:closeurl,data:{csrf_test_name:csrf},success:function(data){$("#openclosecash").html(data);var fullheader="Cash Register In"+$("#rpth").text()+"\nCounter:"+$("#pcounter").val()+"\n"+$("#puser").val();$("#openregister").modal("show"),$("#RoleTbl").DataTable({responsive:!0,paging:!0,dom:"Bfrtip",lengthMenu:[[25,50,100,150,200,500,-1],[25,50,100,150,200,500,"All"]],buttons:[{extend:"csv",title:"Open-Close Cash Register",className:"btn-sm",footer:!0,header:!0,orientation:"landscape",messageTop:fullheader},{extend:"excel",title:"Open-Close Cash Register",className:"btn-sm",title:"exportTitle",messageTop:fullheader,footer:!0,header:!0,orientation:"landscape"},{extend:"pdfHtml5",title:"Open-Close Cash Register",className:"btn-sm",footer:!0,header:!0,orientation:"landscape",messageTop:fullheader,customize:function(doc){doc.defaultStyle.alignment="center",doc.content[1].table.widths=Array(doc.content[1].table.body[0].length+1).join("*").split("")}}],searching:!0,processing:!0})}})}function closecashregister(){var form=$("#cashopenfrm")[0],formdata=new FormData(form);$.ajax({type:"POST",url:basicinfo.baseurl+"ordermanage/order/closecashregister",data:formdata,processData:!1,contentType:!1,success:function(data){1==data?($("#openregister").modal("hide"),window.location.href=basicinfo.baseurl+"dashboard/home"):alert("Something Wrong On Cash Closing!!!")}})}function closeandprintcashregister(){var form=$("#cashopenfrm")[0],formdata=new FormData(form);$.ajax({type:"POST",url:basicinfo.baseurl+"ordermanage/order/closecashregister",data:formdata,processData:!1,contentType:!1,success:function(data){0==data?alert("Something Wrong On Cash Closing!!!"):($("#openregister").modal("hide"),window.location.href=basicinfo.baseurl+"dashboard/home?status=done")}})}setInterval((function(){load_unseen_notification(intervalc)}),3e4),setInterval((function(){$("li.active").trigger("click"),load_unseen_notificationqr()}),3e4),load_unseen_notification(),$(document).on("click","#add_new_payment_type",(function(){var gtotal=$("#grandtotal").val(),total=0;if($(".pay").each((function(){total+=parseFloat($(this).val())||0})),total==gtotal)return alert("Paid amount is exceed to Total amount."),$("#pay-amount").text("0"),!1;var orderid=$("#get-order-id").val(),csrf=$("#csrfhashresarvation").val(),url="showpaymentmodal/"+orderid+"/1";$.ajax({type:"GET",url:url,data:{csrf_test_name:csrf},success:function(data){$("#add_new_payment").append(data);var length=$(".number").length;$(".number:eq("+(length-1)+")").val(parseFloat($("#pay-amount").text())).trigger("keyup")}})})),$(document).on("click",".close_div",(function(){$(this).parent("div").remove(),changedueamount()})),$(document).on("click",".due_print",(function(){$(this).children("option:selected").val();var url=$(this).attr("data-url"),csrf=$("#csrfhashresarvation").val();$.ajax({type:"GET",url:url,data:{csrf_test_name:csrf},success:function(data){printRawHtml(data)}})})),$(document).on("click",".due_mergeprint",(function(){$(this).children("option:selected").val();var url=$(this).attr("data-url"),csrf=$("#csrfhashresarvation").val();$.ajax({type:"GET",url:url,data:{csrf_test_name:csrf},success:function(data){printRawHtml(data)}})})),$(document).on("change","#update_product_name",(function(){var idvid=$(this).children("option:selected").val().split("-"),id=idvid[0],vid=idvid[1],csrf=$("#csrfhashresarvation").val(),updateid=$("#saleinvoice").val(),url="addtocartupdate_uniqe/"+id+"/"+updateid,dataString="csrf_test_name="+csrf;if(1==$("#production_setting").val()){if(0==checkproduction(id,vid,1))return $("#update_product_name").html(""),!1}$.ajax({type:"GET",url:url,data:dataString,success:function(data){var myurl="adonsproductadd/"+id;$.ajax({type:"GET",url:myurl,data:dataString,success:function(data){$(".addonsinfo").html(data),$("#edit").modal("show");var tax=$("#tvat").val(),discount=$("#tdiscount").val(),tgtotal=$("#tgtotal").val();$("#vat").val(tax),$("#calvat").text(tax);var sc=$("#sc").val();$("#service_charge").val(sc),$("#invoice_discount").val(discount),1==basicinfo.isvatinclusive?$("#caltotal").text(tgtotal-tax):$("#caltotal").text(tgtotal),$("#grandtotal").val(tgtotal),$("#orggrandTotal").val(tgtotal),$("#orginattotal").val(tgtotal),$("#update_product_name").html("")}})}})})),$((function($){var barcodeScannerTimer;$("#customer_name").select2();var barcodeString="";$("#customer_name").on("select2:open",(function(){document.getElementsByClassName("select2-search__field")[0].onkeypress=function(evt){barcodeString+=String.fromCharCode(evt.charCode),clearTimeout(barcodeScannerTimer),barcodeScannerTimer=setTimeout((function(){!function(){if(""!=barcodeString){var customerid=Number(barcodeString).toString();Math.floor(customerid)==customerid&&$.isNumeric(customerid)&&$("#customer_name").select2().val(customerid).trigger("change"),$("#customer_name").val(customerid)}else alert("barcode is invalid: "+barcodeString);barcodeString=""}()}),300)}}))})),$(".lang_box").on("click",(function(event){$(this).next(".lang_options").slideToggle(400,(function(){}))}));