<?php
/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace SquareConnect\Model;

use \ArrayAccess;
/**
 * Order Class Doc Comment
 *
 * @category Class
 * @package  SquareConnect
 * <AUTHOR> Inc.
 * @license  http://www.apache.org/licenses/LICENSE-2.0 Apache License v2
 * @link     https://squareup.com/developers
 */
class Order implements ArrayAccess
{
    /**
      * Array of property to type mappings. Used for (de)serialization 
      * @var string[]
      */
    static $swaggerTypes = array(
        'id' => 'string',
        'location_id' => 'string',
        'reference_id' => 'string',
        'line_items' => '\SquareConnect\Model\OrderLineItem[]',
        'taxes' => '\SquareConnect\Model\OrderLineItemTax[]',
        'discounts' => '\SquareConnect\Model\OrderLineItemDiscount[]',
        'fulfillments' => '\SquareConnect\Model\OrderFulfillment[]',
        'total_money' => '\SquareConnect\Model\Money',
        'total_tax_money' => '\SquareConnect\Model\Money',
        'total_discount_money' => '\SquareConnect\Model\Money'
    );
  
    /** 
      * Array of attributes where the key is the local name, and the value is the original name
      * @var string[] 
      */
    static $attributeMap = array(
        'id' => 'id',
        'location_id' => 'location_id',
        'reference_id' => 'reference_id',
        'line_items' => 'line_items',
        'taxes' => 'taxes',
        'discounts' => 'discounts',
        'fulfillments' => 'fulfillments',
        'total_money' => 'total_money',
        'total_tax_money' => 'total_tax_money',
        'total_discount_money' => 'total_discount_money'
    );
  
    /**
      * Array of attributes to setter functions (for deserialization of responses)
      * @var string[]
      */
    static $setters = array(
        'id' => 'setId',
        'location_id' => 'setLocationId',
        'reference_id' => 'setReferenceId',
        'line_items' => 'setLineItems',
        'taxes' => 'setTaxes',
        'discounts' => 'setDiscounts',
        'fulfillments' => 'setFulfillments',
        'total_money' => 'setTotalMoney',
        'total_tax_money' => 'setTotalTaxMoney',
        'total_discount_money' => 'setTotalDiscountMoney'
    );
  
    /**
      * Array of attributes to getter functions (for serialization of requests)
      * @var string[]
      */
    static $getters = array(
        'id' => 'getId',
        'location_id' => 'getLocationId',
        'reference_id' => 'getReferenceId',
        'line_items' => 'getLineItems',
        'taxes' => 'getTaxes',
        'discounts' => 'getDiscounts',
        'fulfillments' => 'getFulfillments',
        'total_money' => 'getTotalMoney',
        'total_tax_money' => 'getTotalTaxMoney',
        'total_discount_money' => 'getTotalDiscountMoney'
    );
  
    /**
      * $id The order's unique ID.  This value is only present for Order objects created by the Orders API through the [CreateOrder](#endpoint-createorder) endpoint.
      * @var string
      */
    protected $id;
    /**
      * $location_id The ID of the merchant location this order is associated with.
      * @var string
      */
    protected $location_id;
    /**
      * $reference_id A client specified identifier to associate an entity in another system with this order.
      * @var string
      */
    protected $reference_id;
    /**
      * $line_items The line items included in the order.
      * @var \SquareConnect\Model\OrderLineItem[]
      */
    protected $line_items;
    /**
      * $taxes A list of taxes applied to this order. On read or retrieve, this list includes both order-level and item-level taxes. When creating an Order, set your order-level taxes in this list.
      * @var \SquareConnect\Model\OrderLineItemTax[]
      */
    protected $taxes;
    /**
      * $discounts A list of discounts applied to this order. On read or retrieve, this list includes both order-level and item-level discounts. When creating an Order, set your order-level discounts in this list.
      * @var \SquareConnect\Model\OrderLineItemDiscount[]
      */
    protected $discounts;
    /**
      * $fulfillments Details on order fulfillment.  Orders can only be created with at most one fulfillment. However, orders returned by the API may contain multiple fulfillments.
      * @var \SquareConnect\Model\OrderFulfillment[]
      */
    protected $fulfillments;
    /**
      * $total_money The total amount of money to collect for the order.
      * @var \SquareConnect\Model\Money
      */
    protected $total_money;
    /**
      * $total_tax_money The total tax amount of money to collect for the order.
      * @var \SquareConnect\Model\Money
      */
    protected $total_tax_money;
    /**
      * $total_discount_money The total discount amount of money to collect for the order.
      * @var \SquareConnect\Model\Money
      */
    protected $total_discount_money;

    /**
     * Constructor
     * @param mixed[] $data Associated array of property value initializing the model
     */
    public function __construct(array $data = null)
    {
        if ($data != null) {
            if (isset($data["id"])) {
              $this->id = $data["id"];
            } else {
              $this->id = null;
            }
            if (isset($data["location_id"])) {
              $this->location_id = $data["location_id"];
            } else {
              $this->location_id = null;
            }
            if (isset($data["reference_id"])) {
              $this->reference_id = $data["reference_id"];
            } else {
              $this->reference_id = null;
            }
            if (isset($data["line_items"])) {
              $this->line_items = $data["line_items"];
            } else {
              $this->line_items = null;
            }
            if (isset($data["taxes"])) {
              $this->taxes = $data["taxes"];
            } else {
              $this->taxes = null;
            }
            if (isset($data["discounts"])) {
              $this->discounts = $data["discounts"];
            } else {
              $this->discounts = null;
            }
            if (isset($data["fulfillments"])) {
              $this->fulfillments = $data["fulfillments"];
            } else {
              $this->fulfillments = null;
            }
            if (isset($data["total_money"])) {
              $this->total_money = $data["total_money"];
            } else {
              $this->total_money = null;
            }
            if (isset($data["total_tax_money"])) {
              $this->total_tax_money = $data["total_tax_money"];
            } else {
              $this->total_tax_money = null;
            }
            if (isset($data["total_discount_money"])) {
              $this->total_discount_money = $data["total_discount_money"];
            } else {
              $this->total_discount_money = null;
            }
        }
    }
    /**
     * Gets id
     * @return string
     */
    public function getId()
    {
        return $this->id;
    }
  
    /**
     * Sets id
     * @param string $id The order's unique ID.  This value is only present for Order objects created by the Orders API through the [CreateOrder](#endpoint-createorder) endpoint.
     * @return $this
     */
    public function setId($id)
    {
        $this->id = $id;
        return $this;
    }
    /**
     * Gets location_id
     * @return string
     */
    public function getLocationId()
    {
        return $this->location_id;
    }
  
    /**
     * Sets location_id
     * @param string $location_id The ID of the merchant location this order is associated with.
     * @return $this
     */
    public function setLocationId($location_id)
    {
        $this->location_id = $location_id;
        return $this;
    }
    /**
     * Gets reference_id
     * @return string
     */
    public function getReferenceId()
    {
        return $this->reference_id;
    }
  
    /**
     * Sets reference_id
     * @param string $reference_id A client specified identifier to associate an entity in another system with this order.
     * @return $this
     */
    public function setReferenceId($reference_id)
    {
        $this->reference_id = $reference_id;
        return $this;
    }
    /**
     * Gets line_items
     * @return \SquareConnect\Model\OrderLineItem[]
     */
    public function getLineItems()
    {
        return $this->line_items;
    }
  
    /**
     * Sets line_items
     * @param \SquareConnect\Model\OrderLineItem[] $line_items The line items included in the order.
     * @return $this
     */
    public function setLineItems($line_items)
    {
        $this->line_items = $line_items;
        return $this;
    }
    /**
     * Gets taxes
     * @return \SquareConnect\Model\OrderLineItemTax[]
     */
    public function getTaxes()
    {
        return $this->taxes;
    }
  
    /**
     * Sets taxes
     * @param \SquareConnect\Model\OrderLineItemTax[] $taxes A list of taxes applied to this order. On read or retrieve, this list includes both order-level and item-level taxes. When creating an Order, set your order-level taxes in this list.
     * @return $this
     */
    public function setTaxes($taxes)
    {
        $this->taxes = $taxes;
        return $this;
    }
    /**
     * Gets discounts
     * @return \SquareConnect\Model\OrderLineItemDiscount[]
     */
    public function getDiscounts()
    {
        return $this->discounts;
    }
  
    /**
     * Sets discounts
     * @param \SquareConnect\Model\OrderLineItemDiscount[] $discounts A list of discounts applied to this order. On read or retrieve, this list includes both order-level and item-level discounts. When creating an Order, set your order-level discounts in this list.
     * @return $this
     */
    public function setDiscounts($discounts)
    {
        $this->discounts = $discounts;
        return $this;
    }
    /**
     * Gets fulfillments
     * @return \SquareConnect\Model\OrderFulfillment[]
     */
    public function getFulfillments()
    {
        return $this->fulfillments;
    }
  
    /**
     * Sets fulfillments
     * @param \SquareConnect\Model\OrderFulfillment[] $fulfillments Details on order fulfillment.  Orders can only be created with at most one fulfillment. However, orders returned by the API may contain multiple fulfillments.
     * @return $this
     */
    public function setFulfillments($fulfillments)
    {
        $this->fulfillments = $fulfillments;
        return $this;
    }
    /**
     * Gets total_money
     * @return \SquareConnect\Model\Money
     */
    public function getTotalMoney()
    {
        return $this->total_money;
    }
  
    /**
     * Sets total_money
     * @param \SquareConnect\Model\Money $total_money The total amount of money to collect for the order.
     * @return $this
     */
    public function setTotalMoney($total_money)
    {
        $this->total_money = $total_money;
        return $this;
    }
    /**
     * Gets total_tax_money
     * @return \SquareConnect\Model\Money
     */
    public function getTotalTaxMoney()
    {
        return $this->total_tax_money;
    }
  
    /**
     * Sets total_tax_money
     * @param \SquareConnect\Model\Money $total_tax_money The total tax amount of money to collect for the order.
     * @return $this
     */
    public function setTotalTaxMoney($total_tax_money)
    {
        $this->total_tax_money = $total_tax_money;
        return $this;
    }
    /**
     * Gets total_discount_money
     * @return \SquareConnect\Model\Money
     */
    public function getTotalDiscountMoney()
    {
        return $this->total_discount_money;
    }
  
    /**
     * Sets total_discount_money
     * @param \SquareConnect\Model\Money $total_discount_money The total discount amount of money to collect for the order.
     * @return $this
     */
    public function setTotalDiscountMoney($total_discount_money)
    {
        $this->total_discount_money = $total_discount_money;
        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     * @param  integer $offset Offset 
     * @return boolean
     */
    public function offsetExists($offset)
    {
        return isset($this->$offset);
    }
  
    /**
     * Gets offset.
     * @param  integer $offset Offset 
     * @return mixed 
     */
    public function offsetGet($offset)
    {
        return $this->$offset;
    }
  
    /**
     * Sets value based on offset.
     * @param  integer $offset Offset 
     * @param  mixed   $value  Value to be set
     * @return void
     */
    public function offsetSet($offset, $value)
    {
        $this->$offset = $value;
    }
  
    /**
     * Unsets offset.
     * @param  integer $offset Offset 
     * @return void
     */
    public function offsetUnset($offset)
    {
        unset($this->$offset);
    }
  
    /**
     * Gets the string presentation of the object
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) {
            return json_encode(\SquareConnect\ObjectSerializer::sanitizeForSerialization($this), JSON_PRETTY_PRINT);
        } else {
            return json_encode(\SquareConnect\ObjectSerializer::sanitizeForSerialization($this));
        }
    }
}
