<?php
/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace SquareConnect\Model;

use \ArrayAccess;
/**
 * TimeRange Class Doc Comment
 *
 * @category Class
 * @package  SquareConnect
 * <AUTHOR> Inc.
 * @license  http://www.apache.org/licenses/LICENSE-2.0 Apache License v2
 * @link     https://squareup.com/developers
 */
class TimeRange implements ArrayAccess
{
    /**
      * Array of property to type mappings. Used for (de)serialization 
      * @var string[]
      */
    static $swaggerTypes = array(
        'start_at' => 'string',
        'end_at' => 'string'
    );
  
    /** 
      * Array of attributes where the key is the local name, and the value is the original name
      * @var string[] 
      */
    static $attributeMap = array(
        'start_at' => 'start_at',
        'end_at' => 'end_at'
    );
  
    /**
      * Array of attributes to setter functions (for deserialization of responses)
      * @var string[]
      */
    static $setters = array(
        'start_at' => 'setStartAt',
        'end_at' => 'setEndAt'
    );
  
    /**
      * Array of attributes to getter functions (for serialization of requests)
      * @var string[]
      */
    static $getters = array(
        'start_at' => 'getStartAt',
        'end_at' => 'getEndAt'
    );
  
    /**
      * $start_at A datetime value in RFC-3339 format indicating when the time range starts.
      * @var string
      */
    protected $start_at;
    /**
      * $end_at A datetime value in RFC-3339 format indicating when the time range ends.
      * @var string
      */
    protected $end_at;

    /**
     * Constructor
     * @param mixed[] $data Associated array of property value initializing the model
     */
    public function __construct(array $data = null)
    {
        if ($data != null) {
            if (isset($data["start_at"])) {
              $this->start_at = $data["start_at"];
            } else {
              $this->start_at = null;
            }
            if (isset($data["end_at"])) {
              $this->end_at = $data["end_at"];
            } else {
              $this->end_at = null;
            }
        }
    }
    /**
     * Gets start_at
     * @return string
     */
    public function getStartAt()
    {
        return $this->start_at;
    }
  
    /**
     * Sets start_at
     * @param string $start_at A datetime value in RFC-3339 format indicating when the time range starts.
     * @return $this
     */
    public function setStartAt($start_at)
    {
        $this->start_at = $start_at;
        return $this;
    }
    /**
     * Gets end_at
     * @return string
     */
    public function getEndAt()
    {
        return $this->end_at;
    }
  
    /**
     * Sets end_at
     * @param string $end_at A datetime value in RFC-3339 format indicating when the time range ends.
     * @return $this
     */
    public function setEndAt($end_at)
    {
        $this->end_at = $end_at;
        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     * @param  integer $offset Offset 
     * @return boolean
     */
    public function offsetExists($offset)
    {
        return isset($this->$offset);
    }
  
    /**
     * Gets offset.
     * @param  integer $offset Offset 
     * @return mixed 
     */
    public function offsetGet($offset)
    {
        return $this->$offset;
    }
  
    /**
     * Sets value based on offset.
     * @param  integer $offset Offset 
     * @param  mixed   $value  Value to be set
     * @return void
     */
    public function offsetSet($offset, $value)
    {
        $this->$offset = $value;
    }
  
    /**
     * Unsets offset.
     * @param  integer $offset Offset 
     * @return void
     */
    public function offsetUnset($offset)
    {
        unset($this->$offset);
    }
  
    /**
     * Gets the string presentation of the object
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) {
            return json_encode(\SquareConnect\ObjectSerializer::sanitizeForSerialization($this), JSON_PRETTY_PRINT);
        } else {
            return json_encode(\SquareConnect\ObjectSerializer::sanitizeForSerialization($this));
        }
    }
}
