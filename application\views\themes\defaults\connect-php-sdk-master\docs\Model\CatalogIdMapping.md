# CatalogIdMapping

## Properties
Name | Getter | Setter | Type | Description | Notes
------------ | ------------- | ------------- | ------------- | ------------- | -------------
**client_object_id** | getClientObjectId() | setClientObjectId($value) | **string** | The client-supplied, temporary &#x60;#&#x60;-prefixed ID for a new [CatalogObject](#type-catalogobject). | [optional] 
**object_id** | getObjectId() | setObjectId($value) | **string** | The permanent ID for the [CatalogObject](#type-catalogobject) created by the server. | [optional] 

Note: All properties are protected and only accessed via getters and setters.

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)

