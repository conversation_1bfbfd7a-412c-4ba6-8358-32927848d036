<?php
/**
 * Html2Pdf Library - Tests
 *
 * HTML => PDF converter
 * distributed under the OSL-3.0 License
 *
 * @package   Html2pdf
 * <AUTHOR> MINGUET <<EMAIL>>
 * @copyright 2023 Laurent MINGUET
 */

namespace Spipu\Html2Pdf\Tests\CrossVersionCompatibility\PhpUnit9;

use PHPUnit\Framework\TestCase;
use Spipu\Html2Pdf\Parsing\TextParser;

abstract class TextParserTestCase extends TestCase
{
    /**
     * @var TextParser
     */
    protected $parser;

    protected function setUp(): void
    {
        $this->parser = new TextParser();
    }
}
