# TenderCashDetails

## Properties
Name | Getter | Setter | Type | Description | Notes
------------ | ------------- | ------------- | ------------- | ------------- | -------------
**buyer_tendered_money** | getBuyerTenderedMoney() | setBuyerTenderedMoney($value) | [**\SquareConnect\Model\Money**](Money.md) | The total amount of cash provided by the buyer, before change is given. | [optional] 
**change_back_money** | getChangeBackMoney() | setChangeBackMoney($value) | [**\SquareConnect\Model\Money**](Money.md) | The amount of change returned to the buyer. | [optional] 

Note: All properties are protected and only accessed via getters and setters.

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)

