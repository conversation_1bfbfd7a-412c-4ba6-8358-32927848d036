"use strict";
function printDiv(divName) {
	var printContents = document.getElementById(divName).innerHTML;
	var originalContents = document.body.innerHTML;
	document.body.innerHTML = printContents;
	document.body.style.marginTop = "0px";
	window.print();
	document.body.innerHTML = originalContents;
}

function getreport() {
	var from_date = $('#from_date').val();
	var to_date = $('#to_date').val();
	var paytype = $('#paytype').val();
	var invoice_no = $('#invoice_no').val();
	var customer_id = $('#customer_id').val();


	if (from_date == '') {
		alert("Please select from date");
		return false;
	}
	if (to_date == '') {
		alert("Please select To date");
		return false;
	}
	var myurl = baseurl + 'report/reports/salereport';
	var csrf = $('#csrfhashresarvation').val();
	var dataString = "from_date=" + from_date + '&to_date=' + to_date + '&paytype=' + paytype + '&invoice_no=' + invoice_no + '&customer_id=' + customer_id + '&csrf_test_name=' + csrf;
	$.ajax({
		type: "POST",
		url: myurl,
		data: dataString,
		success: function (data) {
			$('#getresult2').html(data);
			$('#respritbl').DataTable({
				responsive: true,
				paging: true,
				"language": {
					"sProcessing": lang.Processingod,
					"sSearch": lang.search,
					"sLengthMenu": lang.sLengthMenu,
					"sInfo": lang.sInfo,
					"sInfoEmpty": lang.sInfoEmpty,
					"sInfoFiltered": lang.sInfoFiltered,
					"sInfoPostFix": "",
					"sLoadingRecords": lang.sLoadingRecords,
					"sZeroRecords": lang.sZeroRecords,
					"sEmptyTable": lang.sEmptyTable,
					"oPaginate": {
						"sFirst": lang.sFirst,
						"sPrevious": lang.sPrevious,
						"sNext": lang.sNext,
						"sLast": lang.sLast
					},
					"oAria": {
						"sSortAscending": ":" + lang.sSortAscending + '"',
						"sSortDescending": ":" + lang.sSortDescending + '"'
					},
					"select": {
						"rows": {
							"_": lang._sign,
							"0": lang._0sign,
							"1": lang._1sign
						}
					},
					buttons: {
						copy: lang.copy,
						csv: lang.csv,
						excel: lang.excel,
						pdf: lang.pdf,
						print: lang.print,
						colvis: lang.colvis
					}
				},
				dom: 'Blfrtip',
				lengthChange: true,
				"lengthMenu": [[25, 50, 100, 150, 200, 500, -1], [25, 50, 100, 150, 200, 500, "All"]],
				buttons: [
					{ extend: 'copy', className: 'btn-sm', footer: true },
					{ extend: 'csv', title: 'Report', className: 'btn-sm', footer: true },
					{ extend: 'excel', title: 'Report', className: 'btn-sm', title: 'exportTitle', footer: true },
					{ extend: 'pdf', title: 'Report', className: 'btn-sm', footer: true },
					{ extend: 'print', className: 'btn-sm', footer: true },
					{ extend: 'colvis', className: 'btn-sm', footer: true }
				],
				"searching": true,
				"processing": true,

			});
		}
	});
}
function generatereport() {
	var from_date = $('#from_date').val();
	var to_date = $('#to_date').val();
	var csrf = $('#csrfhashresarvation').val();
	if (from_date == '') {
		alert("Please select from date");
		return false;
	}
	if (to_date == '') {
		alert("Please select To date");
		return false;
	}
	var myurl = baseurl + 'report/reports/generaterpt';
	var dataString = "from_date=" + from_date + '&to_date=' + to_date + '&csrf_test_name=' + csrf;
	$.ajax({
		type: "POST",
		url: myurl,
		data: dataString,
		success: function (data) {

		}
	});
}