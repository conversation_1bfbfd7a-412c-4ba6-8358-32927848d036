<?php
/**
 * NOTE: This class is auto generated by the swagger code generator program. 
 * https://github.com/swagger-api/swagger-codegen 
 * Do not edit the class manually.
 */

namespace SquareConnect\Api;

use \SquareConnect\Configuration;
use \SquareConnect\ApiClient;
use \SquareConnect\ApiException;
use \SquareConnect\ObjectSerializer;

/**
 * CheckoutApiTest Class Doc Comment
 *
 * @category Class
 * @package  SquareConnect
 * <AUTHOR> Inc.
 * @license  http://www.apache.org/licenses/LICENSE-2.0 Apache Licene v2
 * @link     https://squareup.com/developers
 */
class CheckoutApiTest extends \PHPUnit_Framework_TestCase
{
    private static $api;
    private static $test_accounts;
    private static $location_id;

    /**
     * Setup before running each test case
     */
    public static function setUpBeforeClass() {
        self::$api = new \SquareConnect\Api\CheckoutApi();
        self::$test_accounts = new \SquareConnect\TestAccounts();
        // Configure OAuth2 access token for authorization: oauth2
        $account = self::$test_accounts->{'US-Prod-Sandbox'};
        $access_token = $account->access_token;
        Configuration::getDefaultConfiguration()->setAccessToken($access_token);

        self::$location_id = $account->location_id;

    }

    /**
     * Clean up after running each test case
     */
    public static function tearDownAfterClass() {

    }

    /**
     * Test case for createCheckout
     *
     * CreateCheckout
     *
     */
    public function test_createCheckout() {
        $body = new \SquareConnect\Model\CreateCheckoutRequest([
            "idempotency_key" => uniqid(),
            "order" => [
                "reference_id" => "reference_id",
                "line_items" => [
                    [
                        "name" => "Printed T Shirt",
                        "quantity" => "2",
                        "base_price_money" => [
                            "amount" => 1500,
                            "currency" => "USD"
                        ],
                        "discounts" => [
                            [
                                "name" => "7% off previous season item",
                                "percentage" => "7"
                            ],
                            [
                                "name" => "$3 off Customer Discount",
                                "amount_money" => [
                                    "amount" => 300,
                                    "currency" => "USD"
                                ]
                            ]
                        ]
                    ],
                    [
                        "name" => "Slim Jeans",
                        "quantity" => "1",
                        "base_price_money" => [
                          "amount" => 2500,
                          "currency" => "USD"
                        ]
                    ],
                    [
                        "name" => "Woven Sweater",
                        "quantity" => "3",
                        "base_price_money" => [
                            "amount" => 3500,
                            "currency" => "USD"
                        ],
                        "discounts" => [
                            [
                                "name" => "$11 off Customer Discount",
                                "amount_money" => [
                                    "amount" => 1100,
                                    "currency" => "USD"
                                ]
                            ]
                        ],
                        "taxes" => [
                            [
                              "name" => "Fair Trade Tax",
                              "percentage" => "5"
                            ]
                        ]
                    ]
                ],
                "discounts" => [
                    [
                        "name" => "Father's day 12% OFF",
                        "percentage" => "12"
                    ],
                    [
                        "name" => "Global Sales $55 OFF",
                        "amount_money" =>  [
                            "amount" => 5500,
                            "currency" => "USD"
                        ]
                    ]
                ],
                "taxes" => [
                    [
                        "name" => "Sales Tax",
                        "percentage" => "8.5"
                    ]
                ]
            ],
            "ask_for_shipping_address" => true,
            "merchant_support_email" => "<EMAIL>",
            "pre_populate_buyer_email" => "<EMAIL>",
            "pre_populate_shipping_address" => [
                "address_line_1" => "1455 Market St.",
                "address_line_2" => "Suite 600",
                "locality" => "San Francisco",
                "administrative_district_level_1" => "CA",
                "postal_code" => "94103",
                "country" => "US",
                "first_name" => "Jane",
                "last_name" => "Doe"
            ],
            "redirect_url" => "https://docs.connect.squareup.com/order-confirm"
        ]);

        $response = self::$api->createCheckout(self::$location_id, $body);
        $this->assertEmpty($response->getErrors());
        $this->assertStringStartsWith("https://connect.", $response->getCheckout()->getCheckoutPageUrl());
    }
}
