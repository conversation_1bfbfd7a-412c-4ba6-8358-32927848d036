<?php
/**
 * NOTE: This class is auto generated by the swagger code generator program. 
 * https://github.com/swagger-api/swagger-codegen 
 * Do not edit the class manually.
 */

namespace SquareConnect\Api;

use \SquareConnect\Configuration;
use \SquareConnect\ApiClient;
use \SquareConnect\ApiException;
use \SquareConnect\ObjectSerializer;

/**
 * V1ItemsApiTest Class Doc Comment
 *
 * @category Class
 * @package  SquareConnect
 * <AUTHOR> Inc.
 * @license  http://www.apache.org/licenses/LICENSE-2.0 Apache Licene v2
 * @link     https://squareup.com/developers
 */
class V1ItemsApiTest extends \PHPUnit_Framework_TestCase
{

    /**
     * Setup before running each test case
     */
    public static function setUpBeforeClass() {

    }

    /**
     * Clean up after running each test case
     */
    public static function tearDownAfterClass() {

    }

    /**
     * Test case for adjustInventory
     *
     * Adjusts an item variation's current available inventory.
     *
     */
    public function test_adjustInventory() {

    }
    /**
     * Test case for applyFee
     *
     * Associates a fee with an item, meaning the fee is automatically applied to the item in Square Register.
     *
     */
    public function test_applyFee() {

    }
    /**
     * Test case for applyModifierList
     *
     * Associates a modifier list with an item, meaning modifier options from the list can be applied to the item.
     *
     */
    public function test_applyModifierList() {

    }
    /**
     * Test case for createCategory
     *
     * Creates an item category.
     *
     */
    public function test_createCategory() {

    }
    /**
     * Test case for createDiscount
     *
     * Creates a discount.
     *
     */
    public function test_createDiscount() {

    }
    /**
     * Test case for createFee
     *
     * Creates a fee (tax).
     *
     */
    public function test_createFee() {

    }
    /**
     * Test case for createItem
     *
     * Creates an item and at least one variation for it.
     *
     */
    public function test_createItem() {

    }
    /**
     * Test case for createModifierList
     *
     * Creates an item modifier list and at least one modifier option for it.
     *
     */
    public function test_createModifierList() {

    }
    /**
     * Test case for createModifierOption
     *
     * Creates an item modifier option and adds it to a modifier list.
     *
     */
    public function test_createModifierOption() {

    }
    /**
     * Test case for createPage
     *
     * Creates a Favorites page in Square Register.
     *
     */
    public function test_createPage() {

    }
    /**
     * Test case for createVariation
     *
     * Creates an item variation for an existing item.
     *
     */
    public function test_createVariation() {

    }
    /**
     * Test case for deleteCategory
     *
     * Deletes an existing item category.
     *
     */
    public function test_deleteCategory() {

    }
    /**
     * Test case for deleteDiscount
     *
     * Deletes an existing discount.
     *
     */
    public function test_deleteDiscount() {

    }
    /**
     * Test case for deleteFee
     *
     * Deletes an existing fee (tax).
     *
     */
    public function test_deleteFee() {

    }
    /**
     * Test case for deleteItem
     *
     * Deletes an existing item and all item variations associated with it.
     *
     */
    public function test_deleteItem() {

    }
    /**
     * Test case for deleteModifierList
     *
     * Deletes an existing item modifier list and all modifier options associated with it.
     *
     */
    public function test_deleteModifierList() {

    }
    /**
     * Test case for deleteModifierOption
     *
     * Deletes an existing item modifier option from a modifier list.
     *
     */
    public function test_deleteModifierOption() {

    }
    /**
     * Test case for deletePage
     *
     * Deletes an existing Favorites page and all of its cells.
     *
     */
    public function test_deletePage() {

    }
    /**
     * Test case for deletePageCell
     *
     * Deletes a cell from a Favorites page in Square Register.
     *
     */
    public function test_deletePageCell() {

    }
    /**
     * Test case for deleteVariation
     *
     * Deletes an existing item variation from an item.
     *
     */
    public function test_deleteVariation() {

    }
    /**
     * Test case for listCategories
     *
     * Lists all of a location's item categories.
     *
     */
    public function test_listCategories() {

    }
    /**
     * Test case for listDiscounts
     *
     * Lists all of a location's discounts.
     *
     */
    public function test_listDiscounts() {

    }
    /**
     * Test case for listFees
     *
     * Lists all of a location's fees (taxes).
     *
     */
    public function test_listFees() {

    }
    /**
     * Test case for listInventory
     *
     * Provides inventory information for all of a merchant's inventory-enabled item variations.
     *
     */
    public function test_listInventory() {

    }
    /**
     * Test case for listItems
     *
     * Provides summary information for all of a location's items.
     *
     */
    public function test_listItems() {

    }
    /**
     * Test case for listModifierLists
     *
     * Lists all of a location's modifier lists.
     *
     */
    public function test_listModifierLists() {

    }
    /**
     * Test case for listPages
     *
     * Lists all of a location's Favorites pages in Square Register.
     *
     */
    public function test_listPages() {

    }
    /**
     * Test case for removeFee
     *
     * Removes a fee assocation from an item, meaning the fee is no longer automatically applied to the item in Square Register.
     *
     */
    public function test_removeFee() {

    }
    /**
     * Test case for removeModifierList
     *
     * Removes a modifier list association from an item, meaning modifier options from the list can no longer be applied to the item.
     *
     */
    public function test_removeModifierList() {

    }
    /**
     * Test case for retrieveItem
     *
     * Provides the details for a single item, including associated modifier lists and fees.
     *
     */
    public function test_retrieveItem() {

    }
    /**
     * Test case for retrieveModifierList
     *
     * Provides the details for a single modifier list.
     *
     */
    public function test_retrieveModifierList() {

    }
    /**
     * Test case for updateCategory
     *
     * Modifies the details of an existing item category.
     *
     */
    public function test_updateCategory() {

    }
    /**
     * Test case for updateDiscount
     *
     * Modifies the details of an existing discount.
     *
     */
    public function test_updateDiscount() {

    }
    /**
     * Test case for updateFee
     *
     * Modifies the details of an existing fee (tax).
     *
     */
    public function test_updateFee() {

    }
    /**
     * Test case for updateItem
     *
     * Modifies the core details of an existing item.
     *
     */
    public function test_updateItem() {

    }
    /**
     * Test case for updateModifierList
     *
     * Modifies the details of an existing item modifier list.
     *
     */
    public function test_updateModifierList() {

    }
    /**
     * Test case for updateModifierOption
     *
     * Modifies the details of an existing item modifier option.
     *
     */
    public function test_updateModifierOption() {

    }
    /**
     * Test case for updatePage
     *
     * Modifies the details of a Favorites page in Square Register.
     *
     */
    public function test_updatePage() {

    }
    /**
     * Test case for updatePageCell
     *
     * Modifies a cell of a Favorites page in Square Register.
     *
     */
    public function test_updatePageCell() {

    }
    /**
     * Test case for updateVariation
     *
     * Modifies the details of an existing item variation.
     *
     */
    public function test_updateVariation() {

    }
}
