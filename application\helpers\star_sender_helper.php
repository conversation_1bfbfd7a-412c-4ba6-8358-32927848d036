<?php
    // var set
    $keyToken = "11b40b55-6942-4309-a4b0-7d344634108a";

    // message formatter
    function starSender_formatMessage($lines = []) {
        // Accepts array of lines and joins with newlines
        return implode("\n", $lines);
    }

    function starSender_default() {
		$curl = curl_init();

		$pesan = [
			"messageType" => "text",
			"to" => "085274897212",
			"body" => "default dari helper starsender\nini baris 2"
		];

		curl_setopt_array($curl, array(
		CURLOPT_URL => 'https://api.starsender.online/api/send',
		CURLOPT_RETURNTRANSFER => true,
		CURLOPT_ENCODING => '',
		CURLOPT_MAXREDIRS => 10,
		CURLOPT_TIMEOUT => 0,
		CURLOPT_FOLLOWLOCATION => true,
		CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		CURLOPT_CUSTOMREQUEST => 'POST',
		CURLOPT_POSTFIELDS => json_encode($pesan),
		CURLOPT_HTTPHEADER => array(
			'Content-Type:application/json',
			'Authorization: 11b40b55-6942-4309-a4b0-7d344634108a'
		),
		));

		$response = curl_exec($curl);

		curl_close($curl);

        return json_decode($response, true);
	}

    function starSender_send($params = []) {
        // extract params
        $message  = isset($params['message']) ? $params['message'] : 'no message set';
        $to       = isset($params['to']) ? $params['to'] : null;
        $fileUrl  = isset($params['fileUrl']) ? $params['fileUrl'] : null;

        // Map name to number
        switch ($to) {
            case 'ilham':
                $receiverNumber = '082171469407';
                break;
            case 'indra':
                $receiverNumber = '085274897212';
                break;
			case 'tiwi':
                $receiverNumber = '082283778797';
                break;
			case 'bunda':
                $receiverNumber = '08117527774';
                break;
            default:
                $receiverNumber = '085274897212';
                break;
        }

		$payload = [
			"messageType" => "text",
			"to" => $receiverNumber,
			"body" => $message,
            "delay" => 10
		];

        if ($fileUrl) {
            $payload['file'] = $fileUrl;
        }

        $curl = curl_init();

		curl_setopt_array($curl, array(
		CURLOPT_URL => 'https://api.starsender.online/api/send',
		CURLOPT_RETURNTRANSFER => true,
		CURLOPT_ENCODING => '',
		CURLOPT_MAXREDIRS => 10,
		CURLOPT_TIMEOUT => 0,
		CURLOPT_FOLLOWLOCATION => true,
		CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		CURLOPT_CUSTOMREQUEST => 'POST',
		CURLOPT_POSTFIELDS => json_encode($payload),
		CURLOPT_HTTPHEADER => array(
			'Content-Type:application/json',
			'Authorization: 11b40b55-6942-4309-a4b0-7d344634108a'
		),
		));

		$response = curl_exec($curl);

		curl_close($curl);

        return json_decode($response, true);
	}  

    function starSender_curl($payload) {
        $curl = curl_init();

		curl_setopt_array($curl, array(
		CURLOPT_URL => 'https://api.starsender.online/api/send',
		CURLOPT_RETURNTRANSFER => true,
		CURLOPT_ENCODING => '',
		CURLOPT_MAXREDIRS => 10,
		CURLOPT_TIMEOUT => 0,
		CURLOPT_FOLLOWLOCATION => true,
		CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		CURLOPT_CUSTOMREQUEST => 'POST',
		CURLOPT_POSTFIELDS => json_encode($payload),
		CURLOPT_HTTPHEADER => array(
			'Content-Type:application/json',
			'Authorization: 11b40b55-6942-4309-a4b0-7d344634108a'
		),
		));

		$response = curl_exec($curl);

		curl_close($curl);

        return json_decode($response, true);
    }
