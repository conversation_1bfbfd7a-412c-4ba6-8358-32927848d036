<?php
/**
 * NOTE: This class is auto generated by the swagger code generator program. 
 * https://github.com/swagger-api/swagger-codegen 
 * Do not edit the class manually.
 */

namespace SquareConnect\Api;

use \SquareConnect\Configuration;
use \SquareConnect\ApiClient;
use \SquareConnect\ApiException;
use \SquareConnect\ObjectSerializer;

/**
 * V1EmployeesApi Class Doc Comment
 *
 * @category Class
 * @package  SquareConnect
 * <AUTHOR> Inc.
 * @license  http://www.apache.org/licenses/LICENSE-2.0 Apache Licene v2
 * @link     https://squareup.com/developers
 */
class V1EmployeesApi
{

    /**
     * API Client
     * @var \SquareConnect\ApiClient instance of the ApiClient
     */
    protected $apiClient;
  
    /**
     * Constructor
     * @param \SquareConnect\ApiClient|null $apiClient The api client to use
     */
    function __construct($apiClient = null)
    {
        if ($apiClient == null) {
            $apiClient = new ApiClient();
            $apiClient->getConfig()->setHost('https://connect.squareup.com');
        }
  
        $this->apiClient = $apiClient;
    }
  
    /**
     * Get API client
     * @return \SquareConnect\ApiClient get the API client
     */
    public function getApiClient()
    {
        return $this->apiClient;
    }
  
    /**
     * Set the API client
     * @param \SquareConnect\ApiClient $apiClient set the API client
     * @return V1EmployeesApi
     */
    public function setApiClient(ApiClient $apiClient)
    {
        $this->apiClient = $apiClient;
        return $this;
    }
  
    /**
     * createEmployee
     *
     * Creates an employee for a business.
     *
     * @param \SquareConnect\Model\V1Employee $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return \SquareConnect\Model\V1Employee
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function createEmployee($body)
    {
        list($response, $statusCode, $httpHeader) = $this->createEmployeeWithHttpInfo ($body);
        return $response; 
    }


    /**
     * createEmployeeWithHttpInfo
     *
     * Creates an employee for a business.
     *
     * @param \SquareConnect\Model\V1Employee $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return Array of \SquareConnect\Model\V1Employee, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function createEmployeeWithHttpInfo($body)
    {
        
        // verify the required parameter 'body' is set
        if ($body === null) {
            throw new \InvalidArgumentException('Missing the required parameter $body when calling createEmployee');
        }
  
        // parse inputs
        $resourcePath = "/v1/me/employees";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        // body params
        $_tempBody = null;
        if (isset($body)) {
            $_tempBody = $body;
        }
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'POST',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Employee'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Employee', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Employee', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * createEmployeeRole
     *
     * Creates an employee role you can then assign to employees.
     *
     * @param \SquareConnect\Model\V1EmployeeRole $employee_role An EmployeeRole object with a name and permissions, and an optional owner flag. (required)
     * @return \SquareConnect\Model\V1EmployeeRole
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function createEmployeeRole($employee_role)
    {
        list($response, $statusCode, $httpHeader) = $this->createEmployeeRoleWithHttpInfo ($employee_role);
        return $response; 
    }


    /**
     * createEmployeeRoleWithHttpInfo
     *
     * Creates an employee role you can then assign to employees.
     *
     * @param \SquareConnect\Model\V1EmployeeRole $employee_role An EmployeeRole object with a name and permissions, and an optional owner flag. (required)
     * @return Array of \SquareConnect\Model\V1EmployeeRole, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function createEmployeeRoleWithHttpInfo($employee_role)
    {
        
        // verify the required parameter 'employee_role' is set
        if ($employee_role === null) {
            throw new \InvalidArgumentException('Missing the required parameter $employee_role when calling createEmployeeRole');
        }
  
        // parse inputs
        $resourcePath = "/v1/me/roles";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        // body params
        $_tempBody = null;
        if (isset($employee_role)) {
            $_tempBody = $employee_role;
        }
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'POST',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1EmployeeRole'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1EmployeeRole', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1EmployeeRole', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * createTimecard
     *
     * Creates a timecard for an employee. Each timecard corresponds to a single shift.
     *
     * @param \SquareConnect\Model\V1Timecard $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return \SquareConnect\Model\V1Timecard
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function createTimecard($body)
    {
        list($response, $statusCode, $httpHeader) = $this->createTimecardWithHttpInfo ($body);
        return $response; 
    }


    /**
     * createTimecardWithHttpInfo
     *
     * Creates a timecard for an employee. Each timecard corresponds to a single shift.
     *
     * @param \SquareConnect\Model\V1Timecard $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return Array of \SquareConnect\Model\V1Timecard, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function createTimecardWithHttpInfo($body)
    {
        
        // verify the required parameter 'body' is set
        if ($body === null) {
            throw new \InvalidArgumentException('Missing the required parameter $body when calling createTimecard');
        }
  
        // parse inputs
        $resourcePath = "/v1/me/timecards";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        // body params
        $_tempBody = null;
        if (isset($body)) {
            $_tempBody = $body;
        }
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'POST',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Timecard'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Timecard', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Timecard', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * deleteTimecard
     *
     * Deletes a timecard. Deleted timecards are still accessible from Connect API endpoints, but the value of their deleted field is set to true. See Handling deleted timecards for more information.
     *
     * @param string $timecard_id The ID of the timecard to delete. (required)
     * @return object
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function deleteTimecard($timecard_id)
    {
        list($response, $statusCode, $httpHeader) = $this->deleteTimecardWithHttpInfo ($timecard_id);
        return $response; 
    }


    /**
     * deleteTimecardWithHttpInfo
     *
     * Deletes a timecard. Deleted timecards are still accessible from Connect API endpoints, but the value of their deleted field is set to true. See Handling deleted timecards for more information.
     *
     * @param string $timecard_id The ID of the timecard to delete. (required)
     * @return Array of object, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function deleteTimecardWithHttpInfo($timecard_id)
    {
        
        // verify the required parameter 'timecard_id' is set
        if ($timecard_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $timecard_id when calling deleteTimecard');
        }
  
        // parse inputs
        $resourcePath = "/v1/me/timecards/{timecard_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($timecard_id !== null) {
            $resourcePath = str_replace(
                "{" . "timecard_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($timecard_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'DELETE',
                $queryParams, $httpBody,
                $headerParams, 'object'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, 'object', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), 'object', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * listCashDrawerShifts
     *
     * Provides the details for all of a location's cash drawer shifts during a date range. The date range you specify cannot exceed 90 days.
     *
     * @param string $location_id The ID of the location to list cash drawer shifts for. (required)
     * @param string $order The order in which cash drawer shifts are listed in the response, based on their created_at field. Default value: ASC (optional)
     * @param string $begin_time The beginning of the requested reporting period, in ISO 8601 format. Default value: The current time minus 90 days. (optional)
     * @param string $end_time The beginning of the requested reporting period, in ISO 8601 format. Default value: The current time. (optional)
     * @return \SquareConnect\Model\V1CashDrawerShift[]
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function listCashDrawerShifts($location_id, $order = null, $begin_time = null, $end_time = null)
    {
        list($response, $statusCode, $httpHeader) = $this->listCashDrawerShiftsWithHttpInfo ($location_id, $order, $begin_time, $end_time);
        return $response; 
    }


    /**
     * listCashDrawerShiftsWithHttpInfo
     *
     * Provides the details for all of a location's cash drawer shifts during a date range. The date range you specify cannot exceed 90 days.
     *
     * @param string $location_id The ID of the location to list cash drawer shifts for. (required)
     * @param string $order The order in which cash drawer shifts are listed in the response, based on their created_at field. Default value: ASC (optional)
     * @param string $begin_time The beginning of the requested reporting period, in ISO 8601 format. Default value: The current time minus 90 days. (optional)
     * @param string $end_time The beginning of the requested reporting period, in ISO 8601 format. Default value: The current time. (optional)
     * @return Array of \SquareConnect\Model\V1CashDrawerShift[], HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function listCashDrawerShiftsWithHttpInfo($location_id, $order = null, $begin_time = null, $end_time = null)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling listCashDrawerShifts');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/cash-drawer-shifts";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        // query params
        if ($order !== null) {
            $queryParams['order'] = $this->apiClient->getSerializer()->toQueryValue($order);
        }// query params
        if ($begin_time !== null) {
            $queryParams['begin_time'] = $this->apiClient->getSerializer()->toQueryValue($begin_time);
        }// query params
        if ($end_time !== null) {
            $queryParams['end_time'] = $this->apiClient->getSerializer()->toQueryValue($end_time);
        }
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'GET',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1CashDrawerShift[]'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1CashDrawerShift[]', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1CashDrawerShift[]', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * listEmployeeRoles
     *
     * Provides summary information for all of a business's employee roles.
     *
     * @param string $order The order in which employees are listed in the response, based on their created_at field.Default value: ASC (optional)
     * @param int $limit The maximum integer number of employee entities to return in a single response. Default 100, maximum 200. (optional)
     * @param string $batch_token A pagination cursor to retrieve the next set of results for your original query to the endpoint. (optional)
     * @return \SquareConnect\Model\V1EmployeeRole[]
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function listEmployeeRoles($order = null, $limit = null, $batch_token = null)
    {
        list($response, $statusCode, $httpHeader) = $this->listEmployeeRolesWithHttpInfo ($order, $limit, $batch_token);
        return $response; 
    }


    /**
     * listEmployeeRolesWithHttpInfo
     *
     * Provides summary information for all of a business's employee roles.
     *
     * @param string $order The order in which employees are listed in the response, based on their created_at field.Default value: ASC (optional)
     * @param int $limit The maximum integer number of employee entities to return in a single response. Default 100, maximum 200. (optional)
     * @param string $batch_token A pagination cursor to retrieve the next set of results for your original query to the endpoint. (optional)
     * @return Array of \SquareConnect\Model\V1EmployeeRole[], HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function listEmployeeRolesWithHttpInfo($order = null, $limit = null, $batch_token = null)
    {
        
  
        // parse inputs
        $resourcePath = "/v1/me/roles";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        // query params
        if ($order !== null) {
            $queryParams['order'] = $this->apiClient->getSerializer()->toQueryValue($order);
        }// query params
        if ($limit !== null) {
            $queryParams['limit'] = $this->apiClient->getSerializer()->toQueryValue($limit);
        }// query params
        if ($batch_token !== null) {
            $queryParams['batch_token'] = $this->apiClient->getSerializer()->toQueryValue($batch_token);
        }
        
        
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'GET',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1EmployeeRole[]'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1EmployeeRole[]', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1EmployeeRole[]', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * listEmployees
     *
     * Provides summary information for all of a business's employees.
     *
     * @param string $order The order in which employees are listed in the response, based on their created_at field.      Default value: ASC (optional)
     * @param string $begin_updated_at If filtering results by their updated_at field, the beginning of the requested reporting period, in ISO 8601 format (optional)
     * @param string $end_updated_at If filtering results by there updated_at field, the end of the requested reporting period, in ISO 8601 format. (optional)
     * @param string $begin_created_at If filtering results by their created_at field, the beginning of the requested reporting period, in ISO 8601 format. (optional)
     * @param string $end_created_at If filtering results by their created_at field, the end of the requested reporting period, in ISO 8601 format. (optional)
     * @param string $status If provided, the endpoint returns only employee entities with the specified status (ACTIVE or INACTIVE). (optional)
     * @param string $external_id If provided, the endpoint returns only employee entities with the specified external_id. (optional)
     * @param int $limit The maximum integer number of employee entities to return in a single response. Default 100, maximum 200. (optional)
     * @param string $batch_token A pagination cursor to retrieve the next set of results for your original query to the endpoint. (optional)
     * @return \SquareConnect\Model\V1Employee[]
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function listEmployees($order = null, $begin_updated_at = null, $end_updated_at = null, $begin_created_at = null, $end_created_at = null, $status = null, $external_id = null, $limit = null, $batch_token = null)
    {
        list($response, $statusCode, $httpHeader) = $this->listEmployeesWithHttpInfo ($order, $begin_updated_at, $end_updated_at, $begin_created_at, $end_created_at, $status, $external_id, $limit, $batch_token);
        return $response; 
    }


    /**
     * listEmployeesWithHttpInfo
     *
     * Provides summary information for all of a business's employees.
     *
     * @param string $order The order in which employees are listed in the response, based on their created_at field.      Default value: ASC (optional)
     * @param string $begin_updated_at If filtering results by their updated_at field, the beginning of the requested reporting period, in ISO 8601 format (optional)
     * @param string $end_updated_at If filtering results by there updated_at field, the end of the requested reporting period, in ISO 8601 format. (optional)
     * @param string $begin_created_at If filtering results by their created_at field, the beginning of the requested reporting period, in ISO 8601 format. (optional)
     * @param string $end_created_at If filtering results by their created_at field, the end of the requested reporting period, in ISO 8601 format. (optional)
     * @param string $status If provided, the endpoint returns only employee entities with the specified status (ACTIVE or INACTIVE). (optional)
     * @param string $external_id If provided, the endpoint returns only employee entities with the specified external_id. (optional)
     * @param int $limit The maximum integer number of employee entities to return in a single response. Default 100, maximum 200. (optional)
     * @param string $batch_token A pagination cursor to retrieve the next set of results for your original query to the endpoint. (optional)
     * @return Array of \SquareConnect\Model\V1Employee[], HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function listEmployeesWithHttpInfo($order = null, $begin_updated_at = null, $end_updated_at = null, $begin_created_at = null, $end_created_at = null, $status = null, $external_id = null, $limit = null, $batch_token = null)
    {
        
  
        // parse inputs
        $resourcePath = "/v1/me/employees";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        // query params
        if ($order !== null) {
            $queryParams['order'] = $this->apiClient->getSerializer()->toQueryValue($order);
        }// query params
        if ($begin_updated_at !== null) {
            $queryParams['begin_updated_at'] = $this->apiClient->getSerializer()->toQueryValue($begin_updated_at);
        }// query params
        if ($end_updated_at !== null) {
            $queryParams['end_updated_at'] = $this->apiClient->getSerializer()->toQueryValue($end_updated_at);
        }// query params
        if ($begin_created_at !== null) {
            $queryParams['begin_created_at'] = $this->apiClient->getSerializer()->toQueryValue($begin_created_at);
        }// query params
        if ($end_created_at !== null) {
            $queryParams['end_created_at'] = $this->apiClient->getSerializer()->toQueryValue($end_created_at);
        }// query params
        if ($status !== null) {
            $queryParams['status'] = $this->apiClient->getSerializer()->toQueryValue($status);
        }// query params
        if ($external_id !== null) {
            $queryParams['external_id'] = $this->apiClient->getSerializer()->toQueryValue($external_id);
        }// query params
        if ($limit !== null) {
            $queryParams['limit'] = $this->apiClient->getSerializer()->toQueryValue($limit);
        }// query params
        if ($batch_token !== null) {
            $queryParams['batch_token'] = $this->apiClient->getSerializer()->toQueryValue($batch_token);
        }
        
        
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'GET',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Employee[]'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Employee[]', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Employee[]', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * listTimecardEvents
     *
     * Provides summary information for all events associated with a particular timecard.
     *
     * @param string $timecard_id The ID of the timecard to list events for. (required)
     * @return \SquareConnect\Model\V1TimecardEvent[]
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function listTimecardEvents($timecard_id)
    {
        list($response, $statusCode, $httpHeader) = $this->listTimecardEventsWithHttpInfo ($timecard_id);
        return $response; 
    }


    /**
     * listTimecardEventsWithHttpInfo
     *
     * Provides summary information for all events associated with a particular timecard.
     *
     * @param string $timecard_id The ID of the timecard to list events for. (required)
     * @return Array of \SquareConnect\Model\V1TimecardEvent[], HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function listTimecardEventsWithHttpInfo($timecard_id)
    {
        
        // verify the required parameter 'timecard_id' is set
        if ($timecard_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $timecard_id when calling listTimecardEvents');
        }
  
        // parse inputs
        $resourcePath = "/v1/me/timecards/{timecard_id}/events";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($timecard_id !== null) {
            $resourcePath = str_replace(
                "{" . "timecard_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($timecard_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'GET',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1TimecardEvent[]'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1TimecardEvent[]', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1TimecardEvent[]', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * listTimecards
     *
     * Provides summary information for all of a business's employee timecards.
     *
     * @param string $order The order in which timecards are listed in the response, based on their created_at field. (optional)
     * @param string $employee_id If provided, the endpoint returns only timecards for the employee with the specified ID. (optional)
     * @param string $begin_clockin_time If filtering results by their clockin_time field, the beginning of the requested reporting period, in ISO 8601 format. (optional)
     * @param string $end_clockin_time If filtering results by their clockin_time field, the end of the requested reporting period, in ISO 8601 format. (optional)
     * @param string $begin_clockout_time If filtering results by their clockout_time field, the beginning of the requested reporting period, in ISO 8601 format. (optional)
     * @param string $end_clockout_time If filtering results by their clockout_time field, the end of the requested reporting period, in ISO 8601 format. (optional)
     * @param string $begin_updated_at If filtering results by their updated_at field, the beginning of the requested reporting period, in ISO 8601 format. (optional)
     * @param string $end_updated_at If filtering results by their updated_at field, the end of the requested reporting period, in ISO 8601 format. (optional)
     * @param bool $deleted If true, only deleted timecards are returned. If false, only valid timecards are returned.If you don&#39;t provide this parameter, both valid and deleted timecards are returned. (optional)
     * @param int $limit The maximum integer number of employee entities to return in a single response. Default 100, maximum 200. (optional)
     * @param string $batch_token A pagination cursor to retrieve the next set of results for your original query to the endpoint. (optional)
     * @return \SquareConnect\Model\V1Timecard[]
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function listTimecards($order = null, $employee_id = null, $begin_clockin_time = null, $end_clockin_time = null, $begin_clockout_time = null, $end_clockout_time = null, $begin_updated_at = null, $end_updated_at = null, $deleted = null, $limit = null, $batch_token = null)
    {
        list($response, $statusCode, $httpHeader) = $this->listTimecardsWithHttpInfo ($order, $employee_id, $begin_clockin_time, $end_clockin_time, $begin_clockout_time, $end_clockout_time, $begin_updated_at, $end_updated_at, $deleted, $limit, $batch_token);
        return $response; 
    }


    /**
     * listTimecardsWithHttpInfo
     *
     * Provides summary information for all of a business's employee timecards.
     *
     * @param string $order The order in which timecards are listed in the response, based on their created_at field. (optional)
     * @param string $employee_id If provided, the endpoint returns only timecards for the employee with the specified ID. (optional)
     * @param string $begin_clockin_time If filtering results by their clockin_time field, the beginning of the requested reporting period, in ISO 8601 format. (optional)
     * @param string $end_clockin_time If filtering results by their clockin_time field, the end of the requested reporting period, in ISO 8601 format. (optional)
     * @param string $begin_clockout_time If filtering results by their clockout_time field, the beginning of the requested reporting period, in ISO 8601 format. (optional)
     * @param string $end_clockout_time If filtering results by their clockout_time field, the end of the requested reporting period, in ISO 8601 format. (optional)
     * @param string $begin_updated_at If filtering results by their updated_at field, the beginning of the requested reporting period, in ISO 8601 format. (optional)
     * @param string $end_updated_at If filtering results by their updated_at field, the end of the requested reporting period, in ISO 8601 format. (optional)
     * @param bool $deleted If true, only deleted timecards are returned. If false, only valid timecards are returned.If you don&#39;t provide this parameter, both valid and deleted timecards are returned. (optional)
     * @param int $limit The maximum integer number of employee entities to return in a single response. Default 100, maximum 200. (optional)
     * @param string $batch_token A pagination cursor to retrieve the next set of results for your original query to the endpoint. (optional)
     * @return Array of \SquareConnect\Model\V1Timecard[], HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function listTimecardsWithHttpInfo($order = null, $employee_id = null, $begin_clockin_time = null, $end_clockin_time = null, $begin_clockout_time = null, $end_clockout_time = null, $begin_updated_at = null, $end_updated_at = null, $deleted = null, $limit = null, $batch_token = null)
    {
        
  
        // parse inputs
        $resourcePath = "/v1/me/timecards";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        // query params
        if ($order !== null) {
            $queryParams['order'] = $this->apiClient->getSerializer()->toQueryValue($order);
        }// query params
        if ($employee_id !== null) {
            $queryParams['employee_id'] = $this->apiClient->getSerializer()->toQueryValue($employee_id);
        }// query params
        if ($begin_clockin_time !== null) {
            $queryParams['begin_clockin_time'] = $this->apiClient->getSerializer()->toQueryValue($begin_clockin_time);
        }// query params
        if ($end_clockin_time !== null) {
            $queryParams['end_clockin_time'] = $this->apiClient->getSerializer()->toQueryValue($end_clockin_time);
        }// query params
        if ($begin_clockout_time !== null) {
            $queryParams['begin_clockout_time'] = $this->apiClient->getSerializer()->toQueryValue($begin_clockout_time);
        }// query params
        if ($end_clockout_time !== null) {
            $queryParams['end_clockout_time'] = $this->apiClient->getSerializer()->toQueryValue($end_clockout_time);
        }// query params
        if ($begin_updated_at !== null) {
            $queryParams['begin_updated_at'] = $this->apiClient->getSerializer()->toQueryValue($begin_updated_at);
        }// query params
        if ($end_updated_at !== null) {
            $queryParams['end_updated_at'] = $this->apiClient->getSerializer()->toQueryValue($end_updated_at);
        }// query params
        if ($deleted !== null) {
            $queryParams['deleted'] = $this->apiClient->getSerializer()->toQueryValue($deleted);
        }// query params
        if ($limit !== null) {
            $queryParams['limit'] = $this->apiClient->getSerializer()->toQueryValue($limit);
        }// query params
        if ($batch_token !== null) {
            $queryParams['batch_token'] = $this->apiClient->getSerializer()->toQueryValue($batch_token);
        }
        
        
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'GET',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Timecard[]'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Timecard[]', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Timecard[]', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * retrieveCashDrawerShift
     *
     * Provides the details for a single cash drawer shift, including all events that occurred during the shift.
     *
     * @param string $location_id The ID of the location to list cash drawer shifts for. (required)
     * @param string $shift_id The shift&#39;s ID. (required)
     * @return \SquareConnect\Model\V1CashDrawerShift
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function retrieveCashDrawerShift($location_id, $shift_id)
    {
        list($response, $statusCode, $httpHeader) = $this->retrieveCashDrawerShiftWithHttpInfo ($location_id, $shift_id);
        return $response; 
    }


    /**
     * retrieveCashDrawerShiftWithHttpInfo
     *
     * Provides the details for a single cash drawer shift, including all events that occurred during the shift.
     *
     * @param string $location_id The ID of the location to list cash drawer shifts for. (required)
     * @param string $shift_id The shift&#39;s ID. (required)
     * @return Array of \SquareConnect\Model\V1CashDrawerShift, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function retrieveCashDrawerShiftWithHttpInfo($location_id, $shift_id)
    {
        
        // verify the required parameter 'location_id' is set
        if ($location_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $location_id when calling retrieveCashDrawerShift');
        }
        // verify the required parameter 'shift_id' is set
        if ($shift_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $shift_id when calling retrieveCashDrawerShift');
        }
  
        // parse inputs
        $resourcePath = "/v1/{location_id}/cash-drawer-shifts/{shift_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($location_id !== null) {
            $resourcePath = str_replace(
                "{" . "location_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($location_id),
                $resourcePath
            );
        }// path params
        if ($shift_id !== null) {
            $resourcePath = str_replace(
                "{" . "shift_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($shift_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'GET',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1CashDrawerShift'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1CashDrawerShift', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1CashDrawerShift', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * retrieveEmployee
     *
     * Provides the details for a single employee.
     *
     * @param string $employee_id The employee&#39;s ID. (required)
     * @return \SquareConnect\Model\V1Employee
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function retrieveEmployee($employee_id)
    {
        list($response, $statusCode, $httpHeader) = $this->retrieveEmployeeWithHttpInfo ($employee_id);
        return $response; 
    }


    /**
     * retrieveEmployeeWithHttpInfo
     *
     * Provides the details for a single employee.
     *
     * @param string $employee_id The employee&#39;s ID. (required)
     * @return Array of \SquareConnect\Model\V1Employee, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function retrieveEmployeeWithHttpInfo($employee_id)
    {
        
        // verify the required parameter 'employee_id' is set
        if ($employee_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $employee_id when calling retrieveEmployee');
        }
  
        // parse inputs
        $resourcePath = "/v1/me/employees/{employee_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($employee_id !== null) {
            $resourcePath = str_replace(
                "{" . "employee_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($employee_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'GET',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Employee'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Employee', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Employee', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * retrieveEmployeeRole
     *
     * Provides the details for a single employee role.
     *
     * @param string $role_id The role&#39;s ID. (required)
     * @return \SquareConnect\Model\V1EmployeeRole
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function retrieveEmployeeRole($role_id)
    {
        list($response, $statusCode, $httpHeader) = $this->retrieveEmployeeRoleWithHttpInfo ($role_id);
        return $response; 
    }


    /**
     * retrieveEmployeeRoleWithHttpInfo
     *
     * Provides the details for a single employee role.
     *
     * @param string $role_id The role&#39;s ID. (required)
     * @return Array of \SquareConnect\Model\V1EmployeeRole, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function retrieveEmployeeRoleWithHttpInfo($role_id)
    {
        
        // verify the required parameter 'role_id' is set
        if ($role_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $role_id when calling retrieveEmployeeRole');
        }
  
        // parse inputs
        $resourcePath = "/v1/me/roles/{role_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($role_id !== null) {
            $resourcePath = str_replace(
                "{" . "role_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($role_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'GET',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1EmployeeRole'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1EmployeeRole', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1EmployeeRole', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * retrieveTimecard
     *
     * Provides the details for a single timecard.
     *
     * @param string $timecard_id The timecard&#39;s ID. (required)
     * @return \SquareConnect\Model\V1Timecard
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function retrieveTimecard($timecard_id)
    {
        list($response, $statusCode, $httpHeader) = $this->retrieveTimecardWithHttpInfo ($timecard_id);
        return $response; 
    }


    /**
     * retrieveTimecardWithHttpInfo
     *
     * Provides the details for a single timecard.
     *
     * @param string $timecard_id The timecard&#39;s ID. (required)
     * @return Array of \SquareConnect\Model\V1Timecard, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function retrieveTimecardWithHttpInfo($timecard_id)
    {
        
        // verify the required parameter 'timecard_id' is set
        if ($timecard_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $timecard_id when calling retrieveTimecard');
        }
  
        // parse inputs
        $resourcePath = "/v1/me/timecards/{timecard_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($timecard_id !== null) {
            $resourcePath = str_replace(
                "{" . "timecard_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($timecard_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'GET',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Timecard'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Timecard', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Timecard', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * updateEmployee
     *
     * V1 UpdateEmployee
     *
     * @param string $employee_id The ID of the role to modify. (required)
     * @param \SquareConnect\Model\V1Employee $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return \SquareConnect\Model\V1Employee
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function updateEmployee($employee_id, $body)
    {
        list($response, $statusCode, $httpHeader) = $this->updateEmployeeWithHttpInfo ($employee_id, $body);
        return $response; 
    }


    /**
     * updateEmployeeWithHttpInfo
     *
     * V1 UpdateEmployee
     *
     * @param string $employee_id The ID of the role to modify. (required)
     * @param \SquareConnect\Model\V1Employee $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return Array of \SquareConnect\Model\V1Employee, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function updateEmployeeWithHttpInfo($employee_id, $body)
    {
        
        // verify the required parameter 'employee_id' is set
        if ($employee_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $employee_id when calling updateEmployee');
        }
        // verify the required parameter 'body' is set
        if ($body === null) {
            throw new \InvalidArgumentException('Missing the required parameter $body when calling updateEmployee');
        }
  
        // parse inputs
        $resourcePath = "/v1/me/employees/{employee_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($employee_id !== null) {
            $resourcePath = str_replace(
                "{" . "employee_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($employee_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        // body params
        $_tempBody = null;
        if (isset($body)) {
            $_tempBody = $body;
        }
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'PUT',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Employee'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Employee', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Employee', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * updateEmployeeRole
     *
     * Modifies the details of an employee role.
     *
     * @param string $role_id The ID of the role to modify. (required)
     * @param \SquareConnect\Model\V1EmployeeRole $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return \SquareConnect\Model\V1EmployeeRole
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function updateEmployeeRole($role_id, $body)
    {
        list($response, $statusCode, $httpHeader) = $this->updateEmployeeRoleWithHttpInfo ($role_id, $body);
        return $response; 
    }


    /**
     * updateEmployeeRoleWithHttpInfo
     *
     * Modifies the details of an employee role.
     *
     * @param string $role_id The ID of the role to modify. (required)
     * @param \SquareConnect\Model\V1EmployeeRole $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return Array of \SquareConnect\Model\V1EmployeeRole, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function updateEmployeeRoleWithHttpInfo($role_id, $body)
    {
        
        // verify the required parameter 'role_id' is set
        if ($role_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $role_id when calling updateEmployeeRole');
        }
        // verify the required parameter 'body' is set
        if ($body === null) {
            throw new \InvalidArgumentException('Missing the required parameter $body when calling updateEmployeeRole');
        }
  
        // parse inputs
        $resourcePath = "/v1/me/roles/{role_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($role_id !== null) {
            $resourcePath = str_replace(
                "{" . "role_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($role_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        // body params
        $_tempBody = null;
        if (isset($body)) {
            $_tempBody = $body;
        }
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'PUT',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1EmployeeRole'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1EmployeeRole', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1EmployeeRole', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
    /**
     * updateTimecard
     *
     * Modifies a timecard's details. This creates an API_EDIT event for the timecard. You can view a timecard's event history with the List Timecard Events endpoint.
     *
     * @param string $timecard_id TThe ID of the timecard to modify. (required)
     * @param \SquareConnect\Model\V1Timecard $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return \SquareConnect\Model\V1Timecard
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function updateTimecard($timecard_id, $body)
    {
        list($response, $statusCode, $httpHeader) = $this->updateTimecardWithHttpInfo ($timecard_id, $body);
        return $response; 
    }


    /**
     * updateTimecardWithHttpInfo
     *
     * Modifies a timecard's details. This creates an API_EDIT event for the timecard. You can view a timecard's event history with the List Timecard Events endpoint.
     *
     * @param string $timecard_id TThe ID of the timecard to modify. (required)
     * @param \SquareConnect\Model\V1Timecard $body An object containing the fields to POST for the request.  See the corresponding object definition for field details. (required)
     * @return Array of \SquareConnect\Model\V1Timecard, HTTP status code, HTTP response headers (array of strings)
     * @throws \SquareConnect\ApiException on non-2xx response
     */
    public function updateTimecardWithHttpInfo($timecard_id, $body)
    {
        
        // verify the required parameter 'timecard_id' is set
        if ($timecard_id === null) {
            throw new \InvalidArgumentException('Missing the required parameter $timecard_id when calling updateTimecard');
        }
        // verify the required parameter 'body' is set
        if ($body === null) {
            throw new \InvalidArgumentException('Missing the required parameter $body when calling updateTimecard');
        }
  
        // parse inputs
        $resourcePath = "/v1/me/timecards/{timecard_id}";
        $httpBody = '';
        $queryParams = array();
        $headerParams = array();
        $formParams = array();
        $_header_accept = ApiClient::selectHeaderAccept(array('application/json'));
        if (!is_null($_header_accept)) {
            $headerParams['Accept'] = $_header_accept;
        }
        $headerParams['Content-Type'] = ApiClient::selectHeaderContentType(array('application/json'));

        
        
        // path params
        if ($timecard_id !== null) {
            $resourcePath = str_replace(
                "{" . "timecard_id" . "}",
                $this->apiClient->getSerializer()->toPathValue($timecard_id),
                $resourcePath
            );
        }
        // default format to json
        $resourcePath = str_replace("{format}", "json", $resourcePath);

        
        // body params
        $_tempBody = null;
        if (isset($body)) {
            $_tempBody = $body;
        }
  
        // for model (json/xml)
        if (isset($_tempBody)) {
            $httpBody = $_tempBody; // $_tempBody is the method argument, if present
        } elseif (count($formParams) > 0) {
            $httpBody = $formParams; // for HTTP post (form)
        }
        
        // this endpoint requires OAuth (access token)
        if (strlen($this->apiClient->getConfig()->getAccessToken()) !== 0) {
            $headerParams['Authorization'] = 'Bearer ' . $this->apiClient->getConfig()->getAccessToken();
        }
        // make the API Call
        try {
            list($response, $statusCode, $httpHeader) = $this->apiClient->callApi(
                $resourcePath, 'PUT',
                $queryParams, $httpBody,
                $headerParams, '\SquareConnect\Model\V1Timecard'
            );
            if (!$response) {
                return array(null, $statusCode, $httpHeader);
            }

            return array(\SquareConnect\ObjectSerializer::deserialize($response, '\SquareConnect\Model\V1Timecard', $httpHeader), $statusCode, $httpHeader);
                    } catch (ApiException $e) {
            switch ($e->getCode()) { 
            case 200:
                $data = \SquareConnect\ObjectSerializer::deserialize($e->getResponseBody(), '\SquareConnect\Model\V1Timecard', $e->getResponseHeaders());
                $e->setResponseObject($data);
                break;
            }
  
            throw $e;
        }
    }
}
