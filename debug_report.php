<?php
// Debug script untuk melihat data report
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Debug Report Data</h2>";

// Database connection
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'viera_kasir';

try {
    $connection = new mysqli($host, $username, $password, $database);
    
    if ($connection->connect_error) {
        echo "<p style='color: red;'>Database connection failed: " . $connection->connect_error . "</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✓ Database connected successfully</p>";
    
    // Test 1: Check customer_order table
    echo "<h3>1. Customer Orders (order_status = 4)</h3>";
    $query1 = "SELECT COUNT(*) as count FROM customer_order WHERE order_status = 4";
    $result1 = $connection->query($query1);
    if ($result1) {
        $row1 = $result1->fetch_assoc();
        echo "<p>Total completed orders: <strong>" . $row1['count'] . "</strong></p>";
    }
    
    // Test 2: Check recent orders
    echo "<h3>2. Recent Orders (Last 30 days)</h3>";
    $query2 = "SELECT order_id, order_date, totalamount FROM customer_order WHERE order_status = 4 AND order_date >= DATE_SUB(NOW(), INTERVAL 30 DAY) ORDER BY order_date DESC LIMIT 10";
    $result2 = $connection->query($query2);
    if ($result2 && $result2->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Order ID</th><th>Date</th><th>Amount</th></tr>";
        while ($row2 = $result2->fetch_assoc()) {
            echo "<tr><td>" . $row2['order_id'] . "</td><td>" . $row2['order_date'] . "</td><td>" . $row2['totalamount'] . "</td></tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>No recent orders found</p>";
    }
    
    // Test 3: Check order_menu table
    echo "<h3>3. Order Menu Items</h3>";
    $query3 = "SELECT COUNT(*) as count FROM order_menu om 
               JOIN customer_order co ON om.order_id = co.order_id 
               WHERE co.order_status = 4";
    $result3 = $connection->query($query3);
    if ($result3) {
        $row3 = $result3->fetch_assoc();
        echo "<p>Total menu items in completed orders: <strong>" . $row3['count'] . "</strong></p>";
    }
    
    // Test 4: Check categories
    echo "<h3>4. Categories</h3>";
    $query4 = "SELECT CategoryID, Name FROM item_category ORDER BY Name";
    $result4 = $connection->query($query4);
    if ($result4 && $result4->num_rows > 0) {
        echo "<ul>";
        while ($row4 = $result4->fetch_assoc()) {
            echo "<li>ID: " . $row4['CategoryID'] . " - " . $row4['Name'] . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>No categories found</p>";
    }
    
    // Test 5: Check items with categories
    echo "<h3>5. Items Report Query Test</h3>";
    $start_date = '2025-07-01';
    $end_date = '2025-07-31';
    
    $query5 = "SELECT a.order_id FROM customer_order a 
               WHERE a.order_date >= '$start_date' 
               AND a.order_date <= '$end_date' 
               AND a.order_status = 4 
               ORDER BY a.order_date DESC";
    $result5 = $connection->query($query5);
    if ($result5) {
        echo "<p>Orders in date range ($start_date to $end_date): <strong>" . $result5->num_rows . "</strong></p>";
        if ($result5->num_rows > 0) {
            $order_ids = [];
            while ($row5 = $result5->fetch_assoc()) {
                $order_ids[] = $row5['order_id'];
            }
            echo "<p>Order IDs: " . implode(', ', array_slice($order_ids, 0, 10)) . (count($order_ids) > 10 ? '...' : '') . "</p>";
            
            // Test order_items query
            if (!empty($order_ids)) {
                $order_ids_str = "'" . implode("','", array_slice($order_ids, 0, 5)) . "'";
                $query6 = "SELECT om.menu_id, om.menuqty, if.ProductName, ic.Name as category_name
                          FROM order_menu om
                          LEFT JOIN item_foods if ON om.menu_id = if.ProductsID
                          LEFT JOIN item_category ic ON if.CategoryID = ic.CategoryID
                          WHERE om.order_id IN ($order_ids_str)
                          AND om.allfoodready IS NOT NULL
                          LIMIT 10";
                $result6 = $connection->query($query6);
                if ($result6) {
                    echo "<h4>Sample Menu Items:</h4>";
                    if ($result6->num_rows > 0) {
                        echo "<table border='1' style='border-collapse: collapse;'>";
                        echo "<tr><th>Menu ID</th><th>Quantity</th><th>Product Name</th><th>Category</th></tr>";
                        while ($row6 = $result6->fetch_assoc()) {
                            echo "<tr><td>" . $row6['menu_id'] . "</td><td>" . $row6['menuqty'] . "</td><td>" . $row6['ProductName'] . "</td><td>" . $row6['category_name'] . "</td></tr>";
                        }
                        echo "</table>";
                    } else {
                        echo "<p style='color: red;'>No menu items found for these orders</p>";
                    }
                }
            }
        }
    }
    
    $connection->close();
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
