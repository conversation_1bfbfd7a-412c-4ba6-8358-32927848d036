# Transaction

## Properties
Name | Getter | Setter | Type | Description | Notes
------------ | ------------- | ------------- | ------------- | ------------- | -------------
**id** | getId() | setId($value) | **string** | The transaction&#39;s unique ID, issued by Square payments servers. | [optional] 
**location_id** | getLocationId() | setLocationId($value) | **string** | The ID of the transaction&#39;s associated location. | [optional] 
**created_at** | getCreatedAt() | setCreatedAt($value) | **string** | The time when the transaction was created, in RFC 3339 format. | [optional] 
**tenders** | getTenders() | setTenders($value) | [**\SquareConnect\Model\Tender[]**](Tender.md) | The tenders used to pay in the transaction. | [optional] 
**refunds** | getRefunds() | setRefunds($value) | [**\SquareConnect\Model\Refund[]**](Refund.md) | Refunds that have been applied to any tender in the transaction. | [optional] 
**reference_id** | getReferenceId() | setReferenceId($value) | **string** | If the transaction was created with the [Charge](#endpoint-charge) endpoint, this value is the same as the value provided for the &#x60;reference_id&#x60; parameter in the request to that endpoint. Otherwise, it is not set. | [optional] 
**product** | getProduct() | setProduct($value) | **string** | The Square product that processed the transaction. | [optional] 
**client_id** | getClientId() | setClientId($value) | **string** | If the transaction was created in the Square Point of Sale app, this value is the ID generated for the transaction by Square Point of Sale.  This ID has no relationship to the transaction&#39;s canonical &#x60;id&#x60;, which is generated by Square&#39;s backend servers. This value is generated for bookkeeping purposes, in case the transaction cannot immediately be completed (for example, if the transaction is processed in offline mode).  It is not currently possible with the Connect API to perform a transaction lookup by this value. | [optional] 
**shipping_address** | getShippingAddress() | setShippingAddress($value) | [**\SquareConnect\Model\Address**](Address.md) | The shipping address provided in the request, if any. | [optional] 
**order_id** | getOrderId() | setOrderId($value) | **string** | The order_id is an identifier for the order associated with this transaction, if any. | [optional] 

Note: All properties are protected and only accessed via getters and setters.

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)

