<style type="text/css">
<!--
div.special { margin: auto; width:95%; border:1px solid #000000; padding: 2px; }
div.special table { width:100%; border:1px solid #000000; font-size:10px; border-collapse:collapse; }
.topLeftRight     { border-top:1px solid #000; border-left:1px solid #000; border-right:1px solid #000;}
.topLeftBottom    { border-top:1px solid #000; border-left:1px solid #000; border-bottom:1px solid #000; }
.topLeft          { border-top:1px solid #000; border-left:1px solid #000; }
.bottomLeft       { border-bottom:1px solid #000; border-left:1px solid #000; }
.topRight         { border-top:1px solid #000; border-right:1px solid #000; }
.bottomRight      { border-bottom:1px solid #000; border-right:1px solid #000; }
.topRightBottom   { border-top:1px solid #000; border-bottom:1px solid #000; border-right:1px solid #000; }
-->
</style>
<page style="font-size: 16px" >
    Vous pouvez choisir le format et l'orientation de votre document, en utilisant ceci :<br>
    <br>
    &lt;page orientation="portrait" format="A5" &gt; <i>A5 en portrait</i> &lt;/page&gt; <br>
    <br>
    &lt;page orientation="paysage" format="100x200" &gt; <i>100mm x 200mm en paysage</i> &lt;/page&gt;<br>
    <br>
    En voici un petit exemple !
</page>
<page orientation="paysage" style="font-size: 18px">
    Ceci est une page en paysage<br>
    <table style="width: 100%; border: solid 1px #FFFFFF;">
        <tr>
            <td style="width: 30%; border: solid 1px #FF0000;">AAA</td>
            <td style="width: 40%; border: solid 1px #00FF00;">BBB</td>
            <td style="width: 30%; border: solid 1px #0000FF;">CCC</td>
        </tr>
        <tr>
            <td style="width: 30%; border: solid 1px #FF0000;">AAA</td>
            <td style="width: 40%; border: solid 1px #00FF00;">BBB</td>
            <td style="width: 30%; border: solid 1px #0000FF;">CCC</td>
        </tr>
        <tr>
            <td style="width: 30%; border: solid 1px #FF0000;">AAA</td>
            <td style="width: 40%; border: solid 1px #00FF00;">BBB</td>
            <td style="width: 30%; border: solid 1px #0000FF;">CCC</td>
        </tr>
    </table>
    <div style="width: 70%; border: solid 1mm #770000; margin: 1mm; padding: 2mm; font-size: 4mm; line-height: normal; text-align: justify">
        <img src="./res/logo.gif" alt="logo html2pdf" style="float: left; width: 60mm; margin: 2mm;">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed elementum, nibh eu ultricies scelerisque, est lorem dignissim elit, quis tempus tortor eros non ipsum. Mauris convallis augue ac sapien. In scelerisque dignissim elit. Donec consequat semper lectus. Sed in quam. Nunc molestie hendrerit ipsum. Curabitur elit risus, rhoncus ut, mattis a, convallis eu, neque. Morbi luctus est sit amet nunc. In nisl. Donec magna libero, aliquet eu, vestibulum ut, mollis sed, felis.
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed elementum, nibh eu ultricies scelerisque, est lorem dignissim elit, quis tempus tortor eros non ipsum. Mauris convallis augue ac sapien. In scelerisque dignissim elit. Donec consequat semper lectus. Sed in quam. Nunc molestie hendrerit ipsum. Curabitur elit risus, rhoncus ut, mattis a, convallis eu, neque. Morbi luctus est sit amet nunc. In nisl. Donec magna libero, aliquet eu, vestibulum ut, mollis sed, felis.
    </div>
    <div style="width: 70%; border: solid 1mm #770000; margin: 1mm; padding: 2mm; font-size: 4mm; line-height: 150%;text-align: right;">
        <img src="./res/logo.gif" alt="logo html2pdf" style="float: right; width: 60mm; margin: 2mm; ">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed elementum, nibh eu ultricies scelerisque, est lorem dignissim elit, quis tempus tortor eros non ipsum. Mauris convallis augue ac sapien. In scelerisque dignissim elit. Donec consequat semper lectus. Sed in quam. Nunc molestie hendrerit ipsum. Curabitur elit risus, rhoncus ut, mattis a, convallis eu, neque. Morbi luctus est sit amet nunc. In nisl. Donec magna libero, aliquet eu, vestibulum ut, mollis sed, felis.
    </div>
    <fieldset style="width: 70%; border: solid 1mm #770000; margin: 1mm; padding: 2mm; padding-top: 0mm; font-size: 4mm; line-height: normal; background: #FFFFFF;">
        <legend style=" background: #FFFFFF; padding: 1; border: solid 1px #440000;">Ceci est un exemple de fieldset</legend>
        <img src="./res/logo.gif" alt="logo html2pdf" style="float: left; width: 60mm; margin: 2mm; ">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed elementum, nibh eu ultricies scelerisque, est lorem dignissim elit, quis tempus tortor eros non ipsum. Mauris convallis augue ac sapien. In scelerisque dignissim elit. Donec consequat semper lectus. Sed in quam. Nunc molestie hendrerit ipsum. Curabitur elit risus, rhoncus ut, mattis a, convallis eu, neque. Morbi luctus est sit amet nunc. In nisl. Donec magna libero, aliquet eu, vestibulum ut, mollis sed, felis.
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed elementum, nibh eu ultricies scelerisque, est lorem dignissim elit, quis tempus tortor eros non ipsum. Mauris convallis augue ac sapien. In scelerisque dignissim elit. Donec consequat semper lectus. Sed in quam. Nunc molestie hendrerit ipsum. Curabitur elit risus, rhoncus ut, mattis a, convallis eu, neque. Morbi luctus est sit amet nunc. In nisl. Donec magna libero, aliquet eu, vestibulum ut, mollis sed, felis.
    </fieldset>
</page>
<page orientation="portrait" format="150x200" style="font-size: 18px">
    Ceci est une page en portrait de 150mm x 200mm<br>
    <table style="width: 100%; border: solid 1px #FFFFFF;">
        <tr>
            <td style="width: 30%; border: solid 1px #FF0000;">AAA</td>
            <td style="width: 40%; border: solid 1px #00FF00;">BBB</td>
            <td style="width: 30%; border: solid 1px #0000FF;">CCC</td>
        </tr>
        <tr>
            <td style="width: 30%; border: solid 1px #FF0000;">AAA</td>
            <td style="width: 40%; border: solid 1px #00FF00;">BBB</td>
            <td style="width: 30%; border: solid 1px #0000FF;">CCC</td>
        </tr>
        <tr>
            <td style="width: 30%; border: solid 1px #FF0000;">AAA</td>
            <td style="width: 40%; border: solid 1px #00FF00;">BBB</td>
            <td style="width: 30%; border: solid 1px #0000FF;">CCC</td>
        </tr>
    </table>
    <br>
    <div class="special">
        <table>
            <tr>
                <td colspan="2" class="topLeftRight" style="width: 100%; text-align:left;border-bottom:1px dashed #000000">blabla blabla</td>
            </tr>
            <tr>
                <td class="bottomLeft" style="width:70%;border-right:1px dashed #000000;text-align:left;">blabla blabla</td>
                <td class="bottomRight" style="width: 30%; text-align:left;vertical-align:top;">Date :<br /> Signature :</td>
            </tr>
        </table>
    </div>
</page>