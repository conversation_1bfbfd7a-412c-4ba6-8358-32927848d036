<?php
/**
 * This file is part of Lcobucci\JWT, a simple library to handle JW<PERSON> and JWS
 *
 * @license http://opensource.org/licenses/BSD-3-Clause BSD-3-Clause
 */

namespace <PERSON><PERSON><PERSON><PERSON>\JWT\Claim;

use <PERSON><PERSON>bucci\JWT\Claim;
use <PERSON>cobucci\JWT\ValidationData;

/**
 * Validatable claim that checks if value is lesser or equals to the given data
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2.0.0
 */
class LesserOrEqualsTo extends Basic implements Claim, Validatable
{
    /**
     * {@inheritdoc}
     */
    public function validate(ValidationData $data)
    {
        if ($data->has($this->getName())) {
            return $this->getValue() <= $data->get($this->getName());
        }

        return true;
    }
}
