<!doctype html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo (!empty($setting->title)?$setting->title:null) ?> :: <?php echo (!empty($title)?$title:null) ?></title>
	<link rel="shortcut icon" href="<?php echo base_url((!empty($setting->favicon)?$setting->favicon:'assets/img/icons/favicon.png')) ?>" type="image/x-icon">
    <!-- Bootstrap -->
    <link href="<?php echo base_url('assets/css/bootstrap.min.css') ?>" rel="stylesheet" type="text/css"/>

    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <link href="<?php echo base_url('assets/css/pe-icon-7-stroke.css') ?>" rel="stylesheet" type="text/css"/>
    <link href="<?php echo base_url('assets/css/custom.min.css') ?>" rel="stylesheet" type="text/css"/>
    <link href="<?php echo base_url('assets/css/extra.css') ?>" rel="stylesheet" type="text/css" />
</head>

<body>
    <div class="login vh100">
        <div class="login-content login-content_bg">

        	<div class="">
                            <!-- alert message -->
                            <?php if ($this->session->flashdata('message') != null) {  ?>
                            <div class="alert alert-info alert-dismissable">
                                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                                <?php echo $this->session->flashdata('message'); ?>
                            </div> 
                            <?php } ?>
                            
                            <?php if ($this->session->flashdata('exception') != null) {  ?>
                            <div class="alert alert-danger alert-dismissable">
                                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                                <?php echo $this->session->flashdata('exception'); ?>
                            </div>
                            <?php } ?>
                            
                            <?php if (validation_errors()) {  ?>
                            <div class="alert alert-danger alert-dismissable">
                                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                                <?php echo validation_errors(); ?>
                            </div>
                            <?php } ?> 
                        </div>
                 <div class="text-center mb-5">
                    <h1 class="mt-0"><?php echo display('login') ?></h1>
                    <div class="text-muted">
                        <?php echo (!empty($setting->title)?$setting->title:null) ?>
                    </div>
            </div>
                <?php echo form_open('login','id="loginForm" novalidate'); ?>            
                    <div class="form-group">
                        <label for="email"><?php echo display('email') ?></label>
                        <input type="text" placeholder="<?php echo display('email') ?>" name="email" id="email" class="form-control fs-15px" autocomplete="off"> 
                    </div>
                    <div class="form-group">
                        <label for="password"><?php echo display('password') ?></label>
                        <input type="password"  placeholder="<?php echo display('password') ?>" name="password" id="password" class="form-control fs-15px" autocomplete="off"> 
                    </div>
                    <div class="form-group" style="display: none">
                        <label class="control-label" for="captcha"><?php echo $captcha_image ?></label>
                        
                        <input type="captcha"  placeholder="<?php echo display('captcha') ?>" name="captcha" id="captcha" class="form-control fs-15px" autocomplete="off"> 
                    </div> 
                    <button type="submit" class="btn btn-success btn-lg btn-block fw-500 mb-3"><?php echo display('login') ?></button>
                </form>
            
            <div id="test-print" style="display: none">
                <script>

                    function BtPrint(prn) {
                        var S = "#Intent;scheme=rawbt;";
                        var P =  "package=ru.a402d.rawbtprinter;end;";
                        var textEncoded = encodeURI(prn);
                        window.location.href = "intent:" + textEncoded + S + P;
                    }

                    function generateReceiptStatic() {
                        let receipt = "";
                        receipt += centerText("PT VIERA ANUGERAH PERTAMA");
                        receipt += "-".repeat(48);
                        receipt += alignText("Total", "Rp. 48.000");
                        receipt += alignText("Tunai", "Rp. 50.000");
                        receipt += alignText("Kembali", "Rp. 2.000");

                        return receipt;
                    }

                    // Function to format item lines (align quantity, price)
                    function formatItem(name, qty, unit, price) {
                        let total = qty * price;
                        return name + "\n  " + qty + " " + unit + " x Rp. " + formatRupiah(price) + "\n";
                    }

                    // Function to align text (left and right)
                    function alignText(label, value) {
                        let totalLength = 48; // Max chars per line for 80mm
                        let spaceCount = totalLength - (label.length + value.length);
                        let spaces = " ".repeat(spaceCount > 0 ? spaceCount : 1);
                        return label + spaces + value + "\n";
                    }

                    function centerText(text) {
                        let totalLength = 48; // Max chars per line for 80mm
                        let padding = Math.floor((totalLength - text.length) / 2);
                        let spaces = " ".repeat(padding > 0 ? padding : 0);
                        return spaces + text + "\n";
                    }

                    // Format number with dot separator
                    function formatRupiah(amount) {
                        return amount.toLocaleString("id-ID"); // Converts to Rp format
                    }

                    // dynamic
                    function generateTextReceipt(params = {}) {
                        // Extract variables from params with defaults
                        const { 
                            dataHeader = {}, 
                            detailTransaksi = [], 
                            detailBayar = [] 
                        } = params;

                        let receipt = "";

                        // Store & Transaction Info
                        receipt += "           Cocodream\n\n";
                        receipt += `${dataHeader.entitas_address || "Alamat Tidak Tersedia"}\n`;
                        receipt += "Kota Pekanbaru, Riau 28289\n";
                        receipt += `Telepon: +62 ${dataHeader.entitas_phone || "-"}\n`;
                        receipt += "-------------------------------\n";
                        receipt += `Invoice: ${dataHeader.no_invoice || "-"}\n`;
                        receipt += `Waktu: ${formatDateTime(dataHeader.transaction_date || new Date())}\n`;
                        receipt += "-------------------------------\n";

                        // Transaction Details
                        detailTransaksi.forEach(item => {
                            receipt += `${item.nama_item || "Item Tidak Diketahui"}\n`;
                            receipt += `  ${item.quantity || 0} ${item.nama_satuan || ""} x Rp. ${thousand_separator(item.subtotal || 0)}\n`;
                        });

                        receipt += "-------------------------------\n";

                        // Payment Details
                        receipt += `Total: Rp. ${thousand_separator(detailBayar[0]?.nominal_awal || 0)}\n`;
                        receipt += `Diskon: Rp. ${thousand_separator(detailBayar[0]?.diskon_tambahan || 0)}\n`;
                        receipt += `Tunai: Rp. ${thousand_separator(detailBayar[0]?.nominal_bayar || 0)}\n`;
                        receipt += `Kembali: Rp. ${thousand_separator(detailBayar[0]?.nominal_kembalian || 0)}\n`;
                        receipt += "-------------------------------\n";

                        // Footer
                        receipt += "Terima kasih atas kunjungan Anda\n";
                        receipt += "Tiktok: cocodream_pku\n";
                        receipt += "Instagram: almeiradeganjelly\n\n";

                        return receipt;
                    }

                    // Call the function
                    function printRawBTStatic() {
                        var receiptText = generateReceiptStatic();
                        BtPrint(receiptText);
                    }

                    // Call the function with error handling
                    function printRawBTParamKosong() {
                        try {
                            var receiptText = generateTextReceipt();
                            if (!receiptText) throw new Error("Receipt text is empty.");
                            BtPrint(receiptText);
                        } catch (error) {
                            alert("Error: " + error.message);
                            console.error(error);
                        }
                    }

                    // Call the function with error handling
                    function printRawBTDynamic() {
                        try {
                            var receiptText = generateTextReceipt({
                                dataHeader: {
                                    entitas_address: "Jl. Sudirman No. 123",
                                    entitas_phone: "8123456789",
                                    no_invoice: "INV-001",
                                    transaction_date: "2025-02-22 14:30:00"
                                },
                                detailTransaksi: [
                                    { nama_item: "Kopi Susu", quantity: 2, nama_satuan: "Cup", subtotal: 25000 },
                                    { nama_item: "Roti Bakar", quantity: 1, nama_satuan: "Porsi", subtotal: 15000 }
                                ],
                                detailBayar: [
                                    { nominal_awal: 40000, diskon_tambahan: 5000, nominal_bayar: 50000, nominal_kembalian: 10000 }
                                ]
                            });

                            if (!receiptText) throw new Error("Receipt text is empty.");
                            BtPrint(receiptText);
                        } catch (error) {
                            alert("Error: " + error.message);
                            console.error(error);
                        }
                    }

                    document.addEventListener("DOMContentLoaded", function () {
                        document.getElementById("printButton").addEventListener("click", printRawBTStatic);
                        document.getElementById("printButton2").addEventListener("click", printRawBTParamKosong);
                        document.getElementById("printButton3").addEventListener("click", printRawBTParamIsi);
                    });
                </script>

                <script>
                    function testWs() {
                        const ws = new WebSocket("ws://localhost:8080");

                        ws.onopen = () => {
                            console.log("✅ Connected to WebSocket Server");

                            const cashDrawerCommand = "\x1B\x70\x00\x19\xFA"; // Cash drawer open command

                            function formatItemList(items, printerWidth = 48, useDoubleWidth = false) {
                                let maxItemLength = useDoubleWidth ? Math.floor(printerWidth / 2) - 7 : printerWidth - 7;
                                let formattedText = "Qty  Item Name                   \n";
                                formattedText += "--------------------------------\n";

                                if (useDoubleWidth) formattedText += "\x1B\x21\x20"; // ESC/POS double width mode

                                items.forEach((item) => {
                                    let qty = item.qty.toString().padEnd(4, " "); // Left-align quantity
                                    let words = item.name.split(" ");
                                    let line = qty; // First line starts with quantity
                                    let remainingText = "";

                                    words.forEach((word) => {
                                        if ((line + " " + word).length > maxItemLength) {
                                            formattedText += line + "\n"; // Print current line
                                            line = "     " + word; // Indent next line (align with items)
                                        } else {
                                            line += (line.trim().length > 0 ? " " : "") + word;
                                        }
                                    });

                                    formattedText += line + "\n"; // Add the last part
                                });

                                return formattedText + "\n"; // Extra spacing
                            }

                            // Example Order Items
                            let orderItems = [
                                { name: "AMERICANO ICE", qty: 2 },
                                { name: "NASI AYAM CRISPY SAMBAL MATAH", qty: 1 },
                                { name: "NASI TELUR CUMI AMBALADO / IJO", qty: 1 },
                                { name: "INDOMIE RAMEN VIE RA PEDASSS", qty: 1 },
                            ];

                            let formattedText = formatItemList(orderItems, 42, true); // 80mm printer, double-width enabled

                            // Send to WebSocket Server
                            ws.send(JSON.stringify({
                                printerIp: "************",
                                printerPort: 9100,
                                text: formattedText, // Append cash drawer command
                                isPrintLogo: false
                            }));
                        };

                        ws.onmessage = (event) => {
                            console.log("📩 Server Response:", event.data);
                            alert("Server Response: " + event.data);
                        };

                        ws.onerror = (error) => {
                            console.error("❌ WebSocket Error:", error);
                            alert("WebSocket error! Please check the server.");
                        };

                        ws.onclose = () => {
                            alert("WebSocket connection closed.");
                        };
                    }

                    function openCashDrawer() {
                        const ws = new WebSocket("ws://localhost:8080");

                        ws.onopen = () => {
                            console.log("✅ Connected to WebSocket Server");
                            ws.send(JSON.stringify({
                                printerIp: "************", // Update with your actual printer IP
                                printerPort: 9100,
                                text: "\x1B\x70\x00\x19\xFA", // Cash drawer open command
                                isPrintLogo: false // No logo, only triggering drawer
                            }));
                        };

                        ws.onmessage = (event) => {
                            console.log("📩 Server Response:", event.data);
                        };

                        ws.onerror = (error) => {
                            console.error("❌ WebSocket Error:", error);
                        };

                        ws.onclose = () => {
                            console.log("🔌 WebSocket connection closed.");
                        };
                    }
                </script>

                <button class="btn btn-info btn-lg btn-block fw-500 mb-3" onClick="testWs()">
                    Test WS
                </button>
                <button onClick="openCashDrawer()" class="btn btn-info btn-lg btn-block fw-500 mb-3">
                    Test Cash Drawer
                </button>

            </div>

            <div id="test-wa" style="display: none">
                <a class="btn btn-warning btn-lg btn-block fw-500 mb-3" id="btn-test-wa">Test WhatsApp</a>
            </div>
            <div id="test-pdf" style="display: none">
                <a class="btn btn-warning btn-lg btn-block fw-500 mb-3" href="<?= base_url('dashboard/auth/test_pdf') ?>" >generate PDF</a>
            </div>
        </div>
    </div>

    <script src="<?php echo base_url('assets/js/jquery-1.12.4.min.js') ?>" type="text/javascript"></script>
    <!-- Bootstrap -->
    <script src="<?php echo base_url('assets/js/bootstrap.min.js') ?>" type="text/javascript"></script>

    <script>
        $('#btn-test-wa').on('click', function () {
            console.log('test wa dihit');
            $('#test-wa').html('Loading...');
            $.ajax({
                url: '<?= base_url('dashboard/auth/testWa') ?>',
                method: 'GET',
                success: function (res) {
                    $('#test-wa').html('<pre>' + JSON.stringify(res, null, 2) + '</pre>');
                },
                error: function (xhr, status, error) {
                    console.log('XHR:', xhr);
                    console.log('Status:', status);
                    console.log('Error:', error);
                    $('#test-wa').html('Error: ' + xhr.status + ' - ' + xhr.responseText);
                }
            });
        });
    </script>

</body>

</html>
