<?php
/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace SquareConnect\Model;

use \ArrayAccess;
/**
 * BatchChangeInventoryRequest Class Doc Comment
 *
 * @category Class
 * @package  SquareConnect
 * <AUTHOR> Inc.
 * @license  http://www.apache.org/licenses/LICENSE-2.0 Apache License v2
 * @link     https://squareup.com/developers
 */
class BatchChangeInventoryRequest implements ArrayAccess
{
    /**
      * Array of property to type mappings. Used for (de)serialization 
      * @var string[]
      */
    static $swaggerTypes = array(
        'idempotency_key' => 'string',
        'changes' => '\SquareConnect\Model\InventoryChange[]',
        'ignore_unchanged_counts' => 'bool'
    );
  
    /** 
      * Array of attributes where the key is the local name, and the value is the original name
      * @var string[] 
      */
    static $attributeMap = array(
        'idempotency_key' => 'idempotency_key',
        'changes' => 'changes',
        'ignore_unchanged_counts' => 'ignore_unchanged_counts'
    );
  
    /**
      * Array of attributes to setter functions (for deserialization of responses)
      * @var string[]
      */
    static $setters = array(
        'idempotency_key' => 'setIdempotencyKey',
        'changes' => 'setChanges',
        'ignore_unchanged_counts' => 'setIgnoreUnchangedCounts'
    );
  
    /**
      * Array of attributes to getter functions (for serialization of requests)
      * @var string[]
      */
    static $getters = array(
        'idempotency_key' => 'getIdempotencyKey',
        'changes' => 'getChanges',
        'ignore_unchanged_counts' => 'getIgnoreUnchangedCounts'
    );
  
    /**
      * $idempotency_key A client-supplied, universally unique identifier (UUID) for the request.  See [Idempotency](/basics/api101/idempotency) in the [API Development 101](/basics/api101/overview) section for more information.
      * @var string
      */
    protected $idempotency_key;
    /**
      * $changes The set of physical counts and inventory adjustments to be made. Changes are applied based on the client-supplied timestamp and may be sent out of order. Max size is 100 changes.
      * @var \SquareConnect\Model\InventoryChange[]
      */
    protected $changes;
    /**
      * $ignore_unchanged_counts Indicates whether the current physical count should be ignored if the quantity is unchanged since the last physical count. Default: `true`.
      * @var bool
      */
    protected $ignore_unchanged_counts;

    /**
     * Constructor
     * @param mixed[] $data Associated array of property value initializing the model
     */
    public function __construct(array $data = null)
    {
        if ($data != null) {
            if (isset($data["idempotency_key"])) {
              $this->idempotency_key = $data["idempotency_key"];
            } else {
              $this->idempotency_key = null;
            }
            if (isset($data["changes"])) {
              $this->changes = $data["changes"];
            } else {
              $this->changes = null;
            }
            if (isset($data["ignore_unchanged_counts"])) {
              $this->ignore_unchanged_counts = $data["ignore_unchanged_counts"];
            } else {
              $this->ignore_unchanged_counts = null;
            }
        }
    }
    /**
     * Gets idempotency_key
     * @return string
     */
    public function getIdempotencyKey()
    {
        return $this->idempotency_key;
    }
  
    /**
     * Sets idempotency_key
     * @param string $idempotency_key A client-supplied, universally unique identifier (UUID) for the request.  See [Idempotency](/basics/api101/idempotency) in the [API Development 101](/basics/api101/overview) section for more information.
     * @return $this
     */
    public function setIdempotencyKey($idempotency_key)
    {
        $this->idempotency_key = $idempotency_key;
        return $this;
    }
    /**
     * Gets changes
     * @return \SquareConnect\Model\InventoryChange[]
     */
    public function getChanges()
    {
        return $this->changes;
    }
  
    /**
     * Sets changes
     * @param \SquareConnect\Model\InventoryChange[] $changes The set of physical counts and inventory adjustments to be made. Changes are applied based on the client-supplied timestamp and may be sent out of order. Max size is 100 changes.
     * @return $this
     */
    public function setChanges($changes)
    {
        $this->changes = $changes;
        return $this;
    }
    /**
     * Gets ignore_unchanged_counts
     * @return bool
     */
    public function getIgnoreUnchangedCounts()
    {
        return $this->ignore_unchanged_counts;
    }
  
    /**
     * Sets ignore_unchanged_counts
     * @param bool $ignore_unchanged_counts Indicates whether the current physical count should be ignored if the quantity is unchanged since the last physical count. Default: `true`.
     * @return $this
     */
    public function setIgnoreUnchangedCounts($ignore_unchanged_counts)
    {
        $this->ignore_unchanged_counts = $ignore_unchanged_counts;
        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     * @param  integer $offset Offset 
     * @return boolean
     */
    public function offsetExists($offset)
    {
        return isset($this->$offset);
    }
  
    /**
     * Gets offset.
     * @param  integer $offset Offset 
     * @return mixed 
     */
    public function offsetGet($offset)
    {
        return $this->$offset;
    }
  
    /**
     * Sets value based on offset.
     * @param  integer $offset Offset 
     * @param  mixed   $value  Value to be set
     * @return void
     */
    public function offsetSet($offset, $value)
    {
        $this->$offset = $value;
    }
  
    /**
     * Unsets offset.
     * @param  integer $offset Offset 
     * @return void
     */
    public function offsetUnset($offset)
    {
        unset($this->$offset);
    }
  
    /**
     * Gets the string presentation of the object
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) {
            return json_encode(\SquareConnect\ObjectSerializer::sanitizeForSerialization($this), JSON_PRETTY_PRINT);
        } else {
            return json_encode(\SquareConnect\ObjectSerializer::sanitizeForSerialization($this));
        }
    }
}
