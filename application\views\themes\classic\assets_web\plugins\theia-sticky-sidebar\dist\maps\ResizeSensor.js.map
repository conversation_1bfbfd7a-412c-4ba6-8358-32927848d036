{"version": 3, "sources": ["ResizeSensor.js"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "../ResizeSensor.js", "sourcesContent": ["/**\n * Copyright Marc <PERSON>. See the LICENSE file at the top-level\n * directory of this distribution and at\n * https://github.com/marcj/css-element-queries/blob/master/LICENSE.\n */\n;\n(function() {\n\n    /**\n     * Class for dimension change detection.\n     *\n     * @param {Element|Element[]|Elements|jQuery} element\n     * @param {Function} callback\n     *\n     * @constructor\n     */\n    var ResizeSensor = function(element, callback) {\n        /**\n         *\n         * @constructor\n         */\n        function EventQueue() {\n            this.q = [];\n            this.add = function(ev) {\n                this.q.push(ev);\n            };\n\n            var i, j;\n            this.call = function() {\n                for (i = 0, j = this.q.length; i < j; i++) {\n                    this.q[i].call();\n                }\n            };\n        }\n\n        /**\n         * @param {HTMLElement} element\n         * @param {String}      prop\n         * @returns {String|Number}\n         */\n        function getComputedStyle(element, prop) {\n            if (element.currentStyle) {\n                return element.currentStyle[prop];\n            } else if (window.getComputedStyle) {\n                return window.getComputedStyle(element, null).getPropertyValue(prop);\n            } else {\n                return element.style[prop];\n            }\n        }\n\n        /**\n         *\n         * @param {HTMLElement} element\n         * @param {Function}    resized\n         */\n        function attachResizeEvent(element, resized) {\n            if (!element.resizedAttached) {\n                element.resizedAttached = new EventQueue();\n                element.resizedAttached.add(resized);\n            } else if (element.resizedAttached) {\n                element.resizedAttached.add(resized);\n                return;\n            }\n\n            element.resizeSensor = document.createElement('div');\n            element.resizeSensor.className = 'resize-sensor';\n            var style = 'position: absolute; left: 0; top: 0; right: 0; bottom: 0; overflow: hidden; z-index: -1; visibility: hidden;';\n            var styleChild = 'position: absolute; left: 0; top: 0; transition: 0s;';\n\n            element.resizeSensor.style.cssText = style;\n            element.resizeSensor.innerHTML =\n                '<div class=\"resize-sensor-expand\" style=\"' + style + '\">' +\n                    '<div style=\"' + styleChild + '\"></div>' +\n                '</div>' +\n                '<div class=\"resize-sensor-shrink\" style=\"' + style + '\">' +\n                    '<div style=\"' + styleChild + ' width: 200%; height: 200%\"></div>' +\n                '</div>';\n            element.appendChild(element.resizeSensor);\n\n            if (!{fixed: 1, absolute: 1}[getComputedStyle(element, 'position')]) {\n                element.style.position = 'relative';\n            }\n\n            var expand = element.resizeSensor.childNodes[0];\n            var expandChild = expand.childNodes[0];\n            var shrink = element.resizeSensor.childNodes[1];\n            var shrinkChild = shrink.childNodes[0];\n\n            var lastWidth, lastHeight;\n\n            var reset = function() {\n                expandChild.style.width = expand.offsetWidth + 10 + 'px';\n                expandChild.style.height = expand.offsetHeight + 10 + 'px';\n                expand.scrollLeft = expand.scrollWidth;\n                expand.scrollTop = expand.scrollHeight;\n                shrink.scrollLeft = shrink.scrollWidth;\n                shrink.scrollTop = shrink.scrollHeight;\n                lastWidth = element.offsetWidth;\n                lastHeight = element.offsetHeight;\n            };\n\n            reset();\n\n            var changed = function() {\n                if (element.resizedAttached) {\n                    element.resizedAttached.call();\n                }\n            };\n\n            var addEvent = function(el, name, cb) {\n                if (el.attachEvent) {\n                    el.attachEvent('on' + name, cb);\n                } else {\n                    el.addEventListener(name, cb);\n                }\n            };\n\n            var onScroll = function() {\n              if (element.offsetWidth != lastWidth || element.offsetHeight != lastHeight) {\n                  changed();\n              }\n              reset();\n            };\n\n            addEvent(expand, 'scroll', onScroll);\n            addEvent(shrink, 'scroll', onScroll);\n        }\n\n        var elementType = Object.prototype.toString.call(element);\n        var isCollectionTyped = ('[object Array]' === elementType\n            || ('[object NodeList]' === elementType)\n            || ('[object HTMLCollection]' === elementType)\n            || ('undefined' !== typeof jQuery && element instanceof jQuery) //jquery\n            || ('undefined' !== typeof Elements && element instanceof Elements) //mootools\n        );\n\n        if (isCollectionTyped) {\n            var i = 0, j = element.length;\n            for (; i < j; i++) {\n                attachResizeEvent(element[i], callback);\n            }\n        } else {\n            attachResizeEvent(element, callback);\n        }\n\n        this.detach = function() {\n            if (isCollectionTyped) {\n                var i = 0, j = element.length;\n                for (; i < j; i++) {\n                    ResizeSensor.detach(element[i]);\n                }\n            } else {\n                ResizeSensor.detach(element);\n            }\n        };\n    };\n\n    ResizeSensor.detach = function(element) {\n        if (element.resizeSensor) {\n            element.removeChild(element.resizeSensor);\n            delete element.resizeSensor;\n            delete element.resizedAttached;\n        }\n    };\n\n    // make available to common module loader\n    if (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\n        module.exports = ResizeSensor;\n    }\n    else {\n        window.ResizeSensor = ResizeSensor;\n    }\n\n})();\n"]}