<?php
/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace SquareConnect\Model;

/**
 * CustomerGroupInfoTest Class Doc Comment
 *
 * @category Class
 * @package  SquareConnect
 * <AUTHOR> Inc.
 * @license  http://www.apache.org/licenses/LICENSE-2.0 Apache Licene v2
 * @link     https://squareup.com/developers
 */
class CustomerGroupInfoTest extends \PHPUnit_Framework_TestCase
{

    /**
     * Setup before running each test case
     */
    public static function setUpBeforeClass() {

    }

    /**
     * Clean up after running each test case
     */
    public static function tearDownAfterClass() {

    }

    /**
     * Test CustomerGroupInfo
     */
    public function testCustomerGroupInfo() {

    }

}
