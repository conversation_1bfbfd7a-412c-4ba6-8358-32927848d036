# V1PaymentItemDetail

## Properties
Name | Getter | Setter | Type | Description | Notes
------------ | ------------- | ------------- | ------------- | ------------- | -------------
**category_name** | getCategoryName() | setCategoryName($value) | **string** | The name of the item&#39;s merchant-defined category, if any. | [optional] 
**sku** | getSku() | setSku($value) | **string** | The item&#39;s merchant-defined SKU, if any. | [optional] 
**item_id** | getItemId() | setItemId($value) | **string** | The unique ID of the item purchased, if any. | [optional] 
**item_variation_id** | getItemVariationId() | setItemVariationId($value) | **string** | The unique ID of the item variation purchased, if any. | [optional] 

Note: All properties are protected and only accessed via getters and setters.

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)

