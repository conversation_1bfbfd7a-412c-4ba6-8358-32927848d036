<?php

namespace Iy<PERSON>pay\Request;

use Iyzipay\JsonBuilder;
use Iyzipay\Request;
use I<PERSON><PERSON>pay\RequestStringBuilder;

class CreateRefundToBalanceRequest extends Request
{
    private $paymentId;
    private $callbackUrl;

    public function getPaymentId()
    {
        return $this->paymentId;
    }

    public function setPaymentId($paymentId)
    {
        $this->paymentId = $paymentId;
    }

    public function getCallbackUrl()
    {
        return $this->callbackUrl;
    }

    public function setCallbackUrl($callbackUrl)
    {
        $this->callbackUrl = $callbackUrl;
    }

    public function getJsonObject()
    {
        return JsonBuilder::fromJsonObject(parent::getJsonObject())
            ->add("paymentId", $this->getPaymentId())
            ->add("callbackUrl", $this->getCallbackUrl())
            ->getObject();
    }

    public function toPKIRequestString()
    {
        return RequestStringBuilder::create()
            ->appendSuper(parent::toPKIRequestString())
            ->append("paymentId", $this->getPaymentId())
            ->append("callbackUrl", $this->getCallbackUrl())
            ->getRequestString();
    }
}