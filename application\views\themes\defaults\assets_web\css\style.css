/*--------------------------------------------------------------------------------------
Theme Name: Hungry
Theme URI: http://bdtask.com
Author Name: BDTask
Author URI: http://bdtask.com
Version: 1.0
----------------------------------------------------------------------------------------
    
    Header
    Newsletter 

----------------------------------------------------------------------------------------*/



body{
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
}
a{
    color: #3a3a3a;
    transition: all 200ms linear 0s;
}
a:hover, a:focus{
    color: #04be51;
    text-decoration: none;
}
h1, h2, h3, h4, h5, h6{
    font-family: 'Montserrat', sans-serif;
    letter-spacing: 0;
    font-weight: 700;
    color: #3a3a3a;
}
p{
    font-size: 14px;
    line-height: 20px;
    margin-bottom: 5px;
    color: #424242;
    font-weight: 500;
}
/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}
.btn-link:hover, .btn-link:focus {
    text-decoration: none;
}
.preloader {
    position: fixed;
    left: 0px;
    top: 0px;
    width: 100%;
    height: 100%;
    z-index: 9999;
    background-color: #ffffff;
    background-position: center center;
    background-repeat: no-repeat;
    background-image: url(../images/loader.gif); 
}
.sect_pad {
    padding: 100px 0;
}
.sect_pad2 {
    padding: 100px 0 50px;
}
.sect_pad3 {
    padding: 100px 0 0;
}
.sect_title {
    margin-bottom: 30px;
}
.sect_title h1,
.sect_title h2,
.sect_title h3,
.sect_title h4{
    text-transform: uppercase;
}
.sect_title .curve_title,
.main_slider .item_caption .curve_title {
    font-family: 'Great Vibes', cursive;
    font-size: 46px;
    font-weight: 400;
    text-transform: capitalize;
}
.sect_title .big_title{
    color: #3a3a3a;
    font-size: 40px;
    font-weight: 700;
}
button:focus, .btn:focus, .form-control:focus{
    outline: none;
    box-shadow: none;
    border: 1px solid #28a745;
}
select{
    line-height: normal;
    border-radius: 0;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url(../../assets_web/images/arrow.svg);
    -webkit-background-size: 9px 6px;
    background-size: 9px 6px;
    background-position: right 0rem center;
    -webkit-background-origin: content-box;
    background-origin: content-box;
    background-repeat: no-repeat;
    max-width: 100%;
    background-color: #fff;
    height: 36px;
    border: 1px solid #dedede;
    width: 100%;
    font-size: 14px;
    color: #999;
    padding: 6px 12px;
}
.sect_title h3 {
    color: #0b0b0b;
    font-size: 57px;
    text-transform: uppercase;
    font-weight: 900;
    letter-spacing: 0;
    font-family: 'Poppins', sans-serif;
}

.sect_title h4 {
    font-size: 15px;
    letter-spacing: 6px;
    position: relative;
    display: inline-block;
    line-height: 20px;
    color: #162334;
    font-family: 'Poppins', sans-serif;
}

.sect_title h4:before{
    content: '';
    position: absolute;
    top: 50%;
    left: -120px;
    transform: translateY(-50%);
    width: 95px;
    height: 3px;
    display: block;
    background: #162334;
}

.sect_title h4:after{
    content: '';
    position: absolute;
    top: 50%;
    right: -120px;
    transform: translateY(-50%);
    width: 95px;
    height: 3px;
    display: block;
    background: #162334;
}

.sect_title p{
    max-width: 550px;
    margin: 0 auto;
}
.title_one{
    font-size: 21px;
    font-weight: 400;
    line-height: 25px;
    display: block;
    margin-bottom: 6px;
}
.simple_btn{
    position: relative;
    display: inline-block;
    font-size: 15px;
    margin-top: 30px;
    padding: 0 30px;
    text-transform: uppercase;
    line-height: 45px;
    border: 2px solid;
    font-weight: 600;
    cursor: pointer;
    background: transparent;
}
.simple_btn:before{
    content: '';
    display: block;
    position: absolute;
    background: #04be51;
    left: 0;
    bottom: 100%;
    width: 100%;
    height: 0;
    transition: all 0.3s;
    z-index: -1;
}
.simple_btn:hover,
.simple_btn:focus{
    color: #fff;
    border: 2px solid #04be51;
}
.simple_btn:hover:before{
    height: 100%;
    bottom: 0;
}
.btn1{
    border: 1px solid #ccc3c3;
    background: transparent;
    color: #0b0b0b;
    padding: 0 10px;
    line-height: 33px;
}
.btn:focus, btn1:focus{
    outline: none;
    box-shadow: none;
    color: #fff;
}
.btn1:focus{
    border: 1px solid #dad;
}
/*--------- Btn Top -----------*/
.cd-top {
    display: inline-block;
    position: fixed;
    bottom: 50px;
    right: 20px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    white-space: nowrap;
    background: #04be51;
    visibility: hidden;
    opacity: 0;
    -webkit-transition: opacity .3s 0s, visibility 0s .3s;
    -moz-transition: opacity .3s 0s, visibility 0s .3s;
    transition: opacity .3s 0s, visibility 0s .3s;
    text-align: center;
    text-decoration: none;
    z-index: 999; 
}
.cd-top:focus {
    text-decoration: none; 
}
.cd-top i {
    color: #fff;
    line-height: 45px;
    display: block;
    font-size: 17px; 
}

.cd-top.cd-is-visible {
    visibility: visible;
    opacity: 1;
    width: 45px;
    height: 45px;
    border-radius: 0;
    bottom: 70px; 
}


.modal{z-index:999999;}
.no-touch .cd-top:hover {
    background-color: #e86256;
    opacity: 1; 
}

@media only screen and (min-width: 1024px) {
    .cd-top {
        height: 45px;
        width: 45px;
        border-radius: 0;
        right: 35px;
        bottom: 100px;
        transition: all 300ms linear 0s; 
    } 
}
.bg_two{
    background: #f9f9f9;
}
.bg_img_area{
    position: relative;
}
.bg_img_left{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: block;
    background: url(../images/background/1.png) no-repeat;
}
.bg_img_left.img_2{
    background: url(../images/background/3.png) no-repeat;
}
.bg_overlay{
    opacity: 0.3;
}
.bg_img_right{
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    display: block;
    background: url(../images/background/2.png) no-repeat 100% 100% scroll;
}

.social-links a {
    width: 35px;
    height: 35px;
    line-height: 35px;
    color: #fff;
    background-color: #1b4e9b;
    margin: 0 2px;
    display: inline-block;
    text-align: center;
    font-size: 17px;
}

.social-links a.tw {
    background-color: #00aeef;
}

.social-links a.gp {
    background-color: #c00;
}

.social-links a.gp {
    background-color: #c00;
}

.social-links a.pr {
    background-color: #c8232c;
}

.search_box{
    position: relative;
    top: -44px;
    z-index: 1;
}
.search_box_inner{
    padding: 25px;
    background: #fff;
    -webkit-box-shadow: 0px 0px 17px 0px rgba(0,0,0,0.15);
    -moz-box-shadow: 0px 0px 17px 0px rgba(0,0,0,0.15);
    box-shadow: 0px 0px 17px 0px rgba(0,0,0,0.15);
}
.search_box_inner .btn{
    border-radius: 0;
    font-size: 15px;
    display: block;
    line-height: 36px;
    padding: 0 14px;
    width: 100%;
}
.search_box_inner .form-control,
.search_box_inner .input-group-text{
    border-radius: 0;
}
.search_box_inner .input-group-text{
    background: #28a745;
    border: 1px solid #28a745;
}
.search_box_inner .form-control::placeholder{
    font-size: 14px;
}
.search_box_inner .form-control:focus{
    outline: none;
    box-shadow: none;
    border: 1px solid #28a745;
}
.search_box_inner .input-group-text i{
    color: #fff;
}

/*--------------------
   Header 
----------------------*/
.header_top_area{
    position: relative;
    width: 100%;
    height: 100%;
}
.header_top {
    position: absolute;
    width: 100%;
    z-index: 999;
    text-transform: uppercase;
    border-bottom: 1px solid #a2a2a2;
}
.header_top .navbar-nav .nav-link {
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    padding: 0 15px;
	transition: auto;
}
.header_top .navbar-nav .nav-link:hover {
    color: #04be51;
}
.header_top .navbar-nav .nav-item.active .nav-link {
    color: #04be51;
}
.header_top.menu_fixed {
    width: 100%;
    display: block;
    position: fixed;
    font-weight: bold;
    background: #1d1d1d;
    -webkit-box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.5);
    -moz-box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.5);
    box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.5);
    border-bottom: 1px solid #1d1d1d;
    transition: all 300ms linear 0s;
    height: auto;
    z-index: 9999;
    color: #fff !important;
    top: 0;
}
@media (min-width: 992px){
    .navbar-expand-lg .navbar-nav .dropdown-menu.search_box{
        position: absolute;
        top: 100%;
        right: 0;
        width: 100%;
        height: 0;
        margin: 0;
        padding: 0;
    }
    .navbar-expand-lg .navbar-nav .dropdown-menu.cart_box{
        position: absolute;
        top: 101%;
        right: 0;
        width: 20%;
        margin: 0;
        margin-left: auto;
        padding: 10px 0;
        text-align: center;
    }
    .navbar-expand-lg .navbar-nav .dropdown-menu {
        position: absolute;
        top: 50px;
        margin: 0;
    }
}
.dropdown-menu{
    font-size: 14px;
    text-transform: capitalize;
}
.dropdown-item:focus, .dropdown-item:hover {
    color: #ffffff;
    text-decoration: none;
    background-color: #04be51;
}

.search_box .card{
    background: #272727;
    border-radius: 0;
    border: 0;
}
.search_box .card .form-control{
    background: #444;
    font-size: 15px;
    border: 0;
    border-radius: 0;
    color: #ececec;
}
.search_box .card .form-control::placeholder{
    color: #b3b3b3;
}
.search_box .card .btn{
    background: #04be51;
    border-radius: 0;
    line-height: 30px;
    border: 1px solid #04be51;
    font-size: 15px;
    color: #ffffff;
}
.cart_box{
    background: #444;
    padding: 15px;
}
.cart_box p{
    color: #fff;
}

.navbar{
    border-bottom: 0;
    padding: .5rem 0;
}
.main_slider .item {
    overflow: hidden;
    position: relative; 
}
.main_slider .item:before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    display: block;
    background: rgba(0, 0, 0, 0.5); 
}
.main_slider .item img {
    max-width: 100%;
}
.main_slider .item .item_caption {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60%;
    z-index: 2;
    text-align: center;
}
.main_slider .item .item_caption h3 {
    color: #fff;
    letter-spacing: 0.5px;     
    animation-duration: .9s;
}
.main_slider .item .item_caption h2 {
    color: #fff;
    font-size: 50px;
    margin: 20px 0 30px;
    font-weight: 800;
    text-transform: uppercase;
    animation-duration: .9s;
    animation-delay: .3s;
}
.main_slider .item .item_caption a {
    display: inline-block;
    font-size: 15px;
    line-height: 40px;
    border: 1px solid #fafafa;
    width: 135px;
    text-align: center;
    color: #fff;
    letter-spacing: 1px;
    animation-duration: .9s;
    animation-delay: .5s;
    text-transform: uppercase;
}
.main_slider .item .item_caption a:hover {
    background: #04be51;
    border: 1px solid #04be51;
    color: #fff;
}
.main_slider .owl-nav {
    position: absolute;
    top: 50%;
    left: 0;
    color: #fff;
    right: 0; 
}
.main_slider .owl-nav .owl-next, 
.main_slider .owl-nav .owl-prev{
    width: 45px;
    height: 70px;
    background: rgba(0,0,0,0.5);
    text-align: center;
    position: absolute;
    display: inline-block;
    -webkit-transition-duration: 0.5s;
    -webkit-transition-timing-function: linear;
    transition: all 0.5s ease 0s;
    opacity: 0;
    border: 0;
}
.main_slider .owl-nav .owl-next:hover, 
.main_slider .owl-nav .owl-prev:hover,
.main_slider .owl-nav .owl-next:focus, 
.main_slider .owl-nav .owl-prev:focus{
    background: rgba(0,0,0,1);
}
.main_slider .owl-nav .owl-prev{
    left: 0;
}
.main_slider .owl-nav .owl-next{
    right: 0;
}
.main_slider:hover .owl-nav .owl-prev{
    left: 30px;
    opacity: 1;
}
.main_slider:hover .owl-nav .owl-next{
    right: 30px;
    opacity: 1;
}
.main_slider .owl-nav .owl-next i, 
.main_slider .owl-nav .owl-prev i{
    display: block;
    line-height: 70px;
    font-size: 21px;
    color: #bbb;
}
.main_slider .owl-nav .owl-next:hover{
    -webkit-box-shadow: -100px 0 0 #04be51 inset;
}
.main_slider .owl-nav .owl-prev:hover{
    -webkit-box-shadow: -100px 0 0 #04bd51 inset
} 
.main_slider .owl-nav .owl-next:hover i, 
.main_slider .owl-nav .owl-prev:hover i,
.main_slider .owl-nav .owl-next:focus i, 
.main_slider .owl-nav .owl-prev:focus i{
    color: #fff;
}
.main_slider .owl-nav .owl-next:hover, 
.main_slider .owl-nav .owl-prev:hover,
.main_slider .owl-nav .owl-next:focus, 
.main_slider .owl-nav .owl-prev:focus{
    border: 0;
}

.owl-nav {
    position: absolute;
    top: calc(50% - 25px);
    left: 0;
    color: #fff;
    right: 0; 
}
.owl-nav .owl-next, 
.owl-nav .owl-prev{
    width: 45px;
    height: 50px;
    text-align: center;
    position: absolute;
    display: inline-block;
    -webkit-transition-duration: 0.3s;
    -webkit-transition-timing-function: linear;
    transition: all 0.3s ease 0s;
    border: 1px solid #ddd;
}

.owl-nav .owl-prev{
    left: -70px;
    transform: translatey(-50%);
}
.owl-nav .owl-next{
    right: -70px;
    transform: translatey(-50%);
}
.owl-nav .owl-next i, 
.owl-nav .owl-prev i{
    display: block;
    font-size: 21px;
    color: #ddd;
    line-height: 50px;
}
.owl-nav .owl-next:hover, 
.owl-nav .owl-prev:hover,
.owl-nav .owl-next:focus, 
.owl-nav .owl-prev:focus{
    background: #04be51;
    border: 1px solid #04be51;
}
.owl-nav .owl-next:hover i, 
.owl-nav .owl-prev:hover i,
.owl-nav .owl-next:focus i, 
.owl-nav .owl-prev:focus i{
    color: #fff;
}

/*----------------------------
    Cart Box
------------------------------*/
.cart-header{
    border-bottom: 1px solid #ddd;
}
.cart-box{
    padding: 15px;
    list-style: none;
    border: 1px solid #ddd;
}
.cart-content{
    padding: 15px;
}
.cart-content .img-box{
    max-width: 60px;
    margin-right: 10px;
}
.cart-content .delete_box{
    max-width: 20px;
    width: 100%;
    text-align: right;
}
.cart-content .content{
    max-width: calc(100% - 90px);
    width: 100%;
}

/*----------------------------
    Metis Menu
------------------------------*/
#sidebarCollapse {
    border-radius: 0;
    border: 0;
    background: #eefdf1;
    padding: 0 10px;
}
#sidebarCollapse i {
    font-size: 19px;
    margin-left: 0;
    color: #037d71;
    line-height: 35px;
    display: block;
}
@media(min-width: 992px){
    .sidebar-toggle-btn{
        display: none;
    }
}
.sidebar-nav {
    width: 260px;
    position: fixed;
    top: 0;
    left: -100%;
    height: 100%;
    z-index: 999;
    background: #04be51;
    transition: all 0.3s;
    overflow-y: scroll;
    padding: 0 20px 20px;
    transition-duration: 0.5s;
}

.sidebar-nav.active {
    left: 0;
}

#dismiss {
    width: 0;
    height: 0;
    line-height: 35px;
    text-align: center;
    position: absolute;
    top: 5px;
    right: 30px;
    cursor: pointer;
    -webkit-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
}
.metismenu {
    margin-top: 20px;
}
.metismenu > li {
    border-bottom: 1px solid #0fcc5d;
    padding: 12px 0;
}
.metismenu > li > a {
    font-size: 14px;
    display: block;
    padding-right: 20px;
    position: relative;
    -webkit-transition: none;
    transition: none;
    color: #d8ffe8;
    font-weight: 500;
}
.metismenu > li > a:hover,
.metismenu > li.active > a,
.metismenu > li > ul > li > a:hover,
.metismenu > li > ul > li > ul > li > a:hover,
.metismenu > li > ul > li > ul > li.active > a {
    color: #fff;
}
.metismenu > li > ul {
    padding-left: 15px;
    padding-top: 10px;
    list-style-type: none;
}
.metismenu > li > ul > li {
    padding: 6px 0;
}
.metismenu > li > ul > li > a {
    font-size: 14px;
    display: block;
    position: relative;
    color: #d8ffe8;
    font-weight: 500;
}
.metismenu > li > ul > li.active > a{
    color: #000;
}
.metismenu > li > ul > li > ul {
    list-style-type: none;
    padding-left: 15px;
    padding-top: 10px;
}

.metismenu > li > ul > li > ul > li {
    padding: 6px 0;
}
.metismenu > li > ul > li > ul > li > a {
    font-size: 14px;
    display: block;
    color: #adbdbb;
}
.metismenu .arrow {
    font-size: 16px;
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    right: 0;
}

.metismenu li.active .fa.arrow::before {
    content: "\f107";
}
@media(min-width: 992px){
    .sidebar-nav{
        display: none;
    }
}

/*----------------------------
    Page Banner
------------------------------*/

.page_header {
    padding-top: 83px;
    position: relative;
}

.page_header:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: block;
    background: rgba(0,0,0,0.5);
}

.menu_banner_bg {
    background: url("../images/background/menu-banner.jpg") no-repeat scroll ;
}
.page_header_content h1,
.page_header_content a{
    color: #fff;
}
.page_header_content a:hover{
    color: #04be51;
}
.page_header_content li i{
    color: #fff;
    font-size: 10px;
    margin: 0 12px;
}

/*----------------------------
    ABOUT AREA
------------------------------*/

.about_us .area-title h2 {
    padding-bottom: 10px;
}

.about_us .area-title h2 span {
    color: #212324;
    font-family: lato;
    font-size: 48px;
    text-transform: uppercase;
}
.about_inner .img_part{
    overflow: hidden;
    transition-duration: 0.6s;
}
.about_inner .img_part img{
    transition-duration: 0.5s;
}
.about_inner .img_part:hover{
    box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.5);
}
.about_inner .img_part:hover img{
    transform: scale(1.1);
}

.aboutus_text {
    color: #4a4d4f;
}

.aboutus_text .simple_btn{
    margin-top: 10px;
}

.read-more:hover {
    background: #d0963e;
    color: #fff;
    border-color: #d0963e;
}

/*-----------------------------
    Offer 
-------------------------------*/
.offer_slider .owl-stage-outer{
    padding: 15px;
}
.offer_inner .item{
    background: #fff;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(99, 96, 96, 0.1);
    transition-duration: 0.5s;
}
.offer_inner .item .top_side{
    transform: rotate(-45deg);
    position: absolute;
    top: -14px;
    left: -62px;
    display: inline-block;
    width: 150px;
    padding-top: 28px;
    font-weight: 400;
    font-size: 14px;
    text-align: center;
    line-height: 30px;
    color: #ffffff;
    background: #04be51;
}

.offer_inner .item .img_area{
    overflow: hidden;
}
.offer_inner .item img{
    text-align: center;
    margin: 0 auto;
    transition-duration: 0.5s;
}
.offer_inner .item_content h6{
    margin-bottom: 5px;
    font-weight: 400;
    font-size: 15px;
} 
.offer_inner .item_content .item_name{
    font-size: 19px;
    letter-spacing: 0.7px;
    margin: 15px 0 25px;
    display: block;
    position: relative;
} 
.offer_inner .item_content .item_name:before{
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    display: block;
    width: 55px;
    height: 1px;
    background: #04be51;
    transition: all 200ms linear 0s;
} 

.offer_inner .item:hover{
    box-shadow: 0px 1px 20px 0px rgba(0,0,0,0.1);
}
.offer_inner .item:hover .item_content .item_name:before{
    width: 105px
}
.offer_inner .item_content .simple_btn{
    border: 1px solid #e4e4e4;
    background: transparent;
    padding: 0 15px;
    margin: 0;
    color: #c5c5c5;
    font-size: 13px;
} 
.offer_inner .item:hover .item_content .simple_btn{
    border: 1px solid #04be51;
    color: #04be51;
} 
.offer_inner .item:hover .item_content .simple_btn:hover{
    color: #fff;
}
.offer_inner .item:hover img{
    transform: scale(1.1);
}

.offer_inner .item_content .simple_btn:before{
    z-index: 0;
}
.offer_inner .item_content .simple_btn i,
.offer_inner .item_content .simple_btn span{
    position: relative;
}
.offer_inner .item_content p{
    letter-spacing: 1.3px;
    font-size: 15px;
    margin: 15px 0;
} 
.offer_inner .item_content .number{
    max-width: 70px;
    display: inline-block;
    padding-left: 5px;
    height: 47px;
    border-radius: 0;
    border: 1px solid #e4e4e4;
    vertical-align: middle;
} 
.number:focus{
    outline: none;
    box-shadow: none;
}
.post_slider .item .img_area .hover_item{
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition-duration: 0.5s;
    display: block;
    z-index: 3;
}
.post_slider .item .img_area .hover_item i{
    font-size: 30px;
    color: #fff;
}
.post_slider .item .img_area:hover .hover_item{
    opacity: 1;
}
.post_slider .item .img_area:hover img{
    filter: grayscale(100%);
}
.hover_shadow{
    position: relative;
    display: block;
}
.hover_shadow:before{
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    display: block;
    background: rgba(0,0,0,0.75);
    opacity: 0;
    transition-duration: 0.5s;
    z-index: 2;
}
.hover_shadow:hover:before{
    opacity: 1;
}

/*--------------------------
    RESERVATION AREA
---------------------------*/

.main-reservaton-form {
    background: #fff;
    padding: 35px;
}

.main-reservaton-form input {
    padding: 8px 40px 8px 15px;
    position: relative;
    width: 100%;
    margin-bottom: 0;
    -webkit-transition: 0.3s;
    transition: 0.3s;
    background: transparent;
    border: 1px solid #c3c3c3;
}

.main-reservaton-form input:focus {
    border-color: #04be51;
    box-shadow: 0 0 0;
    outline: none;
}

.main-reservaton-form label {
    position: absolute;
    right: 22px;
    line-height: 42px;
    text-align: center;
    top: 0;
    width: 30px;
    z-index: 9;
    margin: 0;
    cursor: pointer;
    font-size: 18px;
}

.main-reservaton-form .simple_btn {
    margin: 0;
    display: block;
    width: 100%;
    line-height: 38px;
    background: #04be51;
    border-color: #04be51;
    color: #fff;
}

.main-reservaton-form .simple_btn:hover {
    background: #06a548;
    border-color: #06a548;
}

.reservation-call-to-action,
.reservation-private-text {
    color: #212324;
    line-height: 2;
}

.reservation-call-to-action h4,
.reservation-private-text h4 {
    font-size: 20px;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.reservation-call-to-action a {
    color: #c1934c;
    font-size: 20px;
}

/*-----------------------------
    Menu AREA
-------------------------------*/
.rating_area .rate-container i{
    color: #04be30;
}

.menu_area .sect_title h1{
    padding-bottom: 30px;
    border-bottom: 1px solid #ddd;
    margin-bottom: 30px;
}
.menu_area .card-header{
    padding: 10px 15px;
}
.menu_area .single_item{
    padding: 15px 0;
    border: 1px solid #ddd;
}
.menu_area .item_details .simple_btn{
    font-size: 13px;
    padding: 0 10px;
    line-height: 30px;
    border: 2px solid #04be30;
    color: #04be30;
}
.menu_area .item_details .simple_btn:hover,
.menu_area .item_details .simple_btn:focus{
    border: 2px solid #04be51;
    color: #fff;
}
.menu_area .item_details .item_title{
    font-size: 15px;
    font-weight: 600;
}
.menu_area .item_info h6{
    font-weight: 500;
    font-size: 14px;
}
.menu_area .sidebar .form-control{
    width: calc(100% - 42px);
    border-radius: 0;
}
.menu_area .sidebar .form-control:focus{
    border: 1px solid #28a745;
}
.menu_area .sidebar .btn-success{
    border-radius: 0;
}
.cart_counter .qty{
    width: 50px;
    border: 1px solid #ddd;
    text-align: center;
    height: 30px;
    vertical-align: top;
    margin: 0 -5px 0 -6px;
}
.cart_counter .qty:focus{
    outline: none;
}
.cart_counter .items-count{
    background: transparent;
    border: 1px solid #ddd;
    line-height: 28px;
    font-size: 10px;
    color: #9c9c9c;
    padding: 0 10px;
    transition-duration: 0.3s;
}
.cart_counter .items-count:hover {
    background: #04be51;
    color: #fff;
    border: 1px solid #04be51;
}
.cart_counter .items-count:focus,
.cart_counter .items-count:focus{
    outline: none;
}
.cart_image{
    width: 120px;
}
.menu_info {
    height: auto;
    width: 70%;
    background: #162334;
    padding: 70px;
}
.menu_info .simple_btn{
    border: 2px solid #fff;
    color: #fff;
    padding: 0 18px;
}
.menu_info .simple_btn:hover{
    border: 2px solid #04be51;
}
.menu_info .simple_btn i{
    margin-right: 5px;
}
.menu_info .simple_btn i,
.menu_info .simple_btn span{
    position: relative;
    z-index: 2;
}
.menu_info .simple_btn:before{
    z-index: 1;
}

.menu_info .h1{
    font-weight: 700;
}
.menu_info .h1,
.menu_info p,
.menu_info h2{
    color: #fff;
}

.menu_info p{
    max-width: 550px;
    margin-bottom: 16px;
}

.menu_info .simple_btn{
    margin-top: 10px;
}

.menu_img {
    background: #fff;
    color: #1a1a1a;
    margin-top: -150px;
    padding: 15px;
    position: absolute;
    right: 0;
    top: 50%;
    width: 38%;
    z-index: 9;
    border: 1px solid #dedede;
}

.menu_img h3 {
    font-size: 30px;
}

.menu_img a.read-more {
    margin-top: 25px;
}

.menu_img a.read-more:hover {
    background: #c1934c none repeat scroll 0 0;
    border-color: #c1934c;
    color: #fff;
}

.menu-discount-offer {
    margin: auto;
}

.menu-discount-offer .owl-dots > div {
    background: #bcbfc1 none repeat scroll 0 0;
    border-radius: 50%;
    display: inline-block;
    height: 15px;
    margin: 0 5px;
    width: 15px;
}
.menu_slider .owl-nav{
    top: 0;
    left: -20px;
}
.menu_slider .owl-nav .owl-prev{
    left: -85px;
    transform: none;
}
.menu_slider .owl-nav .owl-next{
    transform: none;
    left: -40px;
}
.menu_slider .owl-nav .owl-next, 
.menu_slider .owl-nav .owl-prev{
    width: 40px;
    height: 40px;
}

.menu_slider .owl-nav .owl-next i, 
.menu_slider .owl-nav .owl-prev i{
    font-size: 15px;
    line-height: 40px;
}

.menu-discount-offer .owl-dots > div.active {
    background: #1a1a1a none repeat scroll 0 0;
}

/*-----------------------------
    Details
-------------------------------*/

.product-price del{
    font-size: 34px;
    color: #ddd;
    margin-left: 10px; 
}

.rating-block {
    background-color: #fff;
    border: 1px solid #dedede;
    padding: 15px 15px 20px 15px;
}
.rating-point {
    position: relative;
}
.rating-point i {
    font-size: 100px;
    line-height: 97px;
    color: #08be51;
}
.rating-count{
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    margin: 0;
    color: #fff;
}
.rating-quantity, .rating-percent, .user-rating{
    display: inline-block;
}
.rating-quantity{
    width: 35px;
    text-align: right;
    margin-right: 5px;
}
.user-rating{
    width: 45px;
    text-align: right;
}
.progress{
    width: 180px;
    border-radius: 0;
    height: 6px;
}
.progress-bar{
    background: #08be51;
}
.product_review_left,
.product_review_right{
    position: relative;
}
.product_review_left a{
    font-weight: 600;
}
.product_review_left:before{
    content: '';
    position: absolute;
    right: -4px;
    top: 0;
    width: 4px;
    height: 100%;
    display: block;
    background: #08be51;
    z-index: 1;
}
.product_review_right:before{
    content: '';
    position: absolute;
    left: -30px;
    top: 0;
    width: 4px;
    height: 100%;
    display: block;
    background: #e4e4e4;
}
.review-content{
    width: 60%;
}
.review-form{
    font-size: 14px;
    line-height: 20px;
    margin-bottom: 5px;
    color: #424242;
    font-weight: 500;
}
.review-block{
    width: 75%;
}
.review-block .btn-success{
    border-radius: 0;
}
.review-block h6{
    line-height: 30px;
}
.review-meta-inner i,
.review-meta-inner span{
    font-size: 13px;
    color: #8a8888;
}

.rating-area{
    display: block;
    overflow: hidden;
}

.rating {
    float:left;
}

.rating:not(:checked) > input {
    position:absolute;
    top:-9999px;
    clip:rect(0,0,0,0);
}

.rating:not(:checked) > label {
    float:right;
    width:1em;
    padding:0 .1em;
    overflow:hidden;
    white-space:nowrap;
    cursor:pointer;
    font-size:200%;
    line-height:1.2;
    color: #b9b8b8;
    text-shadow: 0.1em 0.1em 0.2em rgba(0,0,0,.05);
}

.rating:not(:checked) > label:before {
    content: '★ ';
}

.rating > input:checked ~ label {
    color: #08be51;
    text-shadow: 1px 1px #04c752;
}

.rating:not(:checked) > label:hover,
.rating:not(:checked) > label:hover ~ label {
    color: gold;
    text-shadow:1px 1px goldenrod, 2px 2px #B57340, .1em .1em .2em rgba(0,0,0,.5);
}

.rating > label:active {
    position:relative;
    top:2px;
    left:2px;
}

.page-link{
    border: 1px solid #ddd;
    color: #3a3a3a;
}

.page-link:hover,
.page-link.active{
    border: 1px solid #04be30;
    color: #ffffff;
    background: #04be30;
}

.table_chart a:focus img{
    filter: grayscale(100%);
}
.table_chart .modal-body label{
    font-size: 12px;
}
.table_chart .modal-body .form-control::placeholder{
    font-size: 12px;
}
.table_chart .modal-footer .btn{
    font-size: 13px;
}
.table-bordered td, .table-bordered th {
    vertical-align: middle;
}
.modal-dialog {
    margin: 100px auto 20px;
}
.modal-open{
    padding-right: 0 !important;
}

.category_choose,
.need_booking,
.sidebar_box{
    background: #f1f1f1;
}

.need_booking a{
    font-size: 14px;
    font-weight: 600;
    color: #04be30;
}
.popover.clockpicker-popover.bottom.clockpicker-align-left{
    border: 2px solid #04be51;
}
.clockpicker-canvas-bg{
    fill: #a5f7c7;
}
.clockpicker-canvas line{
    stroke: #04be51;
}
.clockpicker-canvas-bearing, .clockpicker-canvas-fg{
    fill: #04be51;
}
.clockpicker-tick.active, .clockpicker-tick:hover{
    background-color: #a5f7c7;
}
.text-primary,
.clockpicker-popover .popover-title span {
    color: #04be51 !important;
}
.datepicker table tr td.day.focused, .datepicker table tr td.day:hover {
    background: #04be51;
    color: #fff;
}
.datepicker table tr td.active.active{
    background: #04be51;
}
.datepicker table{
    margin: 10px;
}
.datepicker td, .datepicker th{
    width: 25px;
    height: 25px;
}

.category_choose .panel-body a{
    display: block;
    font-size: 15px;
}

.category_choose .panel-body{
    margin-bottom: 10px;
}

.category_choose .panel-body a i{
    font-size: 10px;
}

.category_choose .panel-body a span{
    margin-left: 8px;
    float: right;
    font-size: 13px;
    font-weight: 600;
}

/*-----------------------------
    Contact Area
-------------------------------*/
.contact_area .simple_btn{
    border: 2px solid #ced4da;
    margin-top: 0;
}

.contact_area .btn{
    font-size: 13px;
}

/*-----------------------------
    Team Area
-------------------------------*/

.member-social-bookmark ul {
    list-style: outside none none;
    margin: 0;
    padding: 0;
    text-align: center;
}

.member-social-bookmark ul li a {
    color: #fff;
    display: inline-block;
    font-size: 18px;
    height: 100%;
    padding-top: 8px;
    width: 100%;
}

.member-social-bookmark ul li a:hover {
    color: #d0963e;
}

.team-member-img {
    position: relative;
    -webkit-transition: all 0.3s ease 0s;
    transition: all 0.3s ease 0s;
    border-radius: 0;
    display: block;
    overflow: hidden;
}

.team-member-img:before,
.team-member-img:after {
    content: '';
    position: absolute;
    bottom: -18px;
    display: block;
    width: 100%;
    height: 48px;
    background: #fff;
}

.team-member-img:before {
    transform: rotate(15deg);
    left: -35%;
}

.team-member-img:after {
    right: -35%;
    transform: rotate(-15deg);
}

.team-member-img img {
    width: 100%;
}

.member-details{
    background: #fff;
    width: 82%;
    margin: 0 auto;
    position: relative;
    top: -50px;
    padding: 25px 20px;
    -webkit-box-shadow: 0px 1px 1px 1px rgba(88, 88, 88, 0.15);
    -moz-box-shadow: 0px 1px 1px 1px rgba(88, 88, 88, 0.15);
    box-shadow: 0px 1px 1px 1px rgba(88, 88, 88, 0.15);
}

.member-details .member_title{
    color: #04be51;
}

.member-details h5:before{
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    height: 3px;
    display: block;
    background: #04be51;
}

/*--------------------
   Newsletter 
----------------------*/

.newsletter_area{
    position: relative;
    background: url(../images/background/newsletter.jpg);
    background-size: cover;
    background-attachment: fixed;
    background-repeat: no-repeat;
}
.newsletter_area:before{
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: block;
    background: rgba(0, 0, 0, 0.85);
}

.newsletter_area h2,
.newsletter_area p{
    color: #fff;
}

.newsletter_area .newsletter-form{
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    display: -o-flex;
    display: flex;
}

.newsletter_area .form-control{
    height: 50px;
    border-radius: 0;
    padding: 0 25px;
    margin: 0 auto;
}

.newsletter_area .btn{
    background: #04be51;
    color: #fff;
    border-radius: 0;
    padding: 0 25px;
    text-transform: capitalize;
}

.newsletter_area .btn:hover{
    background: #06a548;
}

.map_area{
    position: relative;
}

.map_area .simple_btn:before{
    z-index: 0;
}

.map_area .simple_btn span{
    position: relative;
}

.map, .map iframe, .map img{
    width: 100%;
    height: 500px;
}

.office_address{
    position: absolute;
    right: 60px;
    width: 30%;
    height: 100%;
    background: #1a2636;
    padding: 80px 50px;
    border: 20px solid rgba(255,255,255,0.05);
}

.office_address h2,
.office_address a,
.office_address address,
.office_address .simple_btn{
    color: #fff;
}

/*------------------------------
    Cart AREA
-------------------------------*/
.shopping_cart .table thead{
    background: #f1f1f1;
}

.cart-totals h2, .shipping-form h2, .coupon-form h2 {
    font-size: 19px;
    color: #000;
    margin: 0 0 5px;
    font-weight: 700;
}

.shipping-form p, .coupon-form p {
    color: rgba(0, 0, 0, 0.74);
    margin-bottom: 20px;
}

.cart-totals .cart-totals-border {
    border: 1px solid #dedede;
}

.cart-totals .totals-item {
    clear: both;
    width: 100%;
    margin-bottom: 15px;
    border-bottom: 1px solid #ebebeb;
    font-size: 14px;
}

.cart-totals .totals-item:last-child {
    margin-bottom: 0;
    border-bottom: 0;
}

.cart-totals .totals-item label {
    width: 79%;
}

.cart-totals .totals-item .totals-value {
    float: right;
    width: 21%;
    text-align: right;
}

.checkout {
    text-align: center;
    border: 0;
    padding: 12px 25px;
    color: #fff;
    font-size: 13px;
    display: block;
    border-radius: 0;
    width: 100%;
    text-transform: uppercase;
    font-weight: 600;
    background: #08ae51;
}

.checkout:hover{
    background: #059645;
    color: #fff;
}

.calculate-content .form-control{
    border-radius: 0;
    border: 1px solid #dedede;
    font-size: 14px;
    color: #999;
}

.calculate-content .btn{
    font-size: 13px;
    color: #fff;
    background-color: #08ae51;
    border-color: #08ae51;
}

.calculate-content .btn:hover{
    background-color: #0a9246;
    border-color: #0a9246;
}

.shipping-form .form-control::placeholder{
    font-size: 14px;
    color: #999;
}


/*------------------------------
    Checkout AREA
-------------------------------*/

.total-cost tbody tr td:last-child{
    text-align: right;
}
.checkout-box .simple_btn {
    display: block;
    text-align: center;
}
.checkout_area .panel-title a{
    font-size: 13px;
}
.check_order{
    background: #f1f1f1;
    padding: 25px;
}
.checkout_area .panel-heading{
    border-bottom: 1px solid #ddd;
}
.checkout_area .panel-heading .panel-title{
    margin: 0;
    padding: 10px;
}
.checkout_area .panel-heading .panel-title a{
    color: #08be51;
}
.checkout_area .panel-collapse{
    padding: 25px 10px;
}
.checkout_area label,
.checkout_area .form-group span,
.checkout_area .form-control{
    font-size: 14px;
}



/*------------------------------
    FOOTER AREA
-------------------------------*/

.footer-area {
    background: #1d1d1d;
    color: #cacaca;
    letter-spacing: 1px;
    position: relative;
}

.footer-inner{
    padding: 80px 0;
}

.footer_bottom{
    border-top: 1px solid #383739;
}

.footer-area a {
    color: #fafafa;
}

.footer-area p {
    color: #fafafa;
}

.footer-area a:hover {
    color: #04be51;
}

.footer-logo,
.footer-address {
    margin-bottom: 20px;
}

.footer-init{
    max-width: 80%;
    margin-bottom: 25px;
}

.footer_widget h4{
    color: #fff;
    margin: 20px 0 33px;
}

.footer-social-bookmark ul,
.footer-menu ul {
    list-style: outside none none;
    margin: 0;
    padding: 0;
}

.footer-social-bookmark ul li,
.footer-menu ul li {
    display: inline;
}

.footer-social-bookmark ul li a {
    display: inline-block;
    font-size: 18px;
    width: 35px;
    color: #c4c3c3;
}

.footer-social-bookmark a:hover {
    color: #04be51;
}

.footer-menu ul {
    text-align: right;
}

.footer-menu ul li a {
    display: inline-block;
    padding: 0 5px;
    font-size: 14px;
}

.footer-menu {
    padding: 20px 0;
}

.footer-copyright {
    letter-spacing: 1px;
    padding: 20px 0;
}

.footer-copyright p {
    margin-bottom: 0;
}

.header_top .navbar-nav .nav-link .badge {
    padding: 0;
	color: #fff;	
    position: absolute;
    top: 15px;
    display: block;
    font-size: 11px;
    right: -6px;
    background: #28a745;
    border-radius: 50px;
    width: 22px;
    line-height: 22px;
    box-shadow: none;
}
.header_top .navbar-nav .nav-link:hover .badge{
	color: #fff;
}



.checkout_w_2{
    width:100px;
}

.default_w_120{
    width:120px;
}
.addonsitem_new_class{
    border-bottom: 1px solid #e9ecef;
    margin: 15px 0;
}


.cart_padding_15px{
 padding-left:15px;
}
.cart_w_96{
  width: 96%;
}
.cart_w_100{
  width: 100%;
}
.cartlist_display_none{
    display:none;
}
.cartlistqr{
    border-radius: 0;
}
.cartlistqrbtn{
   background:#686868;color:#fff;border-radius: 0;
}
.cartlist_d{
    display:flex; 
    align-items: center;
}
.completecss{
    text-align:center;
     display:none;
}

.details_w_50{
display:inline; width:50%;
}
.details_w_100{
    width:100%;
}
.details_w_80{
    width: 80%
}
.details_w_60{
    width: 60%
}
.details_w_40{
    width: 40%
}
.details_w_20{
    width: 20%
}

.nav_link_in{
  display:inline;color: #fff;
}
.badge_color_in{
    color: #fff; background: #28a745; 
    border-radius: 50px; 
    width: 22px;
    line-height: 22px;
     padding:0;
}
.login_pa_cursor{
    cursor:pointer;
    margin-left:30px; 
    color:#090 !important;
}
.login_pa_cursors{
cursor:pointer; 
color:#FFF !important;    
}

.pop_up_view_bottom{
  margin-bottom:0;
}
.pop_up_view_bottom_15px{
    margin-bottom:15px;
}

.updatecart_pointer{
cursor: pointer;
}

.updatecart_padding_0{
   padding:0;
}
.updatecart_border_none{
border:none;
}
.updatecart_padding_botom{
    padding-bottom: 65px;
}

.rma_display_none{
   display:none;
}
.classic-badge2{color:#04be51; background:#f8f9fa; border-radius: 50px; width: 22px;line-height: 22px; padding:0; position: absolute; top: -2px;right: -20px;}
.badgedisplaynone{display:none !important}
.badgedisplayblock{display:block !important}
.btnposition {position:relative}
