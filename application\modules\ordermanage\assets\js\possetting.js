// JavaScript Document
var editpos = 0;
$(document).ready(function () {
  "use strict";

  if (basicinfo.segment4 != null) {
    swal(
      {
        title: lang.ord_uodate_success,
        text: lang.do_print_token,
        type: "success",
        showCancelButton: true,
        confirmButtonColor: "#28a745",
        confirmButtonText: lang.yes,
        cancelButtonText: lang.no,
        closeOnConfirm: false,
        closeOnCancel: true,
      },
      function (isConfirm) {
        if (isConfirm) {
          // const idOrder = basicinfo.segment4
          // printUpdateOrderItem(idOrder)

          window.location.href =
            basicinfo.baseurl +
            "ordermanage/order/postokengenerate/" +
            basicinfo.segment4 +
            "/1";
        } else {
          window.location.href =
            basicinfo.baseurl + "ordermanage/order/pos_invoice";
        }
      }
    );
  }
});

function leftArrowPressed(id) {
  if (id == "ongoingorder") {
    $("#fhome").trigger("click");
  }
  if (id == "kitchenorder") {
    $("#ongoingorder").trigger("click");
  }
  if (id == "todayonlieorder") {
    $("#kitchenorder").trigger("click");
  }
  if (id == "todayorder") {
    $("#todayonlieorder").trigger("click");
  }
}

function rightArrowPressed(id) {
  if (id == "fhome") {
    $("#ongoingorder").trigger("click");
  }
  if (id == "ongoingorder") {
    $("#kitchenorder").trigger("click");
  }
  if (id == "kitchenorder") {
    $("#todayonlieorder").trigger("click");
  }
  if (id == "todayonlieorder") {
    $("#todayorder").trigger("click");
  }
  if (id == "todayorder") {
  }
}

function topArrowPressed(id) {
  //alert("topArrowPressed" + id);
}

function downArrowPressed(id) {
  //alert("downArrowPressed" + id);
}

document.onkeydown = function (evt) {
  var id = $("li.active a").attr("id");

  evt = evt || window.event;
  switch (evt.keyCode) {
    case 37:
      leftArrowPressed(id);
      break;

    case 38:
      topArrowPressed(id);
      break;

    case 39:
      rightArrowPressed(id);
      break;

    case 40:
      downArrowPressed(id);
      break;
  }
};
$(window).on("load", function () {
  // Run code
  "use strict";

  $(".sidebar-mini").addClass("sidebar-collapse");
  var myurl = basicinfo.baseurl + "ordermanage/order/cashregister";
  var csrf = $("#csrfhashresarvation").val();
  $.ajax({
    type: "GET",
    async: false,
    url: myurl,
    success: function (data) {
      if (data == 1) {
        return false;
      }
      $("#openclosecash").html(data);
      $("#openregister").modal({
        backdrop: "static",
        keyboard: false,
      });
    },
  });
  var filename = basicinfo.baseurl + basicinfo.nofitysound;
  var audio = new Audio(filename);
});
$(document).ready(function () {
  "use strict";
  // select 2 dropdown
  $("select.form-control:not(.dont-select-me)").select2({
    placeholder: lang.sl_option,
    allowClear: true,
  });

  //form validate
  $("#validate").validate();
  $("#add_category").validate();
  $("#customer_name").validate();

  $(".product-list").slimScroll({
    size: "3px",
    height: "345px",
    allowPageScroll: true,
    railVisible: true,
  });

  $(".product-grid").slimScroll({
    size: "3px",
    height: "calc(100vh - 180px)",
    allowPageScroll: true,
    railVisible: true,
  });
  $(".cat-grid").slimScroll({
    size: "3px",
    height: "calc(100vh - 180px)",
    allowPageScroll: true,
    railVisible: true,
  });

  var audio = new Audio(basicinfo.baseurl + "assets/beep-08b.mp3");
});

("use strict");

function getslcategory(carid) {
  var product_name = $("#product_name").val();
  var csrf = $("#csrfhashresarvation").val();
  var category_id = carid;
  var myurl = $("#posurl").val();
  $.ajax({
    type: "post",
    async: false,
    url: myurl,
    data: {
      product_name: product_name,
      category_id: category_id,
      isuptade: 0,
      csrf_test_name: csrf,
    },
    success: function (data) {
      if (data == "420") {
        $("#product_search").html("Product not found !");
      } else {
        $("#product_search").html(data);
      }
    },
    error: function () {
      alert(lang.req_failed);
    },
  });
}
//Product search button js
$("body").on("click", "#search_button", function () {
  var product_name = $("#product_name").val();
  var category_id = $("#category_id").val();
  var csrf = $("#csrfhashresarvation").val();
  var myurl = $("#posurl").val();
  $.ajax({
    type: "post",
    async: false,
    url: myurl,
    data: {
      product_name: product_name,
      category_id: category_id,
      csrf_test_name: csrf,
    },
    success: function (data) {
      if (data == "420") {
        $("#product_search").html("Product not found !");
      } else {
        $("#product_search").html(data);
      }
    },
    error: function () {
      alert(lang.req_failed);
    },
  });
});

//Product search button js
$("body").on("click", ".select_product", function (e) {
  e.preventDefault();

  var panel = $(this);

  var pid = panel.find(".panel-body input[name=select_product_id]").val();
  var sizeid = panel.find(".panel-body input[name=select_product_size]").val();
  var totalvarient = panel
    .find(".panel-body input[name=select_totalvarient]")
    .val();
  var customqty = panel
    .find(".panel-body input[name=select_iscustomeqty]")
    .val();
  var isgroup = panel
    .find(".panel-body input[name=select_product_isgroup]")
    .val();
  var catid = panel.find(".panel-body input[name=select_product_cat]").val();
  var itemname = panel
    .find(".panel-body input[name=select_product_name]")
    .val();
  var varientname = panel
    .find(".panel-body input[name=select_varient_name]")
    .val();
  var qty = 1;
  var price = panel.find(".panel-body input[name=select_product_price]").val();
  var hasaddons = panel.find(".panel-body input[name=select_addons]").val();
  var csrf = $("#csrfhashresarvation").val();

  if (hasaddons == 0 && totalvarient == 1 && customqty == 0) {
    /*check production*/
    var productionsetting = $("#production_setting").val();

    if (productionsetting == 1) {
      var isselected = $("#productionsetting-" + pid + "-" + sizeid).length;

      if (isselected == 1) {
        var checkqty =
          parseInt($("#productionsetting-" + pid + "-" + sizeid).text()) + qty;
      } else {
        var checkqty = qty;
      }

      var checkvalue = checkproduction(pid, sizeid, checkqty);

      if (checkvalue == false) {
        return false;
      }
    }

    /*end checking*/
    var mysound = basicinfo.baseurl + "assets/";
    var audio = ["beep-08b.mp3"];
    new Audio(mysound + audio[0]).play();
    var dataString =
      "pid=" +
      pid +
      "&itemname=" +
      itemname +
      "&varientname=" +
      varientname +
      "&qty=" +
      qty +
      "&price=" +
      price +
      "&catid=" +
      catid +
      "&sizeid=" +
      sizeid +
      "&isgroup=" +
      isgroup +
      "&csrf_test_name=" +
      csrf;
    var myurl = $("#carturl").val();

    $.ajax({
      type: "POST",
      url: myurl,
      data: dataString,
      success: function (data) {
        $("#addfoodlist").html(data);

        var total = $("#grtotal").val();
        var totalitem = $("#totalitem").val();
        $("#item-number").text(totalitem);
        $("#getitemp").val(totalitem);
        var tax = $("#tvat").val() || 0;
        $("#vat").val(tax) || 0;
        var discount = $("#tdiscount").val() || 0;
        var tgtotal = $("#tgtotal").val() || 0;
        $("#calvat").text(tax);
        $("#invoice_discount").val(discount) || 0;
        var sc = $("#sc").val();
        $("#service_charge").val(sc);
        if (basicinfo.isvatinclusive == 1) {
          $("#caltotal").text(tgtotal - tax);
        } else {
          $("#caltotal").text(tgtotal);
        }
        $("#grandtotal").val(tgtotal);
        $("#orggrandTotal").val(tgtotal);
        $("#orginattotal").val(tgtotal);
      },
    });
  } else {
    var geturl = $("#addonexsurl").val();
    var myurl = geturl + "/" + pid;
    console.log({ myurl });
    var dataString =
      "pid=" + pid + "&sid=" + sizeid + "&csrf_test_name=" + csrf;
    $.ajax({
      type: "POST",
      url: geturl,
      data: dataString,
      success: function (data) {
        $(".addonsinfo").html(data);

        $("#edit").modal("show");

        //$('#edit').find('.close').focus();
        var totalitem = $(".totalitem").val() || 0;
        var tax = $("#tvat").val() || 0;
        var discount = $("#tdiscount").val() || 0;
        var tgtotal = $("#tgtotal").val() || 0;
        $("#vat").val(tax);
        $("#calvat").text(tax);
        $("#getitemp").val(totalitem);
        $("#invoice_discount").val(discount);

        if (basicinfo.isvatinclusive == 1) {
          $("#caltotal").text(tgtotal - tax);
        } else {
          $("#caltotal").text(tgtotal);
        }
        $("#grandtotal").val(tgtotal);
        $("#orggrandTotal").val(tgtotal);
        $("#orginattotal").val(tgtotal);
      },
    });
  }
});
$(document).ready(function () {
  "use strict";
  $("#nonthirdparty").show();
  $("#thirdparty").hide();
  $("#delivercom").prop("disabled", true);
  $("#waiter").prop("disabled", false);
  $("#tableid").prop("disabled", false);
  $("#cookingtime").prop("disabled", false);
  $("#cardarea").hide();

  $("#paidamount").on("keyup", function () {
    var maintotalamount = $("#maintotalamount").val();
    var paidamount = $("#paidamount").val();
    var restamount = parseFloat(paidamount) - parseFloat(maintotalamount);
    var changes = restamount.toFixed(2);
    $("#change").val(changes);
  });

  $(".payment_button").click(function () {
    $(".payment_method").toggle();

    //Select Option
    $("select.form-control:not(.dont-select-me)").select2({
      placeholder: lang.sl_option,
      allowClear: true,
    });
  });

  $("#card_typesl").on("change", function () {
    var cardtype = $("#card_typesl").val();

    $("#card_type").val(cardtype);
    if (cardtype == 4) {
      $("#isonline").val(0);
      $("#cardarea").hide();
      $("#assigncard_terminal").val("");
      $("#assignbank").val("");
      $("#assignlastdigit").val("");
    } else if (cardtype == 1) {
      $("#isonline").val(0);
      $("#cardarea").show();
    } else {
      $("#isonline").val(1);
      $("#cardarea").hide();
      $("#assigncard_terminal").val("");
      $("#assignbank").val("");
      $("#assignlastdigit").val("");
    }
  });
  $("#ctypeid").on("change", function () {
    var customertype = $("#ctypeid").val();
    if (customertype == 3) {
      $("#delivercom").prop("disabled", false);
      $("#waiter").prop("disabled", true);
      $("#tableid").prop("disabled", true);
      $("#cookingtime").prop("disabled", true);
      $("#nonthirdparty").hide();
      $("#thirdparty").show();
    } else if (customertype == 4) {
      $("#nonthirdparty").show();
      $("#thirdparty").hide();
      $("#tblsec").hide();
      $("#tblsecp").hide();
      $("#delivercom").prop("disabled", true);
      $("#waiter").prop("disabled", false);
      $("#tableid").prop("disabled", true);
      $("#cookingtime").prop("disabled", true);
    } else if (customertype == 2) {
      $("#nonthirdparty").show();
      $("#tblsecp").hide();
      $("#tblsec").hide();
      $("#thirdparty").hide();
      $("#waiter").prop("disabled", false);
      $("#tableid").prop("disabled", false);
      $("#cookingtime").prop("disabled", false);
      $("#delivercom").prop("disabled", true);
    } else {
      $("#nonthirdparty").show();
      $("#tblsecp").show();
      $("#tblsec").show();
      $("#thirdparty").hide();
      $("#waiter").prop("disabled", false);
      $("#tableid").prop("disabled", false);
      $("#cookingtime").prop("disabled", false);
      $("#delivercom").prop("disabled", true);
    }
  });
  $('[data-toggle="popover"]').popover({
    container: "body",
  });
  /*place order*/
  Mousetrap.bind("shift+p", function () {
    placeorder();
  });
  /*quick order*/
  Mousetrap.bind("shift+q", function () {
    quickorder();
  });
  /*select customer name*/
  Mousetrap.bind("shift+c", function () {
    $("#customer_name").select2("open");
  });

  /*select customer type*/
  Mousetrap.bind("shift+y", function () {
    $("#ctypeid").select2("open");
  });

  /*focus on discount*/
  Mousetrap.bind("shift+d", function () {
    $("#invoice_discount").focus();
    return false;
  });
  /*focus service charge*/
  Mousetrap.bind("shift+r", function () {
    $("#service_charge").focus();
    return false;
  });
  /*go ongoing order tab*/
  Mousetrap.bind("shift+g", function () {
    $(".ongord").trigger("click");
  });
  /*go total order tab*/
  Mousetrap.bind("shift+t", function () {
    $(".torder").trigger("click");
  });
  /*go online order tab*/
  Mousetrap.bind("shift+o", function () {
    $(".comorder").trigger("click");
  });
  /*go new order tab*/
  Mousetrap.bind("shift+n", function () {
    $(".home").trigger("click");
  });

  /*search unique product for cart*/
  Mousetrap.bind("shift+s", function () {
    $("#product_name").select2("open");
  });
  /*select item qty on addons modal*/
  Mousetrap.bind("alt+q", function () {
    $("#itemqty_1").focus();
    return false;
  });
  /*add to cart on addons modal*/
  Mousetrap.bind("shift+a", function () {
    $("#add_to_cart").trigger("click");
  });
  /*edit on going order*/
  Mousetrap.bind("shift+e", function (e) {
    $("[id*=table-]").focus();
  });

  /*table search*/
  Mousetrap.bind("shift+x", function (e) {
    $("input[aria-controls=onprocessing]").focus();
    return false;
  });
  /*table search*/
  Mousetrap.bind("shift+v", function (e) {
    $("input[aria-controls=Onlineorder]").focus();
    return false;
  });
  /*edit on going order*/
  Mousetrap.bind("shift+m", function (e) {
    $("[id*=table-today-]").focus();
  });
  /*select cooking time*/
  Mousetrap.bind("alt+k", function () {
    $("#cookedtime").focus();
    return false;
  });
  /*select waiter*/
  Mousetrap.bind("shift+w", function () {
    $("#waiter").select2("open");
    return false;
  });
  /*select table*/
  Mousetrap.bind("shift+b", function () {
    $("#tableid").select2("open");
    return false;
  });
  /*select uniqe table on going order*/
  Mousetrap.bind("alt+t", function () {
    $("#ongoingtable_name").select2("open");
  });
  /*update srotcut*/
  /*select update order list*/
  Mousetrap.bind("alt+s", function () {
    $("#update_product_name").select2("open");
  });
  /*select customer name*/
  Mousetrap.bind("alt+c", function () {
    $("#customer_name_update").select2("open");
  });

  /*select customer type*/
  Mousetrap.bind("alt+y", function () {
    $("#ctypeid_update").select2("open");
  });
  /*select waiter*/
  Mousetrap.bind("alt+w", function () {
    $("#waiter_update").select2("open");
    return false;
  });
  /*select table*/
  Mousetrap.bind("alt+b", function () {
    $("#tableid_update").select2("open");
    return false;
  });
  /*focus on discount*/
  Mousetrap.bind("alt+d", function () {
    $("#invoice_discount_update").focus();
    return false;
  });
  /*focus service charge*/
  Mousetrap.bind("alt+r", function () {
    $("#service_charge_update").focus();
    return false;
  });
  /*submit  update order*/
  Mousetrap.bind("alt+u", function () {
    $("#update_order_confirm").trigger("click");
  });
  /*end update sort cut*/
  /*quick paid modal*/
  /*select payment type name*/
  Mousetrap.bind("alt+m", function () {
    $(".card_typesl").select2("open");
  });
  /*type paid amount*/
  Mousetrap.bind("alt+a", function () {
    $(".number").focus();
    //window.prevFocus = $('.number');
    return false;
  });
  /*print bill paid amount*/
  Mousetrap.bind("alt+p", function () {
    $("#pay_bill").trigger("click");
  });
  /*print bill paid amount*/
  Mousetrap.bind("alt+x", function () {
    $(".close").trigger("click");
  });

  $(".search-field").select2({
    placeholder: lang.sl_product,
    minimumInputLength: 1,
    ajax: {
      url: "getitemlistdroup",
      dataType: "json",
      delay: 250,
      //data:{csrf_test_name:basicinfo.csrftokeng},
      processResults: function (data) {
        return {
          results: $.map(data, function (item) {
            return {
              text: item.text + "-" + item.variantName,
              id: item.id + "-" + item.variantid,
            };
          }),
        };
      },
      cache: true,
    },
  });

  /*all ongoingorder product as ajax*/
  $(document).on("click", "#ongoingorder", function () {
    var url = "getongoingorder";
    var csrf = $("#csrfhashresarvation").val();
    $.ajax({
      type: "GET",
      url: url,
      data: {
        csrf_test_name: csrf,
      },
      success: function (data) {
        $("#onprocesslist").html(data);
      },
    });
  });
  /*all ongoingorder product as ajax*/
  $(document).on("click", "#kitchenorder", function () {
    var url = "kitchenstatus";
    var csrf = $("#csrfhashresarvation").val();
    $.ajax({
      type: "GET",
      url: url,
      data: {
        csrf_test_name: csrf,
      },
      success: function (data) {
        $("#kitchen").html(data);
      },
    });
  });
  /*all todayorder product as ajax*/
  $(document).on("click", "#todayorder", function () {
    var url = "showtodayorder";
    var csrf = $("#csrfhashresarvation").val();
    $.ajax({
      type: "GET",
      url: url,
      data: {
        csrf_test_name: csrf,
      },
      success: function (data) {
        $("#messages").html(data);
      },
    });
  });
  /*all todayorder product as ajax*/
  $(document).on("click", "#todayonlieorder", function () {
    var url = "showonlineorder";
    var csrf = $("#csrfhashresarvation").val();
    $.ajax({
      type: "GET",
      url: url,
      data: {
        csrf_test_name: csrf,
      },
      success: function (data) {
        $("#settings").html(data);
      },
    });
  });
  /*all todayorder product as ajax*/
  $(document).on("click", "#todayqrorder", function () {
    var url = "showqrorder";
    var csrf = $("#csrfhashresarvation").val();
    $.ajax({
      type: "GET",
      url: url,
      data: {
        csrf_test_name: csrf,
      },
      success: function (data) {
        $("#qrorder").html(data);
      },
    });
  });
});
/*unique table data*/
("use strict");
$(document).on("change", "#ongoingtable_name", function () {
  var id = $(this).children("option:selected").val();
  var url = "getongoingorder" + "/" + id;
  var csrf = $("#csrfhashresarvation").val();
  $.ajax({
    type: "GET",
    url: url,
    data: {
      csrf_test_name: csrf,
    },
    success: function (data) {
      $("#onprocesslist").html(data);
    },
  });
  $("#table-" + id).focus();
});
$(document).on("change", "#ongoingtable_sr", function () {
  var id = $(this).children("option:selected").val();
  var url = "getongoingorder" + "/" + id + "/table";
  var csrf = $("#csrfhashresarvation").val();
  $.ajax({
    type: "GET",
    url: url,
    data: {
      csrf_test_name: csrf,
    },
    success: function (data) {
      $("#onprocesslist").html(data);
    },
  });
  $("#table-" + id).focus();
});
/*select product from list*/
$(document).on("change", "#product_name", function () {
  var tid = $(this).children("option:selected").val();
  var idvid = tid.split("-");
  var id = idvid[0];
  var vid = idvid[1];
  var url = "srcposaddcart" + "/" + id;
  var csrf = $("#csrfhashresarvation").val();
  /*check production*/
  /*please fixt count total counting*/
  var productionsetting = $("#production_setting").val();
  if (productionsetting == 1) {
    var checkqty = 1;
    var checkvalue = checkproduction(id, vid, checkqty);
    if (checkvalue == false) {
      $("#product_name").html("");
      return false;
    }
  }
  /*end checking*/
  $.ajax({
    type: "GET",
    url: url,
    data: {
      csrf_test_name: csrf,
    },
    success: function (data) {
      var myurl = "adonsproductadd" + "/" + id;
      $.ajax({
        type: "GET",
        url: myurl,
        data: {
          csrf_test_name: csrf,
        },
        success: function (data) {
          $(".addonsinfo").html(data);
          $("#edit").modal("show");
          var totalitem = $("#totalitem").val();
          var tax = $("#tvat").val();
          var discount = $("#tdiscount").val();
          var tgtotal = $("#tgtotal").val();
          $("#vat").val(tax);
          $("#calvat").text(tax);
          var sc = $("#sc").val();
          $("#service_charge").val(sc);
          $("#getitemp").val(totalitem);
          $("#invoice_discount").val(discount);
          if (basicinfo.isvatinclusive == 1) {
            $("#caltotal").text(tgtotal - tax);
          } else {
            $("#caltotal").text(tgtotal);
          }
          $("#grandtotal").val(tgtotal);
          $("#orggrandTotal").val(tgtotal);
          $("#orginattotal").val(tgtotal);
          $("#product_name").html("");
        },
      });
    },
  });
});
/*$(document).on("keypress", '#varientinfo', function(e){
    if(e.which == 13){
		$('#itemqty_1').trigger(click);	    
    }
});*/
$(document).on("keypress", "#itemqty_1", function (e) {
  if (e.which == 13) {
    $(".asingle").trigger("click");
  }
});
$("#edit").on("shown.bs.modal", function () {
  $("#varientinfo").focus();
});

function printRawHtml(view) {
  printJS({
    printable: view,
    type: "raw-html",
  });
}

function placeorder_old() {
  var ctypeid = $("#ctypeid").val();
  var waiter = "";
  var isdelivary = "";
  var thirdinvoiceid = "";
  var tableid = "";
  var customer_name = $("#customer_name").val();
  var cardtype = 4;
  var isonline = 0;
  var order_date = $("#order_date").val();
  var grandtotal = $("#grandtotal").val();
  var customernote = "";
  var invoice_discount = $("#invoice_discount").val();
  var service_charge = $("#service_charge").val();
  var vat = $("#vat").val();
  var orggrandTotal = $("#subtotal").val();
  var isonline = $("#isonline").val();
  var isitem = $("#totalitem").val();
  var cookedtime = $("#cookedtime").val();
  var multiplletaxvalue = $("#multiplletaxvalue").val();
  var csrf = $("#csrfhashresarvation").val();
  var errormessage = "";
  if (customer_name == "") {
    errormessage = errormessage + "<span>Please Select Customer Name.</span>";
    alert("Please Select Customer Name!!!");
    return false;
  }
  if (ctypeid == "") {
    errormessage = errormessage + "<span>Please Select Customer Type.</span>";
    alert("Please Select Customer Type!!!");
    return false;
  }
  if (isitem == "" || isitem == 0) {
    errormessage = errormessage + "<span>Please add Some Food</span>";
    alert("Please add Some Food!!!");
    return false;
  }
  if (ctypeid == 3) {
    var isdelivary = $("#delivercom").val();
    var thirdinvoiceid = $("#thirdinvoiceid").val();
    if (isdelivary == "") {
      errormessage = errormessage + "<span>Please Select Customer Type.</span>";
      alert("Please Select Delivar Company!!!");
      return false;
    }
  } else if (ctypeid == 4 || ctypeid == 2) {
    if (possetting.waiter == 1) {
      var waiter = $("#waiter").val();
      if (waiter == "") {
        errormessage = errormessage + "<span>Please Select Waiter.</span>";
        alert("Please Select Waiter!!!");
        return false;
      }
    }
  } else {
    var waiter = $("#waiter").val();
    var tableid = $("#tableid").val();
    var table_member_multi = $("#table_member_multi").val();
    var table_member_multi_person = $("#table_member_multi_person").val();
    var table_member = $("#table_member").val(); //table member 02/11
    if (possetting.waiter == 1) {
      if (waiter == "") {
        errormessage = errormessage + "<span>Please Select Waiter.</span>";
        $("#waiter").select2("open");
        return false;
      }
    }
    if (possetting.tableid == 1) {
      if (tableid == "") {
        $("#tableid").select2("open");
        toastr.warning("Please Select Table", "Warning");
        return false;
      }
      if (possetting.tablemaping == 1) {
        if (tableid == "" || !$.isNumeric($("#table_person").val())) {
          toastr.warning("Please Select Table or number person", "Warning");
          return false;
        }
      }
    }
  }
  if (errormessage == "") {
    order_date = encodeURIComponent(order_date);
    customernote = encodeURIComponent(customernote);
    var errormessage =
      '<span style="color:#060;">Signup Completed Successfully.</span>';
    var dataString =
      "customer_name=" +
      customer_name +
      "&ctypeid=" +
      ctypeid +
      "&waiter=" +
      waiter +
      "&tableid=" +
      tableid +
      "&card_type=" +
      cardtype +
      "&isonline=" +
      isonline +
      "&order_date=" +
      order_date +
      "&grandtotal=" +
      grandtotal +
      "&customernote=" +
      customernote +
      "&invoice_discount=" +
      invoice_discount +
      "&service_charge=" +
      service_charge +
      "&vat=" +
      vat +
      "&subtotal=" +
      orggrandTotal +
      "&assigncard_terminal=&assignbank=&assignlastdigit=&delivercom=" +
      isdelivary +
      "&thirdpartyinvoice=" +
      thirdinvoiceid +
      "&cookedtime=" +
      cookedtime +
      "&tablemember=" +
      table_member +
      "&table_member_multi=" +
      table_member_multi +
      "&table_member_multi_person=" +
      table_member_multi_person +
      "&multiplletaxvalue=" +
      multiplletaxvalue +
      "&csrf_test_name=" +
      csrf;
    $.ajax({
      type: "POST",
      url: basicinfo.baseurl + "ordermanage/order/pos_order",
      data: dataString,
      success: function (data) {
        $("#addfoodlist").empty();
        $("#getitemp").val("0");
        $("#calvat").text("0");
        $("#vat").val("0");
        $("#invoice_discount").val("0");
        $("#caltotal").text("");
        $("#grandtotal").val("");
        $("#thirdinvoiceid").val("");
        $("#orggrandTotal").val("");
        $("#waiter").select2("data", null);
        $("#tableid").select2("data", null);
        $("#waiter").val("");

        $("#table_member").val("");
        $("#table_person").val(lang.person);
        $("#table_member_multi").val(0);
        $("#table_member_multi_person").val(0);

        var err = data;
        if (err == "error") {
          swal(
            {
              title: lang.ord_failed,
              text: lang.failed_msg,
              type: "warning",
              showCancelButton: true,
              confirmButtonColor: "#DD6B55",
              confirmButtonText: lang.yes + ", " + lang.cancel + "!",
              closeOnConfirm: true,
            },
            function () {}
          );
        } else {
          if (basicinfo.printtype == 1) {
            swal(
              {
                title: lang.ord_succ,
                text: "",
                type: "success",
                showCancelButton: false,
                confirmButtonColor: "#28a745",
                confirmButtonText: "Done",
                closeOnConfirm: true,
              },
              function () {}
            );
          } else {
            swal(
              {
                title: lang.ord_succ,
                text: "Do you Want to Print Token No.???",
                type: "success",
                showCancelButton: true,
                confirmButtonColor: "#28a745",
                confirmButtonText: lang.yes,
                cancelButtonText: lang.no,
                closeOnConfirm: true,
                closeOnCancel: true,
              },
              function (isConfirm) {
                if (isConfirm) {
                  // indra
                  printRawHtml(data);
                } else {
                  $("#waiter").select2("data", null);
                  $("#tableid").select2("data", null);
                  $("#waiter").val("");
                  $("#tableid").val("");
                }
              }
            );
          }
        }
      },
    });
  }
}

function postokenprint(id) {
  var csrf = $("#csrfhashresarvation").val();
  var url = "paidtoken" + "/" + id + "/";
  $.ajax({
    type: "POST",
    url: url,
    data: {
      csrf_test_name: csrf,
    },
    success: function (data) {
      printRawHtml(data);
    },
  });
}

function editposorder(id, view) {
  var url = "updateorder" + "/" + id;
  var csrf = $("#csrfhashresarvation").val();
  if (view == 1) {
    editpos = 1;
    var vid = $("#onprocesslist");
  } else if (view == 2) {
    var vid = $("#messages");
  } else if (view == 4) {
    var vid = $("#qrorder");
  } else {
    var vid = $("#settings");
  }

  $.ajax({
    type: "GET",
    url: url,
    data: {
      csrf_test_name: csrf,
    },
    success: function (data) {
      vid.html(data);
      $(".listcat3").on("click", function (event) {
        var spid = $(this).next(".dropcat").attr("id");
        if ($("#" + spid).hasClass("display-none")) {
          $("#" + spid).removeClass("display-none");
          $("#" + spid).addClass("display-block");
        } else {
          $("#" + spid).removeClass("display-block");
          $("#" + spid).addClass("display-none");
        }
      });
    },
  });
}

function quickorder() {
  var ctypeid = $("#ctypeid").val();
  var waiter = "";
  var isdelivary = "";
  var thirdinvoiceid = "";
  var tableid = "";
  var customer_name = $("#customer_name").val();
  var cardtype = 4;
  var isonline = 0;
  var order_date = $("#order_date").val();
  var grandtotal = $("#grandtotal").val();
  var customernote = "";
  var invoice_discount = $("#invoice_discount").val();
  var service_charge = $("#service_charge").val();
  var vat = $("#vat").val();
  var orggrandTotal = $("#subtotal").val();

  var isitem = $("#totalitem").val();
  var cookedtime = $("#cookedtime").val();
  var multiplletaxvalue = $("#multiplletaxvalue").val();
  var csrf = $("#csrfhashresarvation").val();
  var errormessage = "";
  if (customer_name == "") {
    errormessage = errormessage + "<span>Please Select Customer Name.</span>";
    alert("Please Select Customer Name!!!");
    return false;
  }
  if (ctypeid == "") {
    errormessage = errormessage + "<span>Please Select Customer Type.</span>";
    alert("Please Select Customer Type!!!");
    return false;
  }
  if (isitem == "" || isitem == 0) {
    errormessage = errormessage + "<span>Please add Some Food</span>";
    alert("Please add Some Food!!!");
    return false;
  }
  if (ctypeid == 3) {
    var isdelivary = $("#delivercom").val();
    var thirdinvoiceid = $("#thirdinvoiceid").val();
    if (isdelivary == "") {
      errormessage = errormessage + "<span>Please Select Customer Type.</span>";
      alert("Please Select Delivar Company!!!");
      return false;
    }
  } else if (ctypeid == 4 || ctypeid == 2) {
    var waiter = $("#waiter").val();
    if (quickordersetting.waiter == 1) {
      if (waiter == "") {
        errormessage = errormessage + "<span>Please Select Waiter.</span>";
        $("#waiter").select2("open");

        return false;
      }
    }
  } else {
    var waiter = $("#waiter").val();
    var tableid = $("#tableid").val();
    var table_member_multi = $("#table_member_multi").val();
    var table_member_multi_person = $("#table_member_multi_person").val();
    var table_member = $("#table_member").val(); //table member 02/11
    if (quickordersetting.waiter == 1) {
      if (waiter == "") {
        errormessage = errormessage + "<span>Please Select Waiter.</span>";
        $("#waiter").select2("open");
        return false;
      }
    }
    if (quickordersetting.tableid == 1) {
      if (tableid == "") {
        $("#tableid").select2("open");
        toastr.warning("Please Select Table", "Warning");
        return false;
      }
      if (quickordersetting.tablemaping == 1) {
        if (tableid == "" || !$.isNumeric($("#table_person").val())) {
          toastr.warning("Please Select Table or number person", "Warning");
          return false;
        }
      }
    }
  }

  if (errormessage == "") {
    order_date = encodeURIComponent(order_date);
    customernote = encodeURIComponent(customernote);
    var errormessage =
      '<span style="color:#060;">Signup Completed Successfully.</span>';
    var dataString =
      "customer_name=" +
      customer_name +
      "&ctypeid=" +
      ctypeid +
      "&waiter=" +
      waiter +
      "&tableid=" +
      tableid +
      "&card_type=" +
      cardtype +
      "&isonline=" +
      isonline +
      "&order_date=" +
      order_date +
      "&grandtotal=" +
      grandtotal +
      "&customernote=" +
      customernote +
      "&invoice_discount=" +
      invoice_discount +
      "&service_charge=" +
      service_charge +
      "&vat=" +
      vat +
      "&subtotal=" +
      orggrandTotal +
      "&assigncard_terminal=&assignbank=&assignlastdigit=&delivercom=" +
      isdelivary +
      "&thirdpartyinvoice=" +
      thirdinvoiceid +
      "&cookedtime=" +
      cookedtime +
      "&tablemember=" +
      table_member +
      "&table_member_multi=" +
      table_member_multi +
      "&table_member_multi_person=" +
      table_member_multi_person +
      "&multiplletaxvalue=" +
      multiplletaxvalue +
      "&csrf_test_name=" +
      csrf;

    $.ajax({
      type: "POST",
      url: basicinfo.baseurl + "ordermanage/order/pos_order/1",
      data: dataString,
      success: function (data) {
        $("#addfoodlist").empty();
        $("#getitemp").val("0");
        $("#calvat").text("0");
        $("#vat").val("0");
        $("#invoice_discount").val("0");
        $("#caltotal").text("");
        $("#grandtotal").val("");
        $("#thirdinvoiceid").val("");
        $("#orggrandTotal").val("");
        $("#waiter").select2("data", null);
        $("#tableid").select2("data", null);
        $("#waiter").val("");

        $("#table_member").val("");
        $("#table_person").val(lang.person);
        $("#table_member_multi").val(0);
        $("#table_member_multi_person").val(0);
        var err = data;
        if (err == "error") {
          swal(
            {
              title: lang.ord_failed,
              text: lang.failed_msg,
              type: "warning",
              showCancelButton: true,
              confirmButtonColor: "#DD6B55",
              confirmButtonText: lang.yes + ", " + lang.cancel + "!",
              closeOnConfirm: true,
            },
            function () {}
          );
        } else {
          swal(
            {
              title: lang.ord_places,
              text: lang.do_print_in,
              type: "success",
              showCancelButton: true,
              confirmButtonColor: "#28a745",
              confirmButtonText: lang.yes,
              cancelButtonText: lang.no,
              closeOnConfirm: true,
              closeOnCancel: true,
            },
            function (isConfirm) {
              if (isConfirm) {
                createMargeorder(data, 1);
              } else {
                $("#waiter").select2("data", null);
                $("#tableid").select2("data", null);
                $("#waiter").val("");
                $("#tableid").val("");
              }
            }
          );
        }
      },
    });
  }
}

function printJobComplete() {
  $("#kotenpr").empty();
}

function printRawHtmlupdate(view, id) {
  printJS({
    printable: view,
    type: "raw-html",
    onPrintDialogClose: function () {
      $.ajax({
        type: "GET",
        url: "tokenupdate/" + id,
        data: {
          csrf_test_name: csrftokeng,
        },
        success: function (data) {
          console.log("done");
        },
      });
    },
  });
}

function postupdateorder_ajax() {
  var form = $("#insert_purchase");
  var url = form.attr("action");
  var data = form.serialize();

  $.ajax({
    url: url,
    type: "POST",
    data: data,
    dataType: "json",

    beforeSend: function (xhr) {
      $("span.error").html("");
    },

    success: function (result) {
      swal(
        {
          title: result.msg,
          text: "Apakah Ingin Print Struct Update Item ?",
          type: "success",
          showCancelButton: true,
          confirmButtonColor: "#28a745",
          confirmButtonText: lang.yes,
          cancelButtonText: lang.no,
          closeOnConfirm: true,
          closeOnCancel: true,
        },
        function (isConfirm) {
          if (isConfirm) {
            // indra
            const idOrder = result.orderid
            // printUpdateOrderItem(idOrder)

            $.ajax({
              type: "GET",
              url: "tokenupdate/" + idOrder,
              success: function (data) {
                // indra
                // do print
                printUpdateOrderItem(idOrder)

                // set disabled
                $(".maindashboard").removeClass("disabled");
                $("#fhome").removeClass("disabled");
                $("#kitchenorder").removeClass("disabled");
                $("#todayqrorder").removeClass("disabled");
                $("#todayonlieorder").removeClass("disabled");
                $("#todayorder").removeClass("disabled");
                $("#ongoingorder").removeClass("disabled");
              },
            });

            // $.ajax({
            //   type: "GET",
            //   url: "postokengenerateupdate/" + result.orderid + "/1",
            //   success: function (data) {
            //     // indra
            //     printRawHtml(data);

            //     $(".maindashboard").removeClass("disabled");
            //     $("#fhome").removeClass("disabled");
            //     $("#kitchenorder").removeClass("disabled");
            //     $("#todayqrorder").removeClass("disabled");
            //     $("#todayonlieorder").removeClass("disabled");
            //     $("#todayorder").removeClass("disabled");
            //     $("#ongoingorder").removeClass("disabled");
            //   },
            // });
          } else {
            $.ajax({
              type: "GET",
              url: "tokenupdate/" + result.orderid,
              success: function (data) {
                $(".maindashboard").removeClass("disabled");
                $("#fhome").removeClass("disabled");
                $("#kitchenorder").removeClass("disabled");
                $("#todayqrorder").removeClass("disabled");
                $("#todayonlieorder").removeClass("disabled");
                $("#todayorder").removeClass("disabled");
                $("#ongoingorder").removeClass("disabled");
              },
            });
          }
        }
      );
      setTimeout(function () {
        toastr.options = {
          closeButton: true,
          progressBar: true,
          showMethod: "slideDown",
          timeOut: 4000,
        };
        toastr.success(result.msg, "Success");
        prevsltab.trigger("click");
      }, 300);
    },
    error: function (a) {},
  });
}

function payorderbill(status, orderid, totalamount) {
  $("#paidbill").attr(
    "onclick",
    "orderconfirmorcancel(" + status + "," + orderid + ")"
  );
  $("#maintotalamount").val(totalamount);
  $("#totalamount").val(totalamount);
  $("#paidamount").attr("max", totalamount);
  $("#payprint").modal("show");
}

function onlinepay() {
  $("#onlineordersubmit").submit();
}

function orderconfirmorcancel(status, orderid) {
  mystatus = status;
  if (status == 9 || status == 10) {
    status = 4;
    var pval = $("#paidamount").val();
    if (pval < 1 || pval == "") {
      alert("Please Insert Paid Amount!!!");
      return false;
    }
  }
  var carttype = "";
  var cterminal = "";
  var mybank = "";
  var mydigit = "";
  var paid = "";
  if (status == 4) {
    var carttype = $("#card_typesl").val();
    var cterminal = $("#card_terminal").val();
    var mybank = $("#bank").val();
    var mydigit = $("#last4digit").val();
    var paid = $("#paidamount").val();

    if (carttype == "") {
      alert("Please Select Payment Method!!!");
      return false;
    }
    if (carttype == 1) {
      if (cterminal == "") {
        alert(lang.crd_terminal_message);
        return false;
      }
    }
  }
  var csrf = $("#csrfhashresarvation").val();
  var dataString =
    "status=" +
    status +
    "&orderid=" +
    orderid +
    "&paytype=" +
    carttype +
    "&cterminal=" +
    cterminal +
    "&mybank=" +
    mybank +
    "&mydigit=" +
    mydigit +
    "&paid=" +
    paid +
    "&csrf_test_name=" +
    csrf;
  $.ajax({
    type: "POST",
    url: basicinfo.baseurl + "ordermanage/order/changestatus", //workingnow
    data: dataString,
    success: function (data) {
      $("#onprocesslist").html(data);
      if (mystatus == "9") {
        window.location.href =
          basicinfo.baseurl + "ordermanage/order/orderinvoice/" + orderid;
      } else if (mystatus == "10") {
        $("#payprint").modal("hide");

        prevsltab.trigger("click");
      } else if (mystatus == 4) {
        swal(
          {
            title: lang.ord_complte,
            text: lang.ord_com_sucs,
            type: "success",
            showCancelButton: false,
            confirmButtonColor: "#28a745",
            confirmButtonText: lang.yes,
            closeOnConfirm: true,
          },
          function () {
            prevsltab.trigger("click");
            $("#paidamount").val("");
            $("#payprint").modal("hide");
          }
        );
      }
    },
  });
}

function paysound() {
  var filename = basicinfo.baseurl + basicinfo.nofitysound;
  var audio = new Audio(filename);
  audio.play();
}

function load_unseen_notification(view = "") {
  var csrf = $("#csrfhashresarvation").val();
  var myAudio = document.getElementById("myAudio");
  var soundenable = possetting.soundenable;
  $.ajax({
    url: "notification",
    method: "POST",
    data: {
      csrf_test_name: csrf,
      view: view,
    },
    dataType: "json",
    success: function (data) {
      if (data.unseen_notification > 0) {
        $(".count").html(data.unseen_notification);
        if (soundenable == 1) {
          myAudio.play();
        }
      } else {
        if (soundenable == 1) {
          myAudio.pause();
        }
        $(".count").html(data.unseen_notification);
      }
    },
  });
}
var intervalc = 0;
setInterval(function () {
  load_unseen_notification(intervalc);
}, 30000);

function load_unseen_notificationqr(view = "") {
  var csrf = $("#csrfhashresarvation").val();
  var myAudio = document.getElementById("myAudio");
  var soundenable = possetting.soundenable;
  $.ajax({
    url: basicinfo.baseurl + "ordermanage/order/notificationqr",
    method: "POST",
    data: {
      csrf_test_name: csrf,
      view: view,
    },
    dataType: "json",
    success: function (data) {
      if (data.unseen_notificationqr > 0) {
        $(".count2").html(data.unseen_notificationqr);
        if (soundenable == 1) {
          myAudio.play();
        }
      } else {
        if (soundenable == 1) {
          myAudio.pause();
        }
        $(".count2").html(data.unseen_notification);
      }
    },
  });
}
setInterval(function () {
  $("li.active").trigger("click");
  load_unseen_notificationqr();
}, 30000);

function detailspop(orderid) {
  var csrf = $("#csrfhashresarvation").val();
  var myurl =
    basicinfo.baseurl + "ordermanage/order/orderdetailspop/" + orderid;
  var dataString = "orderid=" + orderid + "&csrf_test_name=" + csrf;
  $.ajax({
    type: "POST",
    url: myurl,
    data: dataString,
    success: function (data) {
      $(".orddetailspop").html(data);
      $("#orderdetailsp").modal("show");
    },
  });
}

function pospageprint(orderid) {
  var csrf = $("#csrfhashresarvation").val();
  var datavalue = "customer_name=" + customer_name + "&csrf_test_name=" + csrf;
  $.ajax({
    type: "POST",
    url: basicinfo.baseurl + "ordermanage/order/posprintview/" + orderid,
    data: datavalue,
    success: function (printdata) {
      if (basicinfo.printtype != 1) {
        $("#kotenpr").html(printdata);
        const style = "@page { margin:0px;font-size:18px; }";
        printJS({
          printable: "kotenpr",
          onPrintDialogClose: printJobComplete,
          type: "html",
          font_size: "25px",
          style: style,
          scanStyles: false,
        });
      }
    },
  });
}

function printPosinvoice(id) {
  var csrf = $("#csrfhashresarvation").val();
  var url = "posorderinvoice/" + id;
  $.ajax({
    type: "GET",
    url: url,
    data: {
      csrf_test_name: csrf,
    },
    success: function (data) {
      // indra
      printRawHtml(data);
    },
  });
}

function pos_order_invoice(id) {
  var csrf = $("#csrfhashresarvation").val();
  var url = "pos_order_invoice/" + id;
  $.ajax({
    type: "GET",
    url: url,
    data: {
      csrf_test_name: csrf,
    },
    success: function (data) {
      $("#messages").html(data);
    },
  });
}

function orderdetails_post(id) {
  var csrf = $("#csrfhashresarvation").val();
  var url = "orderdetails_post/" + id;
  $.ajax({
    type: "GET",
    url: url,
    data: {
      csrf_test_name: csrf,
    },
    success: function (data) {
      $("#messages").html(data);
    },
  });
}

function orderdetails_onlinepost(id) {
  var url = "orderdetails_post/" + id;
  var csrf = $("#csrfhashresarvation").val();
  $.ajax({
    type: "GET",
    url: url,
    data: {
      csrf_test_name: csrf,
    },
    success: function (data) {
      $("#settings").html(data);
    },
  });
}

load_unseen_notification();

function createMargeorder(orderid, value = null) {
  var csrf = $("#csrfhashresarvation").val();
  var url = "showpaymentmodal/" + orderid;
  callback = function (a) {
    $("#modal-ajaxview").html(a);
    $("#get-order-flag").val("2");
  };
  if (value == null) {
    getAjaxModal(url);
  } else {
    getAjaxModal(url, callback);
  }
}
/*all ongoingorder product as ajax*/
$(document).on("click", "#add_new_payment_type", function () {
  var gtotal = $("#grandtotal").val();
  var total = 0;
  $(".pay").each(function () {
    total += parseFloat($(this).val()) || 0;
  });
  if (total == gtotal) {
    alert("Paid amount is exceed to Total amount.");
    $("#pay-amount").text("0");
    return false;
  }
  var orderid = $("#get-order-id").val();
  var csrf = $("#csrfhashresarvation").val();
  var url = "showpaymentmodal/" + orderid + "/1";
  $.ajax({
    type: "GET",
    url: url,
    data: {
      csrf_test_name: csrf,
    },
    success: function (data) {
      $("#add_new_payment").append(data);
      var length = $(".number").length;
      $(".number:eq(" + (length - 1) + ")")
        .val(parseFloat($("#pay-amount").text()))
        .trigger("keyup");
    },
  });
});
$(document).on("click", ".close_div", function () {
  $(this).parent("div").remove();
  changedueamount();
});
/*show due invoice*/
$(document).on("click", ".due_print", function () {
  var id = $(this).children("option:selected").val();
  var url = $(this).attr("data-url");
  var csrf = $("#csrfhashresarvation").val();
  $.ajax({
    type: "GET",
    url: url,
    data: {
      csrf_test_name: csrf,
    },
    success: function (data) {
      // indra
      printRawHtml(data);
    },
  });
});
$(document).on("click", ".due_mergeprint", function () {
  var id = $(this).children("option:selected").val();
  var url = $(this).attr("data-url");
  var csrf = $("#csrfhashresarvation").val();
  $.ajax({
    type: "GET",
    url: url,
    data: {
      csrf_test_name: csrf,
    },
    success: function (data) {
      printRawHtml(data);
    },
  });
});

function printmergeinvoice(id) {
  var id = atob(id);
  var csrf = $("#csrfhashresarvation").val();
  var url = basicinfo.baseurl + "ordermanage/order/checkprint/" + id;
  $.ajax({
    type: "GET",
    url: url,
    data: {
      csrf_test_name: csrf,
    },
    success: function (data) {
      printRawHtml(data);
    },
  });
}

function showhidecard(element) {
  var cardtype = $(element).val();
  var data = $(element).closest("div.row").next().find("div.cardarea");

  if (cardtype == 4) {
    $("#isonline").val(0);
    $(element)
      .closest("div.row")
      .next()
      .find("div.cardarea")
      .addClass("display-none");
    $("#assigncard_terminal").val("");
    $("#assignbank").val("");
    $("#assignlastdigit").val("");
  } else if (cardtype == 1) {
    $("#isonline").val(0);
    $(element)
      .closest("div.row")
      .next()
      .find("div.cardarea")
      .removeClass("display-none");
  } else {
    $("#isonline").val(1);
    $(element)
      .closest("div.row")
      .next()
      .find("div.cardarea")
      .addClass("display-none");
    $("#assigncard_terminal").val("");
    $("#assignbank").val("");
    $("#assignlastdigit").val("");
  }
}


function changedueamount() {
  var inputval = parseFloat(0);
  var maintotalamount = $("#due-amount").text();

  $(".number").each(function () {
    var inputdata = parseFloat($(this).val());
    inputval = inputval + inputdata;
  });

  restamount = parseFloat(maintotalamount) - parseFloat(inputval);
  var changes = restamount.toFixed(3);
  if (changes <= 0) {
    $("#change-amount").text(Math.abs(changes));
    $("#pay-amount").text(0);
  } else {
    $("#change-amount").text(0);
    $("#pay-amount").text(changes);
  }
}

function mergeorderlist() {
  var values = $('input[name="margeorder"]:checked')
    .map(function () {
      return $(this).val();
    })
    .get()
    .join(",");
  var csrf = $("#csrfhashresarvation").val();
  var dataString = "orderid=" + values + "&csrf_test_name=" + csrf;
  $.ajax({
    url: basicinfo.baseurl + "ordermanage/order/mergemodal",
    method: "POST",
    data: dataString,
    success: function (data) {
      $("#payprint_marge").modal("show");
      $("#modal-ajaxview").html(data);
      $("#get-order-flag").val("2");
    },
  });
}

function duemergeorder(orderid, mergeid) {
  var allorderid = $("#allmerge_" + mergeid).val();
  var csrf = $("#csrfhashresarvation").val();
  var dataString =
    "orderid=" +
    orderid +
    "&mergeid=" +
    mergeid +
    "&allorderid=" +
    allorderid +
    "&csrf_test_name=" +
    csrf;
  $.ajax({
    url: basicinfo.baseurl + "ordermanage/order/duemergemodal",
    method: "POST",
    data: dataString,
    success: function (data) {
      $("#payprint_marge").modal("show");
      $("#modal-ajaxview").html(data);
      $("#get-order-flag").val("2");
    },
  });
}

function margeorderconfirmorcancel() {
  var thisForm = $("#paymodal-multiple-form");
  var formdata = new FormData(thisForm[0]);

  $.ajax({
    type: "POST",
    url: basicinfo.baseurl + "ordermanage/order/changeMargeorder",
    data: formdata,
    processData: false,
    contentType: false,
    success: function (data) {
      $("#payprint_marge").modal("hide");
      if (basicinfo.printtype != 1) {
        printRawHtml(data);
      }
      prevsltab.trigger("click");
    },
  });
}

function duemargebill() {
  var thisForm = $("#paymodal-multiple-form");
  var formdata = new FormData(thisForm[0]);

  $.ajax({
    type: "POST",
    url: basicinfo.baseurl + "ordermanage/order/changeMargedue",
    data: formdata,
    processData: false,
    contentType: false,
    success: function (data) {
      $("#payprint_marge").modal("hide");
      if (basicinfo.printtype != 1) {
        printRawHtml(data);
      }
      prevsltab.trigger("click");
    },
  });
}

function margeorder() {
  var totaldue = 0;
  $(".marg-check").each(function () {
    if ($(this).is(":checked")) {
      var id = $(this).val();
      totaldue = parseFloat($("#due-" + id).text()) + totaldue;
    }
    $("#due-amount").text(totaldue);
    $("#totalamount_marge").text(totaldue);
    $("#paidamount_marge").val(totaldue);
  });
}

function checktable(id = null) {
  if (id != null) {
    var select = "#person-" + id;
    var valu = $(select).val();
    $("#table_member").val(valu);
    var url = "checktablecap/" + id;
  } else {
    idd = $("#tableid").val();
    var url = "checktablecap/" + idd;
  }
  var order_person = $("#table_member").val();

  if (order_person != "") {
    var csrf = $("#csrfhashresarvation").val();
    $.ajax({
      type: "GET",
      url: url,
      data: {
        csrf_test_name: csrf,
      },
      success: function (data) {
        // indra direct edit

        // if (order_person > data) {
        //   setTimeout(function () {
        //     toastr.options = {
        //       closeButton: true,
        //       progressBar: true,
        //       showMethod: "slideDown",
        //       timeOut: 4000,
        //     };
        //     toastr.warning("table capacity overflow", "Warning");
        //   }, 300);
        // } else {
        //   if (id != null) {
        //     $("#tableid").val(id).trigger("change");
        //     $("#table_member_multi").val(0);
        //     $("#table_member_multi_person").val(0);
        //     $("#table_person").val(order_person);
        //     $("#tablemodal").modal("hide");
        //   }

        //   return false;
        // }

        if (id != null) {
          $("#tableid").val(id).trigger("change");
          $("#table_member_multi").val(0);
          $("#table_member_multi_person").val(0);
          $("#table_person").val(order_person);
          $("#tablemodal").modal("hide");
        }

        return false;
      },
    });
  } else {
    setTimeout(function () {
      $("#table_member").focus();

      toastr.options = {
        closeButton: true,
        progressBar: true,
        showMethod: "slideDown",
        timeOut: 4000,
      };
      toastr.error("Please type Number of person", "Error");
    }, 300);
  }
}

function showTablemodal() {
  var url = "showtablemodal";
  getAjaxModal(url, false, "#table-ajaxview", "#tablemodal");
}

function showfloor(floorid) {
  var csrf = $("#csrfhashresarvation").val();
  var geturl = "fllorwisetable";
  var dataString = "floorid=" + floorid + "&csrf_test_name=" + csrf;
  $.ajax({
    type: "POST",
    url: geturl,
    data: dataString,
    success: function (data) {
      $("#floor" + floorid).html(data);
    },
  });
}

function deleterow_table(id, tableid = null) {
  var csrf = $("#csrfhashresarvation").val();
  var dataString = "csrf_test_name=" + csrf;
  if (tableid == null) {
    var url = "delete_table_details/" + id;
    $.ajax({
      type: "GET",
      url: url,
      data: dataString,
      success: function (data) {
        if (data == 1) {
          $("#table-tr-" + id).remove();
        }
      },
    });
  } else {
    var url = "delete_table_details_all/" + tableid;
    $.ajax({
      type: "GET",
      url: url,
      data: dataString,
      success: function (data) {
        if (data == 1) {
          $("#table-tbody-" + tableid).empty();
        }
      },
    });
  }
}

function multi_table() {
  var arr = $('input[name="add_table[]"]')
    .map(function () {
      return this.value;
    })
    .get();
  $("#table_member_multi").val(arr);
  var value = [];
  var order_person_t = 0;
  for (i = 0; i < arr.length; i++) {
    value[i] = $("#person-" + arr[i]).val();

    // Parse the input value to an integer and check if it’s a valid number
    let parsedValue = parseInt(value[i], 10);
    if (!isNaN(parsedValue)) {
      // Check if parsedValue is a valid number
      order_person_t += parsedValue;
    }
  }

  $("#table_member").val($("#person-" + arr[0]).val());
  $("#table_person").val(order_person_t);
  $("#table_member_multi_person").val(value);

  $("#tablemodal").modal("hide");
  $("#tableid").val(arr[0]).trigger("change");
}
$(document).on("change", "#update_product_name", function () {
  var tid = $(this).children("option:selected").val();
  var idvid = tid.split("-");
  var id = idvid[0];
  var vid = idvid[1];
  var csrf = $("#csrfhashresarvation").val();
  var updateid = $("#saleinvoice").val();
  var url = "addtocartupdate_uniqe" + "/" + id + "/" + updateid;
  var dataString = "csrf_test_name=" + csrf;
  /*check production*/
  /*please fixt cart total counting*/
  var productionsetting = $("#production_setting").val();
  if (productionsetting == 1) {
    var checkqty = 1;
    var checkvalue = checkproduction(id, vid, checkqty);

    if (checkvalue == false) {
      $("#update_product_name").html("");
      return false;
    }
  }
  /*end checking*/
  $.ajax({
    type: "GET",
    url: url,
    data: dataString,
    success: function (data) {
      var myurl = "adonsproductadd" + "/" + id;
      $.ajax({
        type: "GET",
        url: myurl,
        data: dataString,
        success: function (data) {
          $(".addonsinfo").html(data);
          $("#edit").modal("show");
          var tax = $("#tvat").val();
          var discount = $("#tdiscount").val();
          var tgtotal = $("#tgtotal").val();
          $("#vat").val(tax);
          $("#calvat").text(tax);
          var sc = $("#sc").val();
          $("#service_charge").val(sc);
          $("#invoice_discount").val(discount);
          if (basicinfo.isvatinclusive == 1) {
            $("#caltotal").text(tgtotal - tax);
          } else {
            $("#caltotal").text(tgtotal);
          }
          $("#grandtotal").val(tgtotal);
          $("#orggrandTotal").val(tgtotal);
          $("#orginattotal").val(tgtotal);
          $("#update_product_name").html("");
        },
      });
    },
  });
});
$(function ($) {
  $("#customer_name").select2();
  var barcodeScannerTimer;
  var barcodeString = "";

  $("#customer_name").on("select2:open", function () {
    document.getElementsByClassName("select2-search__field")[0].onkeypress =
      function (evt) {
        barcodeString = barcodeString + String.fromCharCode(evt.charCode);
        clearTimeout(barcodeScannerTimer);
        barcodeScannerTimer = setTimeout(function () {
          processbarcodeGui();
        }, 300);
      };
  });

  function processbarcodeGui() {
    if (barcodeString != "") {
      var customerid = Number(barcodeString).toString();
      if (Math.floor(customerid) == customerid && $.isNumeric(customerid)) {
        $("#customer_name").select2().val(customerid).trigger("change");
      }
      $("#customer_name").val(customerid);
    } else {
      alert("barcode is invalid: " + barcodeString);
    }

    barcodeString = "";
  }
});

/*for split order js*/
function showsplitmodal(orderid, option = null) {
  var url = "showsplitorder/" + orderid;
  callback = function (a) {
    $("#modal-ajaxview").html(a);
  };
  if (option == null) {
    getAjaxModal(url, false, "#table-ajaxview", "#tablemodal");
  } else {
    getAjaxModal(url, callback);
  }
}

function showsuborder(element) {
  var val = $(element).val();
  var url = $(element).attr("data-url") + val;
  var orderid = $(element).attr("data-value");
  var csrf = $("#csrfhashresarvation").val();
  var datavalue = "orderid=" + orderid;
  getAjaxView(url, "show-sub-order", false, datavalue, "post");
}

function getAjaxView(
  url,
  ajaxclass,
  callback = false,
  data = "",
  method = "get"
) {
  var csrf = $("#csrfhashresarvation").val();
  var fulldata = data + "&csrf_test_name=" + csrf;
  $.ajax({
    url: url,
    type: method,
    data: fulldata,
    beforeSend: function (xhr) {},
    success: function (result) {
      if (callback) {
        callback(result);
        return;
      }
      $("#" + ajaxclass).html(result);
    },
    error: function (a) {},
  });
  return false;
}

function selectelement(element) {
  $(".split-item").each(function (index) {
    $(this).removeClass("split-selected");
  });
  $(element).toggleClass("split-selected");
}

function addintosuborder(menuid, orderid, element) {
  var presentvalue = $(element).find("td:eq(1)").text();
  var isselected = $(".split-selected").length;
  if (presentvalue != 0 && isselected == 1) {
    var suborderid = $(".split-selected").attr("data-value");
    var service_chrg = $("#service-" + suborderid).val();
    var csrf = $("#csrfhashresarvation").val();
    var datavalue =
      "orderid=" +
      orderid +
      "&menuid=" +
      menuid +
      "&suborderid=" +
      suborderid +
      "&qty=" +
      1 +
      "&service_chrg=" +
      service_chrg;
    var url = $(element).attr("data-url");
    var id = "table-tbody-" + orderid + "-" + suborderid;
    getAjaxView(url, id, false, datavalue, "post");

    var nowvalue = parseInt(presentvalue) - 1;
    $(element).find("td:eq(1)").text(nowvalue);
  }
}

function paySuborder(element) {
  var id = $(element).attr("id").replace("subpay-", "");
  var url = $(element).attr("data-url");
  var vat = $("#vat-" + id).val();
  if ($("#vat-" + id).length) {
    var service = $("#service-" + id).val();
    var total = $("#total-sub-" + id).val();
    var customerid = $("#customer-" + id).val();
    $("#tablemodal").modal("hide");
    $("#modal-ajaxview").empty();
    var data =
      "sub_id=" +
      id +
      "&vat=" +
      vat +
      "&service=" +
      service +
      "&total=" +
      total +
      "&customerid=" +
      customerid;
    getAjaxModal(
      url,
      false,
      "#modal-ajaxview-split",
      "#payprint_split",
      data,
      "post"
    );
  } else {
    return false;
  }
}

function submitmultiplepaysub(subid) {
  var thisForm = $("#paymodal-multiple-form");
  var inputval = parseFloat(0);
  var maintotalamount = $("#due-amount").text();

  $(".number").each(function () {
    var inputdata = parseFloat($(this).val());
    inputval = inputval + inputdata;
  });
  if (inputval < parseFloat(maintotalamount)) {
    setTimeout(function () {
      toastr.options = {
        closeButton: true,
        progressBar: true,
        showMethod: "slideDown",
        timeOut: 4000,
      };
      toastr.error("Pay full amount ", "Error");
    }, 100);
    return false;
  }
  var formdata = new FormData(thisForm[0]);
  $.ajax({
    type: "POST",
    url: basicinfo.baseurl + "ordermanage/order/paymultiplsub",
    data: formdata,
    processData: false,
    contentType: false,
    success: function (data) {
      var value = $("#get-order-flag").val();

      setTimeout(function () {
        toastr.options = {
          closeButton: true,
          progressBar: true,
          showMethod: "slideDown",
          timeOut: 4000,
        };
        toastr.success("payment taken successfully", "Success");
        $("#payprint_split").modal("hide");
        $("#subpay-" + subid).hide();
        $("#modal-ajaxview-split").empty();
        if (basicinfo.printtype != 1) {
          printRawHtml(data);
        }
        prevsltab.trigger("click");
      }, 100);
    },
  });
}

function showsplit(orderid) {
  var url =
    basicinfo.baseurl + "ordermanage/order/showsplitorderlist/" + orderid;
  getAjaxModal(url, false, "#modal-ajaxview-split", "#payprint_split");
}

function possubpageprint(orderid) {
  var csrf = $("#csrfhashresarvation").val();
  $.ajax({
    type: "GET",
    url: basicinfo.baseurl + "ordermanage/order/posprintdirectsub/" + orderid,
    data: {
      csrf_test_name: csrf,
    },
    success: function (printdata) {
      printRawHtml(printdata);
    },
  });
}
/*end split order js*/
function itemnote(rowid, notes, qty, isupdate, isgroup = null) {
  $("#foodnote").val(notes);
  $("#foodqty").val(qty);
  $("#foodcartid").val(rowid);
  $("#foodgroup").val(isgroup);
  if (isupdate == 1) {
    $("#notesmbt").text("Update Note");
    $("#notesmbt").attr("onclick", "addnotetoupdate()");
  } else {
    $("#notesmbt").text("Update Note");
    $("#notesmbt").attr("onclick", "addnotetoitem()");
  }
  $("#vieworder").modal("show");
}

function addnotetoitem() {
  var rowid = $("#foodcartid").val();
  var note = $("#foodnote").val();
  var foodqty = $("#foodqty").val();
  var csrf = $("#csrfhashresarvation").val();
  var geturl = basicinfo.baseurl + "ordermanage/order/additemnote";
  var dataString =
    "foodnote=" +
    note +
    "&rowid=" +
    rowid +
    "&qty=" +
    foodqty +
    "&csrf_test_name=" +
    csrf;
  $.ajax({
    type: "POST",
    url: geturl,
    data: dataString,
    success: function (data) {
      setTimeout(function () {
        toastr.options = {
          closeButton: true,
          progressBar: true,
          showMethod: "slideDown",
          timeOut: 4000,
        };
        toastr.success("Note Added Successfully", "Success");
        $("#addfoodlist").html(data);
        $("#vieworder").modal("hide");
      }, 100);
    },
  });
}

function addnotetoupdate() {
  var rowid = $("#foodcartid").val();
  var note = $("#foodnote").val();
  var orderid = $("#foodqty").val();
  var group = $("#foodgroup").val();
  var csrf = $("#csrfhashresarvation").val();
  var geturl = basicinfo.baseurl + "ordermanage/order/addnotetoupdate";
  var dataString =
    "foodnote=" +
    note +
    "&rowid=" +
    rowid +
    "&orderid=" +
    orderid +
    "&group=" +
    group +
    "&csrf_test_name=" +
    csrf;
  $.ajax({
    type: "POST",
    url: geturl,
    data: dataString,
    success: function (data) {
      setTimeout(function () {
        toastr.options = {
          closeButton: true,
          progressBar: true,
          showMethod: "slideDown",
          timeOut: 4000,
        };
        toastr.success("Note Added Successfully", "Success");
        $("#updatefoodlist").html(data);
        $("#vieworder").modal("hide");
      }, 100);
    },
  });
}

function opencashregister() {
  var form = $("#cashopenfrm")[0];
  var formdata = new FormData(form);
  $.ajax({
    type: "POST",
    url: basicinfo.baseurl + "ordermanage/order/addcashregister",
    data: formdata,
    processData: false,
    contentType: false,
    success: function (data) {
      if (data == 1) {
        $("#openregister").modal("hide");
      } else {
        alert("Something Wrong!!! .Please Select Counter Number!!");
      }
    },
  });
}

function closeopenresister() {
  var closeurl = basicinfo.baseurl + "ordermanage/order/cashregisterclose";
  var csrf = $("#csrfhashresarvation").val();
  $.ajax({
    type: "GET",
    async: false,
    url: closeurl,
    data: {
      csrf_test_name: csrf,
    },
    success: function (data) {
      $("#openclosecash").html(data);
      var htitle = $("#rpth").text();
      var counter = $("#pcounter").val();
      var puser = $("#puser").val();
      var fullheader =
        "Cash Register In" +
        htitle +
        "\n" +
        "Counter:" +
        counter +
        "\n" +
        puser;
      $("#openregister").modal("show");
      $("#RoleTbl").DataTable({
        responsive: true,
        paging: true,
        dom: "Bfrtip",
        lengthMenu: [
          [25, 50, 100, 150, 200, 500, -1],
          [25, 50, 100, 150, 200, 500, "All"],
        ],
        buttons: [
          {
            extend: "csv",
            title: "Open-Close Cash Register",
            className: "btn-sm",
            footer: true,
            header: true,
            orientation: "landscape",
            messageTop: fullheader,
          },
          {
            extend: "excel",
            title: "Open-Close Cash Register",
            className: "btn-sm",
            title: "exportTitle",
            messageTop: fullheader,
            footer: true,
            header: true,
            orientation: "landscape",
          },
          {
            extend: "pdfHtml5",
            title: "Open-Close Cash Register",
            className: "btn-sm",
            footer: true,
            header: true,
            orientation: "landscape",
            messageTop: fullheader,
            customize: function (doc) {
              doc.defaultStyle.alignment = "center";
              doc.content[1].table.widths = Array(
                doc.content[1].table.body[0].length + 1
              )
                .join("*")
                .split("");
            },
          },
        ],
        searching: true,
        processing: true,
      });
    },
  });
}

function closecashregister() {
  var form = $("#cashopenfrm")[0];
  var formdata = new FormData(form);
  $.ajax({
    type: "POST",
    url: basicinfo.baseurl + "ordermanage/order/closecashregister",
    data: formdata,
    processData: false,
    contentType: false,
    success: function (data) {
      if (data == 1) {
        $("#openregister").modal("hide");
        window.location.href = basicinfo.baseurl + "dashboard/home";
      } else {
        alert("Something Wrong On Cash Closing!!!");
      }
    },
  });
}

function closeandprintcashregister() {
  var form = $("#cashopenfrm")[0];
  var formdata = new FormData(form);
  $.ajax({
    type: "POST",
    url: basicinfo.baseurl + "ordermanage/order/closecashregister",
    data: formdata,
    processData: false,
    contentType: false,
    success: function (data) {
      if (data == 0) {
        alert("Something Wrong On Cash Closing!!!");
      } else {
        $("#openregister").modal("hide");
        window.location.href = basicinfo.baseurl + "dashboard/home?status=done";
      }
    },
  });
}

$(".lang_box").on("click", function (event) {
  var submenu = $(this).next(".lang_options");
  submenu.slideToggle(400, function () {});
});

function BtPrint(prn) {
  var S = "#Intent;scheme=rawbt;";
  var P =  "package=ru.a402d.rawbtprinter;end;";
  var textEncoded = encodeURI(prn);
  window.location.href = "intent:" + textEncoded + S + P;
}

// Function to format item lines (align quantity, price)
function formatItem(name, qty, unit, price) {
  let total = qty * price;
  return name + "\n  " + qty + " " + unit + " x Rp. " + formatRupiah(price) + "\n";
}

// Function to align text (left and right)
function alignText(label, value) {
  let totalLength = 48; // Max chars per line for 80mm
  let spaceCount = totalLength - (label.length + value.length);
  let spaces = " ".repeat(spaceCount > 0 ? spaceCount : 1);
  return label + spaces + value + "\n";
}

function alignLeft(label, value) {
  let maxLabelLength = 15; // Adjust based on the longest label
  let paddedLabel = label.padEnd(maxLabelLength, " "); // Ensures labels align
  return `${paddedLabel}: ${value}\n`;
}

function centerText(text) {
  let totalLength = 48; // Max chars per line for 80mm
  let padding = Math.floor((totalLength - text.length) / 2);
  let spaces = " ".repeat(padding > 0 ? padding : 0);
  return spaces + text + "\n";
}

function alignItem({ label, value, printerWidth = 48, fontFormat = "normal", maxLabelLength = 2 }) {
  let maxValueLength = (fontFormat === "doubleWidth" ? printerWidth / 2 : printerWidth) - (maxLabelLength + 1); // Remaining space for value
  let paddedLabel = label.padEnd(maxLabelLength, " "); // Ensures labels align
  let formattedText = `${paddedLabel} `; // First line with label

  let words = value.split(" ");
  let line = "";

  words.forEach((word) => {
    if ((line + " " + word).trim().length > maxValueLength) {
      formattedText += line.trim() + "\n" + " ".repeat(maxLabelLength + 1); // Wrap to next line
      line = word;
    } else {
      line += (line.length > 0 ? " " : "") + word;
    }
  });

  formattedText += line.trim() + "\n"; // Append remaining text

  return formattedText;
}

// Format number with dot separator
function thousand_separator(number) {
  if (isNaN(number) || number === null) return "0"; // Handle invalid numbers
  return parseInt(number, 10).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
}

function formatDateTime(date) {
  const options = { year: 'numeric', month: 'short', day: '2-digit', hour: '2-digit', minute: '2-digit' };
  return new Date(date).toLocaleDateString('id-ID', options);
}

function formatDateIndo(date) {
  const options = { year: 'numeric', month: 'short', day: '2-digit'};
  return new Date(date).toLocaleDateString('id-ID', options);
}

// Dynamic Receipt Generator with Sections
function generateTextReceipt(params = {}) {
  const { type="receipt", dataToPrint = {} } = params;
  const dotLine = (count = 1, length = 48) => "-".repeat(length) + "\n".repeat(count);
  const enterSpace = (count = 1) => "\n".repeat(count);
  const fullCut = '\x1B\x64\x03' + '\x1D\x56\x41\x00';
  const cashDrawerOpen = "\x1B\x70\x00\x19\xFA";

  function fontFormat(fontFormatCode = "normal") {
    switch (fontFormatCode) {
        case "bold":
            return "\x1B\x45\x01"; // Enable bold
        case "underline":
            return "\x1B\x2D\x01"; // Enable underline
        case "small":
            return "\x1B\x21\x01"; // Small font
        case "doubleHeight":
            return "\x1B\x21\x10"; // Double height
        case "doubleWidth":
            return "\x1B\x21\x20"; // Double width
        case "biggest":
            return "\x1B\x21\x30"; // 2x Width & Height
        case "normal":
          return "\x1B\x45\x00";
        default:
            return "\x1B\x45\x00\x1B\x2D\x00\x1B\x21\x00"; // Reset to normal text
    }
  }

  let receipt = "", isItemDapur = false, isItemBar = false;

  // Section: Header
  function sectionHeader() {
      return centerText("Tudung Saji Signature") +
             centerText("Jalan Gelatik Kampung Melayu") +
             centerText("Kota Pekanbaru, Riau") +
             centerText("082164645982") +
             dotLine(1);
  }

  // Section: Transaction Info
  function sectionTransactionInfo() {
      return alignLeft("No Nota", dataToPrint.orderDetail.saleinvoice || "-") +
             alignLeft("Waktu", formatDateIndo(dataToPrint.orderDetail.order_date || new Date()) + ", " + (dataToPrint.orderDetail.order_time|| "") ) +
             alignLeft("Pelanggan", dataToPrint.orderDetail.customer_name || "-") +
             alignLeft("Pelayan", dataToPrint.orderDetail.waiter_name || "-") +
             alignLeft("Kasir", dataToPrint.orderDetail.cashier_name || "Nabila") +
             alignLeft("Jenis Transaksi", dataToPrint.orderDetail.customer_type || "-") +
             alignLeft("Nomor Meja", dataToPrint.orderDetail.table_name || "-") +
             dotLine(1);
  }

  // Section: Item Details
  function sectionItemDetails(funcParams = {}) {
    const { isShowPrice = true, isShowNotes = false, itemType = "all" } = funcParams;
    let itemsData = []

    if (type === "receipt" || type === "bill") {
      itemsData = dataToPrint.orderItemsBill
    } else {
      itemsData = dataToPrint.orderItem
    }

    let items = "";
    itemsData.forEach(item => {
      item.food_kitchenid == 1 ? isItemDapur = true : "";
      item.food_kitchenid == 11 ? isItemBar = true : "";

      // Filtering logic
      if (itemType === "kitchen" && item.food_kitchenid != 1) return
      if (itemType === "bar" && item.food_kitchenid != 11) return

      let itemName = `${item.food_ProductName || "Item Tidak Diketahui"}`;
      let itemPrice = thousand_separator( (item.price * item.menuqty) || 0);

      if (!isShowPrice) {
          // items += alignText(`${item.menuqty || 0}x ${itemName}`, ""); 
          items += alignItem({ 
            label: `${item.menuqty || 0}`, 
            value: `${itemName}`,
            fontFormat: "doubleWidth"
          })
      } else {
          items += alignText(`${item.menuqty || 0}x ${itemName}`, `Rp. ${itemPrice}`);
      }

      if (isShowNotes && item.notes !== "") { 
          // items += `   catatan: ${item.notes}`;
          items += alignItem({ 
            label: "", 
            value: `catatan: ${item.notes}`,
            fontFormat: "doubleWidth",
            maxLabelLength: 4
          })
          items += enterSpace(2);

      } else if (!["receipt", "bill"].includes(type)) {
        items += enterSpace(1);
      }
    });

    items += enterSpace(1);
    items += dotLine(1);
    return items;
  }

  // section itemUpdateDetails
  function sectionItemUpdateDetails(funcParams = {}) {
    const { isShowPrice = true, isShowNotes = false, itemType = "all" } = funcParams;
    
    let items = "";

    dataToPrint.latestBatch.batch_items.forEach(item => {
        // Check kitchen type
        if (item.food_kitchenid == "1") isItemDapur = true;
        if (item.food_kitchenid == "11") isItemBar = true;

        // Filtering logic
        if (itemType === "kitchen" && item.food_kitchenid != "1") return;
        if (itemType === "bar" && item.food_kitchenid != "11") return;

        let itemName = item.food_ProductName || "Item Tidak Diketahui";
        let itemQty = parseInt(item.total_qty) || 0;

        items += alignItem({ 
          label: `${itemQty}`, 
          value: `${itemName}`,
          fontFormat: "doubleWidth"
        });

        // Handle item notes
        if (isShowNotes && item.item_note) {
            items += alignItem({ 
                label: "", 
                value: `catatan: ${item.item_note}`,
                fontFormat: "doubleWidth",
                maxLabelLength: 4
            });
            items += enterSpace(2);
        } else {
            items += enterSpace(1);
        }
    });

    items += enterSpace(1);
    items += dotLine(1);
    return items;
  }

  function sectionItemCanceled(funcParams = {}) {
    const { isShowNotes = false, itemType = "all" } = funcParams;
  
    let items = "";
  
    dataToPrint.cancelItem.forEach(item => {

      items += alignItem({
        label: `${item.total_qty}`,
        value: `${item.food_ProductName}`,
        fontFormat: "doubleWidth"
      });

      items += enterSpace(1);

    });
  
    items += enterSpace(1);
    items += dotLine(1);
    return items;
  }

  function sectionBatchInfo() {
    let addToPrintout = ""
    addToPrintout += centerText("BATCH: " + dataToPrint.latestBatch.update_batch)
    return addToPrintout
  }
  
  // Section: Subtotal
  function sectionSubtotal() {
    if (type === "receipt" || type === "bill") {
      return alignText(`Subtotal ${dataToPrint.orderItemsBill.length} Produk`, `Rp. ${thousand_separator(dataToPrint.orderDetail?.totalamount || 0)}`);
    }
  }

  // Section: Payment Details
  function sectionPaymentDetails() {
      let payments = "";

      payments += alignText("Total Tagihan", `Rp. ${thousand_separator(dataToPrint.orderDetail?.totalamount || 0)}`)

      dataToPrint.orderPayment.forEach(each => {  
        let paymentMethod = each.payment_method || "Tidak Diketahui";
        let paymentBankTerminal = each.bank_terminal_name ? ` (${each.bank_terminal_name})` : "";
        let paymentAmount = thousand_separator(each.paidamount || 0);

        payments += alignText(`${paymentMethod}${paymentBankTerminal}`, `Rp. ${paymentAmount}`);

        
      });

      payments += alignText("Total Bayar", `Rp. ${thousand_separator(dataToPrint.orderDetail?.customerpaid || 0)}`)
      payments += enterSpace(1);
      payments += dotLine(1);
      return payments;

  }

  // Section: Footer
  function sectionFooter(slipCode) {
    return centerText("instagram: @tudungsaji.signature") +
           dotLine(1) +
           (slipCode === "receipt" ? centerText("Pesanan yang sudah dibeli tidak dapat ditukar kembali") : "") +
           centerText(`Terbayar: ${formatDateTime(dataToPrint.orderDetail.payment_date || new Date())}`) +
           centerText(`Dicetak oleh: ${dataToPrint.orderDetail.printed_by || "Kasir"}`) +
           enterSpace(3);
  }

  // ==== TEMPLATE FUNCTIONS ====
  function templateReceipt() {
      return  fontFormat() +
              sectionHeader() +
              sectionTransactionInfo() +
              sectionItemDetails({ isShowPrice: true, isShowNotes: false }) +
              sectionSubtotal() +
              sectionPaymentDetails() +
              dotLine(1) +
              sectionFooter();
  }

  function templateBill() {
      return  fontFormat() +
              sectionHeader() +
              sectionTransactionInfo() +
              sectionItemDetails({ isShowPrice: true, isShowNotes: false }) +
              sectionSubtotal() +
              dotLine(1) +
              sectionFooter();
  }

  function templateKitchen() {
      return  fontFormat("") +
              centerText("=== DAFTAR PESANAN DAPUR ===") +
              enterSpace(2) +
              sectionTransactionInfo() +
              fontFormat("doubleWidth") +
              sectionItemDetails({ isShowPrice: false, isShowNotes: true , itemType: "kitchen"}) +
              fontFormat("")
  }

  function templateBar() {
      return  fontFormat() +
              centerText("=== DAFTAR PESANAN BAR ===") +
              enterSpace(2) +
              sectionTransactionInfo() +
              fontFormat("doubleWidth") +
              sectionItemDetails({ isShowPrice: false, isShowNotes: true, itemType: "bar"})
  }

  function templateChecker() {
    return  fontFormat() +
            centerText("=== DAFTAR CHECKER DAPUR ===") +
            enterSpace(2) +
            sectionTransactionInfo() +
            fontFormat("doubleWidth") +
            sectionItemDetails({ isShowPrice: false, isShowNotes: true , itemType: "kitchen"})
  }

  function templateCheckerBar() {
    return  fontFormat() +
            centerText("=== DAFTAR CHECKER BAR ===") +
            enterSpace(2) +
            sectionTransactionInfo() +
            fontFormat("doubleWidth") +
            sectionItemDetails({ isShowPrice: false, isShowNotes: true , itemType: "bar"})
  }

  function templateTable() {
    return  fontFormat() +
            centerText("=== DAFTAR PESANAN MEJA ===") +
            enterSpace(2) +
            sectionTransactionInfo() +
            fontFormat("doubleWidth") +
            sectionItemDetails({ isShowPrice: false, isShowNotes: true })
  }

  function templateKitchenUpdateItem() {
    return  fontFormat("") +
            centerText("=== PESANAN ADD ON DAPUR ===") +
            sectionBatchInfo() +
            enterSpace(2) +
            sectionTransactionInfo() +
            fontFormat("doubleWidth") +
            sectionItemUpdateDetails({ isShowPrice: false, isShowNotes: true , itemType: "kitchen"}) +
            fontFormat("")
  }
  
  function templateBarUpdateItem() {
      return  fontFormat() +
              centerText("=== PESANAN ADD ON BAR ===") +
              sectionBatchInfo() +
              enterSpace(2) +
              sectionTransactionInfo() +
              fontFormat("doubleWidth") +
              sectionItemUpdateDetails({ isShowPrice: false, isShowNotes: true, itemType: "bar"}) +
              fontFormat("")
  }

  function templateCheckerUpdateItemBar() {
    return  fontFormat() +
            centerText("=== CHECKER ADD ON BAR ===") +
            sectionBatchInfo() +
            enterSpace(2) +
            sectionTransactionInfo() +
            fontFormat("doubleWidth") +
            sectionItemUpdateDetails({ isShowPrice: false, isShowNotes: true, itemType: "bar"}) +
            fontFormat("")
  }

  function templateCheckerUpdateItem() {
    return  fontFormat() +
            centerText("=== CHECKER ADD ON DAPUR ===") +
            sectionBatchInfo() +
            enterSpace(2) +
            sectionTransactionInfo() +
            fontFormat("doubleWidth") +
            sectionItemUpdateDetails({ isShowPrice: false, isShowNotes: true , itemType: "kitchen"})
  }

  function templateTableUpdateItem() {
    return  fontFormat() +
            centerText("=== PESANAN ADD ON MEJA ===") +
            sectionBatchInfo() +
            enterSpace(2) +
            sectionTransactionInfo() +
            fontFormat("doubleWidth") +
            sectionItemUpdateDetails({ isShowPrice: false, isShowNotes: true })
  }

  function templateTableCancelItem() {
    return  fontFormat() +
            centerText("=== PESANAN DICANCEL ===") +
            enterSpace(2) +
            sectionTransactionInfo() +
            fontFormat("doubleWidth") +
            sectionItemCanceled({ isShowPrice: false, isShowNotes: true })
  }

  // ==== SWITCH CASE ====
  switch (type) {
      case "receipt":
          receipt += templateReceipt();
          receipt += cashDrawerOpen;
          break;

      case "bill":
          receipt += templateBill();
          break;

      case "kitchen":
          receipt += templateKitchen();
          receipt += fullCut;
          receipt += fontFormat();
          receipt += templateChecker();
          break;

      case "bar": 
          receipt += templateBar();
          receipt += fullCut;
          receipt += fontFormat();
          receipt += templateCheckerBar();
          break;

      case "table":
          receipt += templateTable();
          break;
      
      case "checker":
          receipt += templateChecker();
          break;
      
      case "tableUpdate":
          receipt += templateTableUpdateItem();
          break;
      
      case "kitchenUpdate":
          receipt += templateKitchenUpdateItem();
          receipt += fullCut;
          receipt += fontFormat();
          receipt += templateCheckerUpdateItem();
          break;

      case "barUpdate":
          receipt += templateBarUpdateItem();
          receipt += fullCut;
          receipt += fontFormat();
          receipt += templateCheckerUpdateItemBar();
          break;
      
      case "cancelItem":
          receipt += templateTableCancelItem();
          break;
      
      default:
          receipt += sectionHeader();
          receipt += sectionTransactionInfo();
          receipt += sectionFooter();
          break;
  }

  return receipt;
}

function WsPrint(printerIP, isPrintLogo, prn) {
  const ws = new WebSocket("ws://localhost:8080");

  ws.onopen = () => {
      console.log("✅ Connected to WebSocket Server");
      ws.send(JSON.stringify({
          printerIp: printerIP,
          printerPort: 9100,
          text: prn,
          isPrintLogo: isPrintLogo
      }));
  };

  ws.onmessage = (event) => {
      console.log("📩 Server Response:", event.data);
      // alert("Server Response: " + event.data);
  };

  ws.onerror = (error) => {
      console.error("❌ WebSocket Error:", error);
      alert("WebSocket error! Please check the server.");
  };

  ws.onclose = () => {
      alert("WebSocket connection closed.");
  };
}

function openCashDrawer() {
  const ws = new WebSocket("ws://localhost:8080");

  ws.onopen = () => {
      console.log("✅ Connected to WebSocket Server");
      ws.send(JSON.stringify({
          printerIp: "************", // Update with your actual printer IP
          printerPort: 9100,
          text: "\x1B\x70\x00\x19\xFA", // Cash drawer open command
          isPrintLogo: false // No logo, only triggering drawer
      }));
  };

  ws.onmessage = (event) => {
      console.log("📩 Server Response:", event.data);
  };

  ws.onerror = (error) => {
      console.error("❌ WebSocket Error:", error);
  };

  ws.onclose = () => {
      console.log("🔌 WebSocket connection closed.");
  };
}

function printWithWs(paramsArray = []) {
  try {
    // console.log("Print Data:", paramsArray);
    
    const ipPrinters = {
      // cashier: "************", // for receipt only
      cashier: "************",
      captainOrder: "************", // for table and bar
      kitchen: "************" // for kitchen and checker
    };

    // test
    const ipPrinters_dev = {
      cashier: "************", // for receipt only
      captainOrder: "************", // for table and bar
      kitchen: "************" // for kitchen and checker
    };

    const ipPrinters_used = ipPrinters
    
    // Extract order ID from the first object (assuming all share the same order ID)
    const idOrder = paramsArray.idOrder || null;
    if (!idOrder) {
      console.error("No order ID provided");
      return;
    }

    // Fetch order invoice data via AJAX (only once)
    $.ajax({
      url: basicinfo.baseurl + ( paramsArray.fetchUrl || "ordermanage/order/getInvoice" ),
      type: "GET",
      data: { idOrder: idOrder },
      dataType: "json",
      success: function(response) {
        if (response.status) {
          console.log("Invoice Data:", response.data);

          let isAllowedKitchen = false;
          let isAllowedBar = false;
          const isDineIn = response.data.orderDetail.customer_type === "Dine In" ? true : false;

          // Check for kitchen and bar access
          let urut = 0;
          const itemList = response.data.orderItem || response.data.latestBatch.batch_items
          itemList.forEach(item => {
            urut += 1
            console.log("food_kitchenid item " + urut + ":", item.food_kitchenid);
            const kitchenId = Number(item.food_kitchenid);
            if (kitchenId === 1) {
              isAllowedKitchen = true;
              console.log(urut + ") isAllowedKitchen: " + isAllowedKitchen);
            }
            if (kitchenId === 11) {
              isAllowedBar = true;
              console.log(urut + ") isAllowedBar: " + isAllowedBar);
            }

          });
          
          // Loop through each print request and process accordingly
          paramsArray.printOption.forEach(($printParams) => {
            console.log("isAllowedKitchen: "+isAllowedKitchen+"   isAllowedBar: "+isAllowedBar);
            // check bar and kitchen print
            if (
              ["bar", "barUpdate"].includes($printParams.slipType) && !isAllowedBar ||
              ["kitchen", "kitchenUpdate"].includes($printParams.slipType) && !isAllowedKitchen
            ) {          
              console.log(`Skipping ${$printParams.slipType} print due to restrictions.`);
              return; // Skip this iteration
            }

            if ( isDineIn && paramsArray.callFrom === "submitmultiplepay" && ($printParams.slipType === "bar" || $printParams.slipType === "kitchen") ) {
              return;
            }

            const receiptText = generateTextReceipt({
              type: $printParams.slipType,
              dataToPrint: response.data
            });
            
            const printerIp = ipPrinters_used[$printParams.printerCode];
            if (printerIp) {
              WsPrint(printerIp, $printParams.isPrintLogo, receiptText);
            } else {
              alert("Invalid printer code:", $printParams.printerCode);
            }
          });
        } else {
          alert("Invoice not found. Coz: " + response.message);
        }
      }, error: function(xhr, status, error) {
        console.error("AJAX Error:", error);
        alert("Failed to fetch invoice data.");
      }
    });
  } catch (error) {
    alert("Error: " + error.message);
    console.error(error);
  }
}

// tombol pesan
function placeorder() {
  var ctypeid = $("#ctypeid").val();
  var waiter = "";
  var isdelivary = "";
  var thirdinvoiceid = "";
  var tableid = "";
  var customer_name = $("#customer_name").val();
  var cardtype = 4;
  var isonline = 0;
  var order_date = $("#order_date").val();
  var grandtotal = $("#grandtotal").val();
  var customernote = "";
  var invoice_discount = $("#invoice_discount").val();
  var service_charge = $("#service_charge").val();
  var vat = $("#vat").val();
  var orggrandTotal = $("#subtotal").val();
  var isonline = $("#isonline").val();
  var isitem = $("#totalitem").val();
  var cookedtime = $("#cookedtime").val();
  var multiplletaxvalue = $("#multiplletaxvalue").val();
  var csrf = $("#csrfhashresarvation").val();
  var errormessage = "";
  if (customer_name == "") {
    errormessage = errormessage + "<span>Please Select Customer Name.</span>";
    alert("Please Select Customer Name!!!");
    return false;
  }
  if (ctypeid == "") {
    errormessage = errormessage + "<span>Please Select Customer Type.</span>";
    alert("Please Select Customer Type!!!");
    return false;
  }
  if (isitem == "" || isitem == 0) {
    errormessage = errormessage + "<span>Please add Some Food</span>";
    alert("Please add Some Food!!!");
    return false;
  }
  if (ctypeid == 3) {
    var isdelivary = $("#delivercom").val();
    var thirdinvoiceid = $("#thirdinvoiceid").val();
    if (isdelivary == "") {
      errormessage = errormessage + "<span>Please Select Customer Type.</span>";
      alert("Please Select Delivar Company!!!");
      return false;
    }
  } else if (ctypeid == 4 || ctypeid == 2) {
    if (possetting.waiter == 1) {
      var waiter = $("#waiter").val();
      if (waiter == "") {
        errormessage = errormessage + "<span>Please Select Waiter.</span>";
        alert("Please Select Waiter!!!");
        return false;
      }
    }
  } else {
    var waiter = $("#waiter").val();
    var tableid = $("#tableid").val();
    var table_member_multi = $("#table_member_multi").val();
    var table_member_multi_person = $("#table_member_multi_person").val();
    var table_member = $("#table_member").val(); //table member 02/11
    if (possetting.waiter == 1) {
      if (waiter == "") {
        errormessage = errormessage + "<span>Please Select Waiter.</span>";
        $("#waiter").select2("open");
        return false;
      }
    }
    if (possetting.tableid == 1) {
      if (tableid == "") {
        $("#tableid").select2("open");
        toastr.warning("Please Select Table", "Warning");
        return false;
      }
      if (possetting.tablemaping == 1) {
        if (tableid == "" || !$.isNumeric($("#table_person").val())) {
          toastr.warning("Please Select Table or number person", "Warning");
          return false;
        }
      }
    }
  }

  if (errormessage == "") {
    order_date = encodeURIComponent(order_date);
    customernote = encodeURIComponent(customernote);
    var errormessage =
      '<span style="color:#060;">Signup Completed Successfully.</span>';
    
    var dataString = `customer_name=${customer_name}
      &ctypeid=${ctypeid}
      &waiter=${waiter}
      &tableid=${tableid}
      &card_type=${cardtype}
      &isonline=${isonline}
      &order_date=${order_date}
      &grandtotal=${grandtotal}
      &customernote=${customernote}
      &invoice_discount=${invoice_discount}
      &service_charge=${service_charge}
      &vat=${vat}
      &subtotal=${orggrandTotal}
      &assigncard_terminal=
      &assignbank=
      &assignlastdigit=
      &delivercom=${isdelivary}
      &thirdpartyinvoice=${thirdinvoiceid}
      &cookedtime=${cookedtime}
      &tablemember=${table_member}
      &table_member_multi=${table_member_multi}
      &table_member_multi_person=${table_member_multi_person}
      &multiplletaxvalue=${multiplletaxvalue}
      &csrf_test_name=${csrf}`
    .replace(/\s+/g, ""); // Remove unnecessary spaces
  
    $.ajax({
      type: "POST",
      url: basicinfo.baseurl + "ordermanage/order/pos_order",
      data: dataString,
      success: function (data) {
        $("#addfoodlist").empty();
        $("#getitemp").val("0");
        $("#calvat").text("0");
        $("#vat").val("0");
        $("#invoice_discount").val("0");
        $("#caltotal").text("");
        $("#grandtotal").val("");
        $("#thirdinvoiceid").val("");
        $("#orggrandTotal").val("");
        $("#waiter").select2("data", null);
        $("#tableid").select2("data", null);
        $("#waiter").val("");

        $("#table_member").val("");
        $("#table_person").val(lang.person);
        $("#table_member_multi").val(0);
        $("#table_member_multi_person").val(0);

        var err = data;
        if (err == "error") {
          swal(
            {
              title: lang.ord_failed,
              text: lang.failed_msg,
              type: "warning",
              showCancelButton: true,
              confirmButtonColor: "#DD6B55",
              confirmButtonText: lang.yes + ", " + lang.cancel + "!",
              closeOnConfirm: true,
            },
            function () {}
          );
        } else {
          if (basicinfo.printtype == 1) {
            swal(
              {
                title: lang.ord_succ,
                text: "",
                type: "success",
                showCancelButton: false,
                confirmButtonColor: "#28a745",
                confirmButtonText: "Done",
                closeOnConfirm: true,
              },
              function () {}
            );
          } else {
            swal(
              {
                title: lang.ord_succ,
                text: "Print struk meja, dapur dan bar ???",
                type: "success",
                showCancelButton: true,
                confirmButtonColor: "#28a745",
                confirmButtonText: lang.yes,
                cancelButtonText: lang.no,
                closeOnConfirm: true,
                closeOnCancel: true,
              },
              function (isConfirm) {
                if (isConfirm) {
                  // indra
                  // printRawHtml(data);
                  
                  const idOrder = data
                  let printData = {
                    idOrder: idOrder,
                    printOption: [
                      {
                        slipType: "table",
                        printerCode: "captainOrder",
                        isPrintLogo: false
                      },
                      {
                        slipType: "kitchen",
                        printerCode: "kitchen",
                        isPrintLogo: false
                      },
                      {
                        slipType: "bar",
                        printerCode: "captainOrder",
                        isPrintLogo: false
                      }
                    ] 
                  }
        
                  printWithWs(printData)

                } else {
                  $("#waiter").select2("data", null);
                  $("#tableid").select2("data", null);
                  $("#waiter").val("");
                  $("#tableid").val("");
                }
              }
            );
          }
        }
      },
    });
  }
}

// payment dari pesan cepat, dll
function submitmultiplepay() {
  var thisForm = $("#paymodal-multiple-form");
  var inputval = parseFloat(0);
  var maintotalamount = $("#due-amount").text();

  $(".number").each(function () {
    var inputdata = parseFloat($(this).val());
    inputval = inputval + inputdata;
  });
  if (inputval < parseFloat(maintotalamount)) {
    setTimeout(function () {
      toastr.options = {
        closeButton: true,
        progressBar: true,
        showMethod: "slideDown",
        timeOut: 4000,
      };
      toastr.error("Pay full amount ", "Error");
    }, 100);
    return false;
  }
  var formdata = new FormData(thisForm[0]);

  $.ajax({
    type: "POST",
    url: basicinfo.baseurl + "ordermanage/order/paymultiple",
    data: formdata,
    processData: false,
    contentType: false,
    success: function (data) {

      console.log("berhasil bayar");
      var value = $("#get-order-flag").val();
      var idOrder = formdata.get("orderid");

      if (value == 1) {
        setTimeout(function () {
          toastr.options = {
            closeButton: true,
            progressBar: true,
            showMethod: "slideDown",
            timeOut: 4000,
          };
          toastr.success("payment taken successfully", "Success");
          $("#payprint_marge").modal("hide");
          $("#modal-ajaxview").empty();
          prevsltab.trigger("click");
        }, 100);
      } else {
        $("#payprint_marge").modal("hide");
        $("#modal-ajaxview").empty();
        if (basicinfo.printtype != 1) {
          // indra
          let printData = {
            idOrder: idOrder,
            callFrom: "submitmultiplepay",
            printOption: [
              {
                slipType: "receipt",
                printerCode: "cashier",
                isPrintLogo: true
              },
              {
                slipType: "kitchen",
                printerCode: "kitchen",
                isPrintLogo: false
              },
              {
                slipType: "bar",
                printerCode: "captainOrder",
                isPrintLogo: false
              }
            ] 
          }

          printWithWs(printData)
        }
        prevsltab.trigger("click");
      }
    },
  });
}

// print receipt
function printReceiptOnly (idOrder) {
  let printData = {
    idOrder: idOrder,
    printOption: [
      {
        slipType: "receipt",
        printerCode: "cashier",
        isPrintLogo: true
      }
    ] 
  }

  printWithWs(printData)
}

// print meja
function printMejaOnly (idOrder) {
  let printData = {
    idOrder: idOrder,
    printOption: [
      {
        slipType: "table",
        printerCode: "captainOrder",
        isPrintLogo: false
      }
    ] 
  }

  printWithWs(printData)
}

// print bill
function printBillOnly (idOrder) {
  let printData = {
    idOrder: idOrder,
    printOption: [
      {
        slipType: "bill",
        printerCode: "cashier",
        isPrintLogo: true
      }
    ] 
  }

  printWithWs(printData)
}

// print dapur
function printKitchenOnly (idOrder) {
  let printData = {
    idOrder: idOrder,
    printOption: [
      {
        slipType: "kitchen",
        printerCode: "captainOrder",
        isPrintLogo: false
      }
    ] 
  }

  printWithWs(printData)
}

function printBarOnly (idOrder) {
  let printData = {
    idOrder: idOrder,
    printOption: [
      {
        slipType: "bar",
        printerCode: "captainOrder",
        isPrintLogo: false
      }
    ] 
  }

  printWithWs(printData)
}

function printSlip({ idOrder, slipType, printerCode, isPrintLogo = false }) {
  printWithWs({
    idOrder,
    printOption: [{ slipType, printerCode, isPrintLogo }]
  });
}

// print bill
function printUpdateOrderItem (idOrder) {
  let printData = {
    fetchUrl: "ordermanage/order/doGetOrderItemTempData",
    idOrder: idOrder,
    printOption: [
      {
        slipType: "tableUpdate",
        printerCode: "captainOrder",
        isPrintLogo: false
      },
      {
        slipType: "kitchenUpdate",
        printerCode: "kitchen",
        isPrintLogo: false
      },
      {
        slipType: "barUpdate",
        printerCode: "captainOrder",
        isPrintLogo: false
      }
      // {
      //   slipType: "cancelItem",
      //   printerCode: "cashier",
      //   isPrintLogo: false
      // },
    ] 
  }

  printWithWs(printData)
}

// modal bill preview
function billPreview(paramsArray = []) {
  try {
    console.log("Preview Bill Data:", paramsArray);

    // Extract order ID
    const idOrder = paramsArray.idOrder || null;
    if (!idOrder) {
      console.error("No order ID provided");
      return;
    }

    // Fetch order invoice data via AJAX (only once)
    $.ajax({
      url: basicinfo.baseurl + (paramsArray.fetchUrl || "ordermanage/order/getInvoice"),
      type: "GET",
      data: { idOrder: idOrder },
      dataType: "json",
      success: function(response) {
        if (response.status) {
          console.log("Invoice Data:", response.data);
          const orderDetail = response.data.orderDetail;
          const orderItem = response.data.orderItemsBill;
        
          // Generate bill preview with aligned details
          let billContent = `
            <table style="width:100%; border-collapse: collapse;">
              <tr>
                <td style="width: 25%; font-weight: bold; text-align: left;">No Nota</td>
                <td style="width: 2%; text-align: left;">:</td>
                <td style="width: 73%; text-align: left;">${orderDetail.saleinvoice}</td>
              </tr>
              <tr>
                <td style="font-weight: bold; text-align: left;">Pelanggan</td>
                <td style="text-align: left;">:</td>
                <td style="text-align: left;">${orderDetail.customer_name || "-"}</td>
              </tr>
              <tr>
                <td style="font-weight: bold; text-align: left;">Jenis Transaksi</td>
                <td style="text-align: left;">:</td>
                <td style="text-align: left;">${orderDetail.customer_type || "-"}</td>
              </tr>
              <tr>
                <td style="font-weight: bold; text-align: left;">Nomor Meja</td>
                <td style="text-align: left;">:</td>
                <td style="text-align: left;">${orderDetail.table_name || "-"}</td>
              </tr>
            </table>
            <hr style="margin: 10px 0;">`;
        
          // Table header with left alignment
          billContent += `
            <table style="width: 100%; border-collapse: collapse;">
              <thead>
                <tr>
                  <th style="text-align: left; border-bottom: 2px solid black; padding: 5px;">Item</th>
                  <th style="text-align: right; border-bottom: 2px solid black; padding: 5px;">Qty</th>
                  <th style="text-align: right; border-bottom: 2px solid black; padding: 5px;">Price</th>
                  <th style="text-align: right; border-bottom: 2px solid black; padding: 5px;">Total</th>
                </tr>
              </thead>
              <tbody>`;
        
          let totalAmount = 0;
          orderItem.forEach(item => {
            let itemName = `${item.food_ProductName || "Item Tidak Diketahui"}`;
            let itemQty = thousand_separator(item.menuqty || 0);
            let itemPrice = thousand_separator(item.price || 0);
            let itemTotal = thousand_separator((item.price * item.menuqty) || 0);
        
            totalAmount += item.price * item.menuqty;
            billContent += `<tr>
              <td style="text-align: left; padding: 5px;">${itemName}</td>
              <td style="text-align: center; padding: 5px;">${itemQty}</td>
              <td style="text-align: right; padding: 5px;">Rp ${itemPrice}</td>
              <td style="text-align: right; padding: 5px;">Rp ${itemTotal}</td>
            </tr>`;
          });
        
          billContent += `</tbody></table>`;
          billContent += `<hr style="margin: 10px 0;"><h3 style="text-align: right;"><strong>Total:</strong> Rp `+ thousand_separator(totalAmount) +`</h3>`;
        
          // Inject content into modal and show it
          // $("#billModal .modal-body").html(billContent);
          // $("#billModal").modal("show");
          showBillModal(idOrder, billContent)
        } else {
          alert("Invoice not found. Coz: " + response.message);
        }
        
      },
      error: function(xhr, status, error) {
        console.error("AJAX Error:", error);
        alert("Failed to fetch invoice data.");
      }
    });
  } catch (error) {
    alert("Error: " + error.message);
    console.error(error);
  }
}

// bill prev modal show
function showBillModal(idOrder, billContent) {
  $("#billModal .modal-body").html(billContent);
  
  // Update the button inside the modal-footer
  $("#billModal .modal-footer a").attr("onclick", `closeAndProcess(${idOrder}, 1)`);

  $("#billModal").modal("show");
}

// get updated item
function getUpdatedItem(idOrder) {
  $.ajax({
    url: basicinfo.baseurl + "ordermanage/order/doGetOrderItemTempData",
    type: 'GET',
    data: { idOrder: idOrder },
    dataType: 'json',
    success: function(response) {
        if (response.status) {
          console.log("doGetOrderItemTempData hit successfully!");
            console.log("Success:", response.data);
        } else {
            console.error("Error doGetOrderItemTempData:", response.message);
        }
    },
    error: function(xhr, status, error) {
        console.error("AJAX Error:", error);
        console.log("Failed to hit doGetOrderItemTempData.");
    }
  });
}
