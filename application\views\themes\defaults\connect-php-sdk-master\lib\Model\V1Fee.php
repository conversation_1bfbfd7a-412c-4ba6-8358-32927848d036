<?php
/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace SquareConnect\Model;

use \ArrayAccess;
/**
 * V1Fee Class Doc Comment
 *
 * @category Class
 * @package  SquareConnect
 * <AUTHOR> Inc.
 * @license  http://www.apache.org/licenses/LICENSE-2.0 Apache License v2
 * @link     https://squareup.com/developers
 */
class V1Fee implements ArrayAccess
{
    /**
      * Array of property to type mappings. Used for (de)serialization 
      * @var string[]
      */
    static $swaggerTypes = array(
        'id' => 'string',
        'name' => 'string',
        'rate' => 'string',
        'calculation_phase' => 'string',
        'adjustment_type' => 'string',
        'applies_to_custom_amounts' => 'bool',
        'enabled' => 'bool',
        'inclusion_type' => 'string',
        'type' => 'string'
    );
  
    /** 
      * Array of attributes where the key is the local name, and the value is the original name
      * @var string[] 
      */
    static $attributeMap = array(
        'id' => 'id',
        'name' => 'name',
        'rate' => 'rate',
        'calculation_phase' => 'calculation_phase',
        'adjustment_type' => 'adjustment_type',
        'applies_to_custom_amounts' => 'applies_to_custom_amounts',
        'enabled' => 'enabled',
        'inclusion_type' => 'inclusion_type',
        'type' => 'type'
    );
  
    /**
      * Array of attributes to setter functions (for deserialization of responses)
      * @var string[]
      */
    static $setters = array(
        'id' => 'setId',
        'name' => 'setName',
        'rate' => 'setRate',
        'calculation_phase' => 'setCalculationPhase',
        'adjustment_type' => 'setAdjustmentType',
        'applies_to_custom_amounts' => 'setAppliesToCustomAmounts',
        'enabled' => 'setEnabled',
        'inclusion_type' => 'setInclusionType',
        'type' => 'setType'
    );
  
    /**
      * Array of attributes to getter functions (for serialization of requests)
      * @var string[]
      */
    static $getters = array(
        'id' => 'getId',
        'name' => 'getName',
        'rate' => 'getRate',
        'calculation_phase' => 'getCalculationPhase',
        'adjustment_type' => 'getAdjustmentType',
        'applies_to_custom_amounts' => 'getAppliesToCustomAmounts',
        'enabled' => 'getEnabled',
        'inclusion_type' => 'getInclusionType',
        'type' => 'getType'
    );
  
    /**
      * $id The fee's unique ID.
      * @var string
      */
    protected $id;
    /**
      * $name The fee's name.
      * @var string
      */
    protected $name;
    /**
      * $rate The rate of the fee, as a string representation of a decimal number. A value of 0.07 corresponds to a rate of 7%.
      * @var string
      */
    protected $rate;
    /**
      * $calculation_phase Forthcoming
      * @var string
      */
    protected $calculation_phase;
    /**
      * $adjustment_type The type of adjustment the fee applies to a payment. Currently, this value is TAX for all fees.
      * @var string
      */
    protected $adjustment_type;
    /**
      * $applies_to_custom_amounts If true, the fee applies to custom amounts entered into Square Register that are not associated with a particular item.
      * @var bool
      */
    protected $applies_to_custom_amounts;
    /**
      * $enabled If true, the fee is applied to all appropriate items. If false, the fee is not applied at all.
      * @var bool
      */
    protected $enabled;
    /**
      * $inclusion_type Whether the fee is ADDITIVE or INCLUSIVE.
      * @var string
      */
    protected $inclusion_type;
    /**
      * $type In countries with multiple classifications for sales taxes, indicates which classification the fee falls under. Currently relevant only to Canadian merchants.
      * @var string
      */
    protected $type;

    /**
     * Constructor
     * @param mixed[] $data Associated array of property value initializing the model
     */
    public function __construct(array $data = null)
    {
        if ($data != null) {
            if (isset($data["id"])) {
              $this->id = $data["id"];
            } else {
              $this->id = null;
            }
            if (isset($data["name"])) {
              $this->name = $data["name"];
            } else {
              $this->name = null;
            }
            if (isset($data["rate"])) {
              $this->rate = $data["rate"];
            } else {
              $this->rate = null;
            }
            if (isset($data["calculation_phase"])) {
              $this->calculation_phase = $data["calculation_phase"];
            } else {
              $this->calculation_phase = null;
            }
            if (isset($data["adjustment_type"])) {
              $this->adjustment_type = $data["adjustment_type"];
            } else {
              $this->adjustment_type = null;
            }
            if (isset($data["applies_to_custom_amounts"])) {
              $this->applies_to_custom_amounts = $data["applies_to_custom_amounts"];
            } else {
              $this->applies_to_custom_amounts = null;
            }
            if (isset($data["enabled"])) {
              $this->enabled = $data["enabled"];
            } else {
              $this->enabled = null;
            }
            if (isset($data["inclusion_type"])) {
              $this->inclusion_type = $data["inclusion_type"];
            } else {
              $this->inclusion_type = null;
            }
            if (isset($data["type"])) {
              $this->type = $data["type"];
            } else {
              $this->type = null;
            }
        }
    }
    /**
     * Gets id
     * @return string
     */
    public function getId()
    {
        return $this->id;
    }
  
    /**
     * Sets id
     * @param string $id The fee's unique ID.
     * @return $this
     */
    public function setId($id)
    {
        $this->id = $id;
        return $this;
    }
    /**
     * Gets name
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }
  
    /**
     * Sets name
     * @param string $name The fee's name.
     * @return $this
     */
    public function setName($name)
    {
        $this->name = $name;
        return $this;
    }
    /**
     * Gets rate
     * @return string
     */
    public function getRate()
    {
        return $this->rate;
    }
  
    /**
     * Sets rate
     * @param string $rate The rate of the fee, as a string representation of a decimal number. A value of 0.07 corresponds to a rate of 7%.
     * @return $this
     */
    public function setRate($rate)
    {
        $this->rate = $rate;
        return $this;
    }
    /**
     * Gets calculation_phase
     * @return string
     */
    public function getCalculationPhase()
    {
        return $this->calculation_phase;
    }
  
    /**
     * Sets calculation_phase
     * @param string $calculation_phase Forthcoming
     * @return $this
     */
    public function setCalculationPhase($calculation_phase)
    {
        $this->calculation_phase = $calculation_phase;
        return $this;
    }
    /**
     * Gets adjustment_type
     * @return string
     */
    public function getAdjustmentType()
    {
        return $this->adjustment_type;
    }
  
    /**
     * Sets adjustment_type
     * @param string $adjustment_type The type of adjustment the fee applies to a payment. Currently, this value is TAX for all fees.
     * @return $this
     */
    public function setAdjustmentType($adjustment_type)
    {
        $this->adjustment_type = $adjustment_type;
        return $this;
    }
    /**
     * Gets applies_to_custom_amounts
     * @return bool
     */
    public function getAppliesToCustomAmounts()
    {
        return $this->applies_to_custom_amounts;
    }
  
    /**
     * Sets applies_to_custom_amounts
     * @param bool $applies_to_custom_amounts If true, the fee applies to custom amounts entered into Square Register that are not associated with a particular item.
     * @return $this
     */
    public function setAppliesToCustomAmounts($applies_to_custom_amounts)
    {
        $this->applies_to_custom_amounts = $applies_to_custom_amounts;
        return $this;
    }
    /**
     * Gets enabled
     * @return bool
     */
    public function getEnabled()
    {
        return $this->enabled;
    }
  
    /**
     * Sets enabled
     * @param bool $enabled If true, the fee is applied to all appropriate items. If false, the fee is not applied at all.
     * @return $this
     */
    public function setEnabled($enabled)
    {
        $this->enabled = $enabled;
        return $this;
    }
    /**
     * Gets inclusion_type
     * @return string
     */
    public function getInclusionType()
    {
        return $this->inclusion_type;
    }
  
    /**
     * Sets inclusion_type
     * @param string $inclusion_type Whether the fee is ADDITIVE or INCLUSIVE.
     * @return $this
     */
    public function setInclusionType($inclusion_type)
    {
        $this->inclusion_type = $inclusion_type;
        return $this;
    }
    /**
     * Gets type
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }
  
    /**
     * Sets type
     * @param string $type In countries with multiple classifications for sales taxes, indicates which classification the fee falls under. Currently relevant only to Canadian merchants.
     * @return $this
     */
    public function setType($type)
    {
        $this->type = $type;
        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     * @param  integer $offset Offset 
     * @return boolean
     */
    public function offsetExists($offset)
    {
        return isset($this->$offset);
    }
  
    /**
     * Gets offset.
     * @param  integer $offset Offset 
     * @return mixed 
     */
    public function offsetGet($offset)
    {
        return $this->$offset;
    }
  
    /**
     * Sets value based on offset.
     * @param  integer $offset Offset 
     * @param  mixed   $value  Value to be set
     * @return void
     */
    public function offsetSet($offset, $value)
    {
        $this->$offset = $value;
    }
  
    /**
     * Unsets offset.
     * @param  integer $offset Offset 
     * @return void
     */
    public function offsetUnset($offset)
    {
        unset($this->$offset);
    }
  
    /**
     * Gets the string presentation of the object
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) {
            return json_encode(\SquareConnect\ObjectSerializer::sanitizeForSerialization($this), JSON_PRETTY_PRINT);
        } else {
            return json_encode(\SquareConnect\ObjectSerializer::sanitizeForSerialization($this));
        }
    }
}
