<?php
// Test script to debug the sellrpt page issue
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Testing sellrpt page components</h2>";

// Test 1: Check if we can include the basic CI files
echo "<h3>1. Testing CI Bootstrap</h3>";
try {
    define('BASEPATH', 'application/');
    require_once 'application/config/database.php';
    echo "<p style='color: green;'>✓ Database config loaded</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
}

// Test 2: Check database connection
echo "<h3>2. Testing Database Connection</h3>";
try {
    $host = $db['default']['hostname'];
    $user = $db['default']['username'];
    $pass = $db['default']['password'];
    $database = $db['default']['database'];
    
    $connection = new mysqli($host, $user, $pass, $database);
    
    if ($connection->connect_error) {
        throw new Exception("Connection failed: " . $connection->connect_error);
    }
    
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    // Test 3: Check if required tables exist
    echo "<h3>3. Testing Required Tables</h3>";
    
    $tables = ['customer_order', 'customer_info', 'bill', 'payment_method'];
    foreach ($tables as $table) {
        $result = $connection->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows > 0) {
            echo "<p style='color: green;'>✓ Table '$table' exists</p>";
        } else {
            echo "<p style='color: red;'>✗ Table '$table' missing</p>";
        }
    }
    
    // Test 4: Check if we can run the dropdown queries
    echo "<h3>4. Testing Dropdown Queries</h3>";
    
    // Test invoice dropdown query
    $invoice_query = "SELECT DISTINCT a.saleinvoice FROM customer_order a WHERE a.order_status = 4 AND a.saleinvoice IS NOT NULL AND a.saleinvoice != '' ORDER BY a.saleinvoice ASC LIMIT 5";
    $result = $connection->query($invoice_query);
    if ($result) {
        echo "<p style='color: green;'>✓ Invoice dropdown query works (" . $result->num_rows . " records)</p>";
    } else {
        echo "<p style='color: red;'>✗ Invoice dropdown query failed: " . $connection->error . "</p>";
    }
    
    // Test customer dropdown query
    $customer_query = "SELECT DISTINCT b.customer_id, b.customer_name FROM customer_order a LEFT JOIN customer_info b ON a.customer_id = b.customer_id WHERE a.order_status = 4 AND b.customer_name IS NOT NULL AND b.customer_name != '' ORDER BY b.customer_name ASC LIMIT 5";
    $result = $connection->query($customer_query);
    if ($result) {
        echo "<p style='color: green;'>✓ Customer dropdown query works (" . $result->num_rows . " records)</p>";
    } else {
        echo "<p style='color: red;'>✗ Customer dropdown query failed: " . $connection->error . "</p>";
    }
    
    $connection->close();
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database error: " . $e->getMessage() . "</p>";
}

// Test 5: Check if form helper is available
echo "<h3>5. Testing Form Helper Functions</h3>";
if (function_exists('form_dropdown')) {
    echo "<p style='color: green;'>✓ form_dropdown function available</p>";
} else {
    echo "<p style='color: red;'>✗ form_dropdown function not available</p>";
}

echo "<h3>6. Recommendations</h3>";
echo "<ul>";
echo "<li>If database connection failed, check your database credentials in application/config/database.php</li>";
echo "<li>If tables are missing, make sure you're connected to the correct database</li>";
echo "<li>If queries failed, there might be data type issues or missing columns</li>";
echo "<li>Check your web server error logs for more specific PHP errors</li>";
echo "</ul>";
?>
