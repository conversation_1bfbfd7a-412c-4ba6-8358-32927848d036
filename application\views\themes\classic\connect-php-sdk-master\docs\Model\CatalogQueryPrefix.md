# CatalogQueryPrefix

## Properties
Name | Getter | Setter | Type | Description | Notes
------------ | ------------- | ------------- | ------------- | ------------- | -------------
**attribute_name** | getAttributeName() | setAttributeName($value) | **string** | The name of the attribute to be searched. | 
**attribute_prefix** | getAttributePrefix() | setAttributePrefix($value) | **string** | The desired prefix of the search attribute value. | 

Note: All properties are protected and only accessed via getters and setters.

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)

