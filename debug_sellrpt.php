<?php
// Debug script specifically for sellrpt page
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Debug: Sales Report Page (sellrpt)</h2>";

// Simulate the sellrpt controller method step by step
echo "<h3>Step 1: Testing Basic PHP</h3>";
echo "<p style='color: green;'>✓ PHP is working</p>";

echo "<h3>Step 2: Testing Database Connection</h3>";
try {
    require_once 'application/config/database.php';
    $host = $db['default']['hostname'];
    $user = $db['default']['username'];
    $pass = $db['default']['password'];
    $database = $db['default']['database'];
    
    $connection = new mysqli($host, $user, $pass, $database);
    
    if ($connection->connect_error) {
        throw new Exception("Connection failed: " . $connection->connect_error);
    }
    
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    echo "<h3>Step 3: Testing Invoice Dropdown Query</h3>";
    $invoice_sql = "SELECT DISTINCT a.saleinvoice FROM customer_order a WHERE a.order_status = 4 AND a.saleinvoice != '' AND a.saleinvoice IS NOT NULL ORDER BY a.saleinvoice ASC LIMIT 10";
    $result = $connection->query($invoice_sql);
    
    if ($result) {
        echo "<p style='color: green;'>✓ Invoice query successful (" . $result->num_rows . " records)</p>";
        if ($result->num_rows > 0) {
            echo "<ul>";
            while ($row = $result->fetch_assoc()) {
                echo "<li>" . htmlspecialchars($row['saleinvoice']) . "</li>";
            }
            echo "</ul>";
        }
    } else {
        echo "<p style='color: red;'>✗ Invoice query failed: " . $connection->error . "</p>";
    }
    
    echo "<h3>Step 4: Testing Customer Dropdown Query</h3>";
    $customer_sql = "SELECT DISTINCT b.customer_id, b.customer_name FROM customer_order a LEFT JOIN customer_info b ON a.customer_id = b.customer_id WHERE a.order_status = 4 AND b.customer_name != '' AND b.customer_name IS NOT NULL ORDER BY b.customer_name ASC LIMIT 10";
    $result = $connection->query($customer_sql);
    
    if ($result) {
        echo "<p style='color: green;'>✓ Customer query successful (" . $result->num_rows . " records)</p>";
        if ($result->num_rows > 0) {
            echo "<ul>";
            while ($row = $result->fetch_assoc()) {
                echo "<li>ID: " . $row['customer_id'] . " - " . htmlspecialchars($row['customer_name']) . "</li>";
            }
            echo "</ul>";
        }
    } else {
        echo "<p style='color: red;'>✗ Customer query failed: " . $connection->error . "</p>";
    }
    
    echo "<h3>Step 5: Testing Payment Method Query</h3>";
    $payment_sql = "SELECT * FROM payment_method WHERE is_active = 1";
    $result = $connection->query($payment_sql);
    
    if ($result) {
        echo "<p style='color: green;'>✓ Payment method query successful (" . $result->num_rows . " records)</p>";
    } else {
        echo "<p style='color: red;'>✗ Payment method query failed: " . $connection->error . "</p>";
    }
    
    $connection->close();
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database error: " . $e->getMessage() . "</p>";
}

echo "<h3>Step 6: Testing File Permissions</h3>";
$files_to_check = [
    'application/modules/report/controllers/Reports.php',
    'application/modules/report/models/Report_model.php',
    'application/modules/report/views/salereportfrm.php',
    'application/modules/report/assets/js/salereportfrm.js',
    'application/modules/report/assets/js/salereportfrm_script.js'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        if (is_readable($file)) {
            echo "<p style='color: green;'>✓ $file is readable</p>";
        } else {
            echo "<p style='color: red;'>✗ $file is not readable</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ $file does not exist</p>";
    }
}

echo "<h3>Recommendations:</h3>";
echo "<ul>";
echo "<li>If database queries failed, check your database structure</li>";
echo "<li>If files are not readable, check file permissions</li>";
echo "<li>Check your web server error logs for more specific errors</li>";
echo "<li>Try accessing the page directly: <a href='report/reports/sellrpt'>report/reports/sellrpt</a></li>";
echo "</ul>";
?>
