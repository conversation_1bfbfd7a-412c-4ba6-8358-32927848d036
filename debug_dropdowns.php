<?php
// Debug script to test the new dropdown methods
// Place this file in the root directory and access it via browser

// Include CodeIgniter bootstrap
require_once 'index.php';

// Get CI instance
$CI =& get_instance();
$CI->load->model('report/Report_model', 'report_model');

echo "<h2>Debug: Testing New Dropdown Methods</h2>";

echo "<h3>1. Testing get_invoice_dropdown():</h3>";
try {
    $invoices = $CI->report_model->get_invoice_dropdown();
    echo "<pre>";
    print_r($invoices);
    echo "</pre>";
    echo "<p>Status: <span style='color: green;'>SUCCESS</span></p>";
} catch (Exception $e) {
    echo "<p>Status: <span style='color: red;'>ERROR</span></p>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
}

echo "<h3>2. Testing get_customer_dropdown():</h3>";
try {
    $customers = $CI->report_model->get_customer_dropdown();
    echo "<pre>";
    print_r($customers);
    echo "</pre>";
    echo "<p>Status: <span style='color: green;'>SUCCESS</span></p>";
} catch (Exception $e) {
    echo "<p>Status: <span style='color: red;'>ERROR</span></p>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
}

echo "<h3>3. Testing salereport() method with new parameters:</h3>";
try {
    $today = date('Y-m-d');
    $result = $CI->report_model->salereport($today, $today, null, null, null);
    echo "<p>Records found: " . count($result) . "</p>";
    echo "<p>Status: <span style='color: green;'>SUCCESS</span></p>";
} catch (Exception $e) {
    echo "<p>Status: <span style='color: red;'>ERROR</span></p>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
}

echo "<h3>4. Database Connection Test:</h3>";
try {
    $query = $CI->db->query("SELECT COUNT(*) as count FROM customer_order WHERE order_status = 4");
    $result = $query->row();
    echo "<p>Total completed orders: " . $result->count . "</p>";
    echo "<p>Status: <span style='color: green;'>SUCCESS</span></p>";
} catch (Exception $e) {
    echo "<p>Status: <span style='color: red;'>ERROR</span></p>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?>
