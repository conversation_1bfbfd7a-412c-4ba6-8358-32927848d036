<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Select2 Implementation</title>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>Sales Report - Select2 Implementation Test</h2>
        
        <form class="row g-3">
            <div class="col-md-3">
                <label for="from_date" class="form-label">Start Date</label>
                <input type="date" class="form-control" id="from_date" value="2025-07-15">
            </div>
            
            <div class="col-md-3">
                <label for="to_date" class="form-label">End Date</label>
                <input type="date" class="form-control" id="to_date" value="2025-07-15">
            </div>
            
            <div class="col-md-3">
                <label for="invoice_no" class="form-label">Invoice Number</label>
                <select class="form-control" id="invoice_no">
                    <option value="">Select Invoice</option>
                    <option value="101304">101304</option>
                    <option value="101336">101336</option>
                    <option value="101337">101337</option>
                    <option value="101338">101338</option>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="customer_id" class="form-label">Customer Name</label>
                <select class="form-control" id="customer_id">
                    <option value="">Select Customer</option>
                    <option value="1">Dewi</option>
                    <option value="2">Ahmad</option>
                    <option value="3">Sari</option>
                    <option value="4">Budi</option>
                </select>
            </div>
            
            <div class="col-12">
                <button type="button" class="btn btn-success" onclick="testSearch()">Search</button>
                <button type="button" class="btn btn-warning">Print</button>
            </div>
        </form>
        
        <div class="mt-4">
            <h4>Search Parameters:</h4>
            <div id="search-results"></div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Initialize Select2 for dropdowns
            $("#invoice_no").select2({
                placeholder: "Select Invoice",
                allowClear: true
            });
            
            $("#customer_id").select2({
                placeholder: "Select Customer",
                allowClear: true
            });
        });
        
        function testSearch() {
            var from_date = $('#from_date').val();
            var to_date = $('#to_date').val();
            var invoice_no = $('#invoice_no').val();
            var customer_id = $('#customer_id').val();
            
            var results = `
                <strong>Search Parameters:</strong><br>
                From Date: ${from_date}<br>
                To Date: ${to_date}<br>
                Invoice Number: ${invoice_no || 'Not selected'}<br>
                Customer ID: ${customer_id || 'Not selected'}
            `;
            
            $('#search-results').html(results);
        }
    </script>
</body>
</html>
