[{"name": "guzzlehttp/guzzle", "version": "6.3.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "407b0cb880ace85c9b63c5f9551db498cb2d50ba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/407b0cb880ace85c9b63c5f9551db498cb2d50ba", "reference": "407b0cb880ace85c9b63c5f9551db498cb2d50ba", "shasum": ""}, "require": {"guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.4", "php": ">=5.5"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.0"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "time": "2018-04-22T15:46:56+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "6.3-dev"}}, "installation-source": "dist", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"]}, {"name": "guzzlehttp/promises", "version": "v1.3.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/a59da6cf61d80060647ff4d3eb2c03a2bc694646", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646", "shasum": ""}, "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "^4.0"}, "time": "2016-12-20T10:07:11+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"]}, {"name": "guzzlehttp/psr7", "version": "1.4.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "f5b8a8512e2b58b0071a7280e39f14f72e05d87c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/f5b8a8512e2b58b0071a7280e39f14f72e05d87c", "reference": "f5b8a8512e2b58b0071a7280e39f14f72e05d87c", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "time": "2017-03-20T17:10:46+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "request", "response", "stream", "uri", "url"]}, {"name": "lcobucci/jwt", "version": "3.2.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/lcobucci/jwt.git", "reference": "0b5930be73582369e10c4d4bb7a12bac927a203c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lcobucci/jwt/zipball/0b5930be73582369e10c4d4bb7a12bac927a203c", "reference": "0b5930be73582369e10c4d4bb7a12bac927a203c", "shasum": ""}, "require": {"ext-openssl": "*", "php": ">=5.5"}, "require-dev": {"mdanter/ecc": "~0.3.1", "mikey179/vfsstream": "~1.5", "phpmd/phpmd": "~2.2", "phpunit/php-invoker": "~1.1", "phpunit/phpunit": "~4.5", "squizlabs/php_codesniffer": "~2.3"}, "suggest": {"mdanter/ecc": "Required to use Elliptic Curves based algorithms."}, "time": "2017-09-01T08:23:26+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Lcobucci\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "developer"}], "description": "A simple library to work with JSON Web Token and JSON Web Signature", "keywords": ["JWS", "jwt"]}, {"name": "nexmo/client", "version": "1.3.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Nexmo/nexmo-php.git", "reference": "d6a10bd113e8b24b8d66f013f3159ac0786ae4fd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Nexmo/nexmo-php/zipball/d6a10bd113e8b24b8d66f013f3159ac0786ae4fd", "reference": "d6a10bd113e8b24b8d66f013f3159ac0786ae4fd", "shasum": ""}, "require": {"lcobucci/jwt": "^3.2", "php": ">=5.6", "********/client-implementation": "^1.0", "********/guzzle6-adapter": "^1.0", "zendframework/zend-diactoros": "^1.3"}, "require-dev": {"estahn/phpunit-json-assertions": "@stable", "********/mock-client": "^0.3.0", "phpunit/phpunit": "^5.3", "squizlabs/php_codesniffer": "^3.1"}, "time": "2018-05-01T12:35:27+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Nexmo\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://twitter.com/tjlytle", "role": "Developer"}], "description": "PHP Client for using Nexmo's API."}, {"name": "********/guzzle6-adapter", "version": "v1.1.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/********/guzzle6-adapter.git", "reference": "a56941f9dc6110409cfcddc91546ee97039277ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/********/guzzle6-adapter/zipball/a56941f9dc6110409cfcddc91546ee97039277ab", "reference": "a56941f9dc6110409cfcddc91546ee97039277ab", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.0", "php": ">=5.5.0", "********/httplug": "^1.0"}, "provide": {"********/async-client-implementation": "1.0", "********/client-implementation": "1.0"}, "require-dev": {"ext-curl": "*", "********/adapter-integration-tests": "^0.4"}, "time": "2016-05-10T06:13:32+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Http\\Adapter\\Guzzle6\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Guzzle 6 HTTP Adapter", "homepage": "http://httplug.io", "keywords": ["Guzzle", "http"]}, {"name": "********/httplug", "version": "v1.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/********/httplug.git", "reference": "1c6381726c18579c4ca2ef1ec1498fdae8bdf018"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/********/httplug/zipball/1c6381726c18579c4ca2ef1ec1498fdae8bdf018", "reference": "1c6381726c18579c4ca2ef1ec1498fdae8bdf018", "shasum": ""}, "require": {"php": ">=5.4", "********/promise": "^1.0", "psr/http-message": "^1.0"}, "require-dev": {"henrikbjorn/phpspec-code-coverage": "^1.0", "phpspec/phpspec": "^2.4"}, "time": "2016-08-31T08:30:17+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Eric <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "HTTPlug, the HTTP client abstraction for PHP", "homepage": "http://httplug.io", "keywords": ["client", "http"]}, {"name": "********/promise", "version": "v1.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/********/promise.git", "reference": "dc494cdc9d7160b9a09bd5573272195242ce7980"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/********/promise/zipball/dc494cdc9d7160b9a09bd5573272195242ce7980", "reference": "dc494cdc9d7160b9a09bd5573272195242ce7980", "shasum": ""}, "require-dev": {"henrikbjorn/phpspec-code-coverage": "^1.0", "phpspec/phpspec": "^2.4"}, "time": "2016-01-26T13:27:02+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Http\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Promise used for asynchronous HTTP requests", "homepage": "http://httplug.io", "keywords": ["promise"]}, {"name": "psr/http-message", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2016-08-06T14:39:51+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"]}, {"name": "zendframework/zend-diactoros", "version": "1.7.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/zendframework/zend-diactoros.git", "reference": "bf26aff803a11c5cc8eb7c4878a702c403ec67f1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-diactoros/zipball/bf26aff803a11c5cc8eb7c4878a702c403ec67f1", "reference": "bf26aff803a11c5cc8eb7c4878a702c403ec67f1", "shasum": ""}, "require": {"php": "^5.6 || ^7.0", "psr/http-message": "^1.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-dom": "*", "ext-libxml": "*", "phpunit/phpunit": "^5.7.16 || ^6.0.8", "zendframework/zend-coding-standard": "~1.0"}, "time": "2018-02-26T15:44:50+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.7.x-dev", "dev-develop": "1.8.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Zend\\Diactoros\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "description": "PSR HTTP Message implementations", "homepage": "https://github.com/zendframework/zend-diactoros", "keywords": ["http", "psr", "psr-7"]}]