{"name": "php-http/guzzle6-adapter", "description": "Guzzle 6 HTTP Adapter", "license": "MIT", "keywords": ["guzzle", "http"], "homepage": "http://httplug.io", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=5.5.0", "php-http/httplug": "^1.0", "guzzlehttp/guzzle": "^6.0"}, "require-dev": {"ext-curl": "*", "php-http/adapter-integration-tests": "^0.4"}, "provide": {"php-http/client-implementation": "1.0", "php-http/async-client-implementation": "1.0"}, "autoload": {"psr-4": {"Http\\Adapter\\Guzzle6\\": "src/"}}, "autoload-dev": {"psr-4": {"Http\\Adapter\\Guzzle6\\Tests\\": "tests/"}}, "scripts": {"test": "vendor/bin/phpunit", "test-ci": "vendor/bin/phpunit --coverage-text --coverage-clover=build/coverage.xml"}, "extra": {"branch-alias": {"dev-master": "1.2-dev"}}}