<?php return array(
    'root' => array(
        'name' => 'codeigniter/framework',
        'pretty_version' => 'dev-main',
        'version' => 'dev-main',
        'reference' => '94380fe3c09f9e0036b90071c98319cdbae99f1d',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'bacon/bacon-qr-code' => array(
            'pretty_version' => 'v3.0.1',
            'version' => '*******',
            'reference' => 'f9cc1f52b5a463062251d666761178dbdb6b544f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../bacon/bacon-qr-code',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'codeigniter/framework' => array(
            'pretty_version' => 'dev-main',
            'version' => 'dev-main',
            'reference' => '94380fe3c09f9e0036b90071c98319cdbae99f1d',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'container-interop/container-interop' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '^1.2.0',
            ),
        ),
        'dasprid/enum' => array(
            'pretty_version' => '1.0.6',
            'version' => '1.0.6.0',
            'reference' => '8dfd07c6d2cf31c8da90c53b83c026c7696dda90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dasprid/enum',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dompdf/dompdf' => array(
            'pretty_version' => 'v2.0.8',
            'version' => '2.0.8.0',
            'reference' => 'c20247574601700e1f7c8dab39310fca1964dc52',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dompdf/dompdf',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'endroid/qr-code' => array(
            'pretty_version' => '5.1.0',
            'version' => '5.1.0.0',
            'reference' => '393fec6c4cbdc1bd65570ac9d245704428010122',
            'type' => 'library',
            'install_path' => __DIR__ . '/../endroid/qr-code',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ezyang/htmlpurifier' => array(
            'pretty_version' => 'v4.18.0',
            'version' => '4.18.0.0',
            'reference' => 'cb56001e54359df7ae76dc522d08845dc741621b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ezyang/htmlpurifier',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laminas/laminas-barcode' => array(
            'pretty_version' => '2.14.0',
            'version' => '2.14.0.0',
            'reference' => '692b2224ba4583030fe7b1174d3b6e7250a868c1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laminas/laminas-barcode',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laminas/laminas-servicemanager' => array(
            'pretty_version' => '3.23.0',
            'version' => '3.23.0.0',
            'reference' => 'a8640182b892b99767d54404d19c5c3b3699f79b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laminas/laminas-servicemanager',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laminas/laminas-stdlib' => array(
            'pretty_version' => '3.20.0',
            'version' => '3.20.0.0',
            'reference' => '8974a1213be42c3e2f70b2c27b17f910291ab2f4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laminas/laminas-stdlib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laminas/laminas-validator' => array(
            'pretty_version' => '2.64.2',
            'version' => '********',
            'reference' => '771e504760448ac7af660710237ceb93be602e08',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laminas/laminas-validator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'maennchen/zipstream-php' => array(
            'pretty_version' => '3.1.1',
            'version' => '*******',
            'reference' => '6187e9cc4493da94b9b63eb2315821552015fca9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../maennchen/zipstream-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'markbaker/complex' => array(
            'pretty_version' => '3.0.2',
            'version' => '*******',
            'reference' => '95c56caa1cf5c766ad6d65b6344b807c1e8405b9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../markbaker/complex',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'markbaker/matrix' => array(
            'pretty_version' => '3.0.1',
            'version' => '*******',
            'reference' => '728434227fe21be27ff6d86621a1b13107a2562c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../markbaker/matrix',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'masterminds/html5' => array(
            'pretty_version' => '2.9.0',
            'version' => '*******',
            'reference' => 'f5ac2c0b0a2eefca70b2ce32a5809992227e75a6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../masterminds/html5',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mike42/escpos-php' => array(
            'pretty_version' => 'v3.0',
            'version' => '3.0.0.0',
            'reference' => 'dcb569a123d75f9f6a4a927aae7625ca6b7fdcf3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mike42/escpos-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mike42/gfx-php' => array(
            'pretty_version' => 'v0.6',
            'version' => '0.6.0.0',
            'reference' => 'ed9ded2a9298e4084a9c557ab74a89b71e43dbdb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mike42/gfx-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mikey179/vfsstream' => array(
            'pretty_version' => 'v1.1.0',
            'version' => '1.1.0.0',
            'reference' => 'fc0fe8f4d0b527254a2dc45f0c265567c881d07e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mikey179/vfsstream',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phenx/php-font-lib' => array(
            'pretty_version' => '0.5.6',
            'version' => '0.5.6.0',
            'reference' => 'a1681e9793040740a405ac5b189275059e2a9863',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phenx/php-font-lib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phenx/php-svg-lib' => array(
            'pretty_version' => '0.5.4',
            'version' => '0.5.4.0',
            'reference' => '46b25da81613a9cf43c83b2a8c2c1bdab27df691',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phenx/php-svg-lib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpoffice/phpspreadsheet' => array(
            'pretty_version' => '1.29.5',
            'version' => '1.29.5.0',
            'reference' => '727cb704d5479fe4ddc291497ee471c4ec08f1b6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoffice/phpspreadsheet',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container' => array(
            'pretty_version' => '1.1.2',
            'version' => '1.1.2.0',
            'reference' => '513e0666f7216c7459170d56df27dfcefe1689ea',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '^1.0',
            ),
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'reference' => '2b4765fddfe3b508ac62f829e852b1501d3f6e8a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '764e0b3939f5ca87cb904f570ef9be2d78a07865',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sabberworm/php-css-parser' => array(
            'pretty_version' => 'v8.7.0',
            'version' => '*******',
            'reference' => 'f414ff953002a9b18e3a116f5e462c56f21237cf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sabberworm/php-css-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spipu/html2pdf' => array(
            'pretty_version' => 'v5.2.8',
            'version' => '*******',
            'reference' => '6c94dcd48c94c6c73f206629839c1ebd81e8c726',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spipu/html2pdf',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'tecnickcom/tcpdf' => array(
            'pretty_version' => '6.7.7',
            'version' => '*******',
            'reference' => 'cfbc0028cc23f057f2baf9e73bdc238153c22086',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tecnickcom/tcpdf',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
