function searchmonth(){var monthyear=$("#datepicker3").val();console.log({monthyear:monthyear});var csrf=$("#csrfhashresarvation").val(),myurl=basicinfo.baseurl+"dashboard/home/<USER>",dataString="monthyear="+monthyear+"&csrf_test_name="+csrf;$.ajax({type:"POST",url:myurl,data:dataString,success:function(data){$("#salechart").html(data)}})}function printregistersummery(view){printJS({printable:view,type:"raw-html"})}$(document).ready((function(){"use strict";var monthname=$("#monthname").val(),onlinesaleamount=$("#onlinesaleamount").val(),onlinesaleorder=$("#onlinesaleorder").val(),offlinesaleamount=$("#offlinesaleamount").val(),offlinesaleorder=$("#offlinesaleorder").val(),monthlysaleamount=$("#monthlysaleamount").val(),monthlysaleorder=$("#monthlysaleorder").val(),onlinesaleamount2=onlinesaleamount.substring(0,onlinesaleamount.length-2).split(","),onlinesaleorder2=onlinesaleorder.substring(0,onlinesaleorder.length-1).split(","),offlinesaleamount2=offlinesaleamount.substring(0,offlinesaleamount.length-1).split(","),offlinesaleorder2=offlinesaleorder.substring(0,offlinesaleorder.length-1).split(","),monthlysaleamount2=monthlysaleamount.substring(0,monthlysaleamount.length-1).split(","),monthlysaleorder2=monthlysaleorder.substring(0,monthlysaleorder.length-1).split(","),monthnamelist=monthname.substring(0,monthname.length-0).split(",");console.log("---------m-------",monthnamelist);var salesChartCanvas=document.getElementById("barChart").getContext("2d"),salesChartData={labels:[chartinfo.monthname],datasets:[{label:lang.onlinesamnt,data:[0,0,0,0,0,0,0,0,0,0,0,0],borderColor:"rgba(55, 160, 0, 0.9)",borderWidth:"0",backgroundColor:"rgba(55, 160, 0, 0.5)"},{label:lang.onlineordnum,data:[0,0,0,0,0,0,0,0,0,0,0,0],borderColor:"rgba(0,0,0,0.09)",borderWidth:"0",backgroundColor:"rgba(0,0,0,0.07)"},{label:lang.offsalamnt,data:[0,0,0,0,0,0,0,0,0,0,0,0],borderColor:"rgba(55,160,0,0.9)",borderWidth:"0",backgroundColor:"rgba(55,160,0,0.9)"},{label:lang.offlordnum,data:[0,0,0,0,0,0,0,0,0,0,0,0],borderColor:"rgba(125,134,255,0.09)",borderWidth:"0",backgroundColor:"rgba(125,134,255,0.4)"}]};salesChartData.labels=monthnamelist,salesChartData.datasets[0].data=onlinesaleamount2,salesChartData.datasets[1].data=onlinesaleorder2,salesChartData.datasets[2].data=offlinesaleamount2,salesChartData.datasets[3].data=offlinesaleorder2;var salesChart=new Chart(salesChartCanvas,{type:"bar",data:salesChartData,options:{maintainAspectRatio:!1,responsive:!0,legend:{display:!0},scales:{xAxes:[{gridLines:{display:!0}}],yAxes:[{gridLines:{display:!0}}]}}});function rearrangeChartData(stringValue=""){if("string"!=typeof stringValue)return console.error("Invalid input: Expected a string."),[];return(stringValue.trim().endsWith(",")?stringValue.slice(0,-1):stringValue).split(",").map((item=>item.trim())).filter((item=>""!==item&&!isNaN(item))).map((item=>parseFloat(item)))}$("body").on("change","#datepicker5",(function(e){let yearMonth=$(this).val();if(""===yearMonth||null===yearMonth)return!1;!function(year="dateEmpty"){$.ajax({url:basicinfo.baseurl+"dashboard/home/<USER>/"+year,type:"GET",dataType:"json",success:function(response){let onlineSaleAmount=rearrangeChartData(response.onlinesaleamount),onlineOrderCount=rearrangeChartData(response.onlinesaleorder),offlineSaleAmount=rearrangeChartData(response.offlinesaleamount),offlineOrderCount=rearrangeChartData(response.offlinesaleorder),monthArr=response.monthname.split(",");salesChart.data.labels=monthArr,salesChart.data.datasets[0].data=onlineSaleAmount,salesChart.data.datasets[1].data=onlineOrderCount,salesChart.data.datasets[2].data=offlineSaleAmount,salesChart.data.datasets[3].data=offlineOrderCount,salesChart.update()},error:function(){alert("Failed to load chart data.")}})}(yearMonth)}));var myChartsales=document.getElementById("lineChart").getContext("2d"),salesorderChartData={labels:[chartinfo.monthname],datasets:[{label:lang.saleamnt,borderColor:"rgba(0,0,0,.09)",borderWidth:"1",backgroundColor:"rgba(0,0,0,.07)",data:[0,0,0,0,0,0,0,0,0,0,0,0]},{label:lang.ordnumb,borderColor:"rgba(55, 160, 0, 0.9)",borderWidth:"1",backgroundColor:"rgba(55, 160, 0, 0.5)",pointHighlightStroke:"rgba(26,179,148,1)",data:[0,0,0,0,0,0,0,0,0,0,0,0]}]};salesorderChartData.labels=monthnamelist,salesorderChartData.datasets[0].data=monthlysaleamount2,salesorderChartData.datasets[1].data=monthlysaleorder2;new Chart(myChartsales,{type:"line",data:salesorderChartData,options:{responsive:!0,tooltips:{mode:"index",intersect:!1},hover:{mode:"nearest",intersect:!0}}})})),$("body").on("change","#datepicker3",(function(){searchmonth()})),$(document).ready((function(){if("done"==$("#registerclose").val()){var dataString="csrf_test_name="+$("#csrfhashresarvation").val();$.ajax({type:"POST",url:basicinfo.baseurl+"ordermanage/order/closecashregisterprinttest",data:dataString,success:function(data){printregistersummery(data)}})}}));