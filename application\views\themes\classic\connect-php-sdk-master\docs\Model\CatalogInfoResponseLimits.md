# CatalogInfoResponseLimits

## Properties
Name | Getter | Setter | Type | Description | Notes
------------ | ------------- | ------------- | ------------- | ------------- | -------------
**batch_upsert_max_objects_per_batch** | getBatchUpsertMaxObjectsPerBatch() | setBatchUpsertMaxObjectsPerBatch($value) | **int** | The maximum number of objects that may appear within a single batch in a &#x60;/v2/catalog/batch-upsert&#x60; request. | [optional] 
**batch_upsert_max_total_objects** | getBatchUpsertMaxTotalObjects() | setBatchUpsertMaxTotalObjects($value) | **int** | The maximum number of objects that may appear across all batches in a &#x60;/v2/catalog/batch-upsert&#x60; request. | [optional] 
**batch_retrieve_max_object_ids** | getBatchRetrieveMaxObjectIds() | setBatchRetrieveMaxObjectIds($value) | **int** | The maximum number of object IDs that may appear in a &#x60;/v2/catalog/batch-retrieve&#x60; request. | [optional] 
**search_max_page_limit** | getSearchMaxPageLimit() | setSearchMaxPageLimit($value) | **int** | The maximum number of results that may be returned in a page of a &#x60;/v2/catalog/search&#x60; response. | [optional] 
**batch_delete_max_object_ids** | getBatchDeleteMaxObjectIds() | setBatchDeleteMaxObjectIds($value) | **int** | The maximum number of object IDs that may be included in a single &#x60;/v2/catalog/batch-delete&#x60; request. | [optional] 
**update_item_taxes_max_item_ids** | getUpdateItemTaxesMaxItemIds() | setUpdateItemTaxesMaxItemIds($value) | **int** | The maximum number of item IDs that may be included in a single &#x60;/v2/catalog/update-item-taxes&#x60; request. | [optional] 
**update_item_taxes_max_taxes_to_enable** | getUpdateItemTaxesMaxTaxesToEnable() | setUpdateItemTaxesMaxTaxesToEnable($value) | **int** | The maximum number of tax IDs to be enabled that may be included in a single &#x60;/v2/catalog/update-item-taxes&#x60; request. | [optional] 
**update_item_taxes_max_taxes_to_disable** | getUpdateItemTaxesMaxTaxesToDisable() | setUpdateItemTaxesMaxTaxesToDisable($value) | **int** | The maximum number of tax IDs to be disabled that may be included in a single &#x60;/v2/catalog/update-item-taxes&#x60; request. | [optional] 
**update_item_modifier_lists_max_item_ids** | getUpdateItemModifierListsMaxItemIds() | setUpdateItemModifierListsMaxItemIds($value) | **int** | The maximum number of item IDs that may be included in a single &#x60;/v2/catalog/update-item-modifier-lists&#x60; request. | [optional] 
**update_item_modifier_lists_max_modifier_lists_to_enable** | getUpdateItemModifierListsMaxModifierListsToEnable() | setUpdateItemModifierListsMaxModifierListsToEnable($value) | **int** | The maximum number of modifier list IDs to be enabled that may be included in a single &#x60;/v2/catalog/update-item-modifier-lists&#x60; request. | [optional] 
**update_item_modifier_lists_max_modifier_lists_to_disable** | getUpdateItemModifierListsMaxModifierListsToDisable() | setUpdateItemModifierListsMaxModifierListsToDisable($value) | **int** | The maximum number of modifier list IDs to be disabled that may be included in a single &#x60;/v2/catalog/update-item-modifier-lists&#x60; request. | [optional] 

Note: All properties are protected and only accessed via getters and setters.

[[Back to Model list]](../../README.md#documentation-for-models) [[Back to API list]](../../README.md#documentation-for-api-endpoints) [[Back to README]](../../README.md)

