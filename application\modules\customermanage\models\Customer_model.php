<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Customer_model extends CI_Model
{

    private $table = 'customer_info';

    public function __construct()
    {
        parent::__construct();
        $this->db->query('SET SESSION sql_mode = ""');
    }

    // Get all customers with pagination
    public function get_customers($limit = null, $start = null)
    {
        $this->db->select('customer_info.*, customer_type.customer_type');
        $this->db->from('customer_info');
        $this->db->join('customer_type', 'customer_info.membership_type = customer_type.customer_type_id', 'left');
        $this->db->order_by('customer_info.customer_id', 'desc');

        if ($limit != null) {
            $this->db->limit($limit, $start);
        }

        $query = $this->db->get();
        if ($query->num_rows() > 0) {
            return $query->result();
        }
        return false;
    }

    // Count total customers
    public function count_customers()
    {
        $this->db->select('*');
        $this->db->from('customer_info');
        $query = $this->db->get();
        if ($query->num_rows() > 0) {
            return $query->num_rows();
        }
        return 0;
    }

    // Get customer by ID
    public function get_customer_by_id($customer_id)
    {
        $this->db->select('customer_info.*, customer_type.customer_type');
        $this->db->from('customer_info');
        $this->db->join('customer_type', 'customer_info.membership_type = customer_type.customer_type_id', 'left');
        $this->db->where('customer_info.customer_id', $customer_id);
        $query = $this->db->get();

        if ($query->num_rows() > 0) {
            return $query->row();
        }
        return false;
    }

    // Get customer orders/transactions
    public function get_customer_orders($customer_id, $limit = null, $start = null)
    {
        $this->db->select('customer_order.*, rest_table.tablename, employee_history.first_name, employee_history.last_name');
        $this->db->from('customer_order');
        $this->db->join('rest_table', 'customer_order.table_no = rest_table.tableid', 'left');
        $this->db->join('employee_history', 'customer_order.waiter_id = employee_history.emp_his_id', 'left');
        $this->db->where('customer_order.customer_id', $customer_id);
        $this->db->order_by('customer_order.order_date', 'desc');

        if ($limit != null) {
            $this->db->limit($limit, $start);
        }

        $query = $this->db->get();
        if ($query->num_rows() > 0) {
            return $query->result();
        }
        return false;
    }

    // Count customer orders
    public function count_customer_orders($customer_id)
    {
        $this->db->select('*');
        $this->db->from('customer_order');
        $this->db->where('customer_id', $customer_id);
        $query = $this->db->get();
        if ($query->num_rows() > 0) {
            return $query->num_rows();
        }
        return 0;
    }

    // Get customer order details (items in order)
    public function get_order_details($order_id)
    {
        $this->db->select('order_menu.*, item_foods.ProductName');
        $this->db->from('order_menu');
        $this->db->join('item_foods', 'order_menu.menu_id = item_foods.ProductsID', 'left');
        $this->db->where('order_menu.order_id', $order_id);
        $query = $this->db->get();

        if ($query->num_rows() > 0) {
            return $query->result();
        }
        return false;
    }

    // Get customer statistics
    public function get_customer_statistics($customer_id)
    {
        // Total orders
        $total_orders = $this->count_customer_orders($customer_id);

        // Total amount spent
        $this->db->select_sum('totalamount');
        $this->db->from('customer_order');
        $this->db->where('customer_id', $customer_id);
        $query = $this->db->get();
        $total_spent = $query->row()->totalamount ? $query->row()->totalamount : 0;

        // Last order date
        $this->db->select('order_date');
        $this->db->from('customer_order');
        $this->db->where('customer_id', $customer_id);
        $this->db->order_by('order_date', 'desc');
        $this->db->limit(1);
        $query = $this->db->get();
        $last_order = $query->num_rows() > 0 ? $query->row()->order_date : null;

        return array(
            'total_orders' => $total_orders,
            'total_spent' => $total_spent,
            'last_order_date' => $last_order
        );
    }

    // Search customers
    public function search_customers($search_term, $limit = null, $start = null)
    {
        $this->db->select('customer_info.*, customer_type.customer_type');
        $this->db->from('customer_info');
        $this->db->join('customer_type', 'customer_info.membership_type = customer_type.customer_type_id', 'left');
        $this->db->group_start();
        $this->db->like('customer_info.customer_name', $search_term);
        $this->db->or_like('customer_info.customer_phone', $search_term);
        $this->db->or_like('customer_info.customer_email', $search_term);
        $this->db->or_like('customer_info.cuntomer_no', $search_term);
        $this->db->group_end();
        $this->db->order_by('customer_info.customer_id', 'desc');

        if ($limit != null) {
            $this->db->limit($limit, $start);
        }

        $query = $this->db->get();
        if ($query->num_rows() > 0) {
            return $query->result();
        }
        return false;
    }

    // Get top customers ranking by total spent
    public function get_top_customers($limit = 20)
    {
        $this->db->select('
            customer_info.customer_id,
            customer_info.cuntomer_no,
            customer_info.customer_name,
            customer_info.customer_email,
            customer_info.customer_phone,
            customer_info.customer_address,
            customer_info.crdate,
            customer_type.customer_type,
            COUNT(customer_order.order_id) as total_orders,
            COALESCE(SUM(customer_order.totalamount), 0) as total_spent,
            MAX(customer_order.order_date) as last_order_date
        ');
        $this->db->from('customer_info');
        $this->db->join('customer_type', 'customer_info.membership_type = customer_type.customer_type_id', 'left');
        $this->db->join('customer_order', 'customer_info.customer_id = customer_order.customer_id', 'left');
        $this->db->group_by('customer_info.customer_id');
        $this->db->order_by('total_spent', 'desc');
        $this->db->order_by('total_orders', 'desc');
        $this->db->limit($limit);

        $query = $this->db->get();
        if ($query->num_rows() > 0) {
            return $query->result();
        }
        return false;
    }

    // Get top customers ranking by total orders
    public function get_top_customers_by_orders($limit = 20)
    {
        $this->db->select('
            customer_info.customer_id,
            customer_info.cuntomer_no,
            customer_info.customer_name,
            customer_info.customer_email,
            customer_info.customer_phone,
            customer_info.customer_address,
            customer_info.crdate,
            customer_type.customer_type,
            COUNT(customer_order.order_id) as total_orders,
            COALESCE(SUM(customer_order.totalamount), 0) as total_spent,
            MAX(customer_order.order_date) as last_order_date
        ');
        $this->db->from('customer_info');
        $this->db->join('customer_type', 'customer_info.membership_type = customer_type.customer_type_id', 'left');
        $this->db->join('customer_order', 'customer_info.customer_id = customer_order.customer_id', 'left');
        $this->db->group_by('customer_info.customer_id');
        $this->db->order_by('total_orders', 'desc');
        $this->db->order_by('total_spent', 'desc');
        $this->db->limit($limit);

        $query = $this->db->get();
        if ($query->num_rows() > 0) {
            return $query->result();
        }
        return false;
    }
}
