"use strict";
function printDiv(divName) {
	var printContents = document.getElementById(divName).innerHTML;
	var originalContents = document.body.innerHTML;
	document.body.innerHTML = printContents;
	document.body.style.marginTop = "0px";
	window.print();
	document.body.innerHTML = originalContents;
}

function getreport() {
	var from_date = $('#from_date').val();
	var to_date = $('#to_date').val();
	var catid = $('#catid').val();
	var view_name = $('#view_name').val();


	if (from_date == '') {
		alert("Please select from date");
		return false;
	}
	if (to_date == '') {
		alert("Please select To date");
		return false;
	}
	var myurl = baseurl + 'report/reports/' + view_name;
	var csrf = $('#csrfhashresarvation').val();
	var dataString = "from_date=" + from_date + '&to_date=' + to_date + '&catid=' + catid + '&csrf_test_name=' + csrf;
	$.ajax({
		type: "POST",
		url: myurl,
		data: dataString,
		success: function (data) {
			$('#getresult2').html(data);
			$('#respritbl').DataTable({
				responsive: true,
				paging: true,
				"language": {
					"sProcessing": lang.Processingod,
					"sSearch": lang.search,
					"sLengthMenu": lang.sLengthMenu,
					"sInfo": lang.sInfo,
					"sInfoEmpty": lang.sInfoEmpty,
					"sInfoFiltered": lang.sInfoFiltered,
					"sInfoPostFix": "",
					"sLoadingRecords": lang.sLoadingRecords,
					"sZeroRecords": lang.sZeroRecords,
					"sEmptyTable": lang.sEmptyTable,
					"oPaginate": {
						"sFirst": lang.sFirst,
						"sPrevious": lang.sPrevious,
						"sNext": lang.sNext,
						"sLast": lang.sLast
					},
					"oAria": {
						"sSortAscending": ":" + lang.sSortAscending + '"',
						"sSortDescending": ":" + lang.sSortDescending + '"'
					},
					"select": {
						"rows": {
							"_": lang._sign,
							"0": lang._0sign,
							"1": lang._1sign
						}
					},
					buttons: {
						copy: lang.copy,
						csv: lang.csv,
						excel: lang.excel,
						pdf: lang.pdf,
						print: lang.print,
						colvis: lang.colvis
					}
				},
				dom: 'Bfrtip',
				"lengthMenu": [[25, 50, 100, 150, 200, 500, -1], [25, 50, 100, 150, 200, 500, "All"]],
				buttons: [
					{ extend: 'copy', className: 'btn-sm', footer: true },
					{ extend: 'csv', title: 'Report', className: 'btn-sm', footer: true },
					{ extend: 'excel', title: 'Report', className: 'btn-sm', title: 'exportTitle', footer: true },
					{ extend: 'pdf', title: 'Report', className: 'btn-sm', footer: true },
					{ extend: 'print', className: 'btn-sm', footer: true },
					{ extend: 'colvis', className: 'btn-sm', footer: true }
				],
				"searching": true,
				"processing": true,

			});
		}
	});
}

function getreportupdate() {
	var from_date = $('#from_date').val();
	var to_date = $('#to_date').val();
	var catid = $('#catid').val();
	var view_name = $('#view_name').val();

	if (from_date == '') {
		alert("Please select from date");
		return false;
	}
	if (to_date == '') {
		alert("Please select To date");
		return false;
	}
	var myurl = baseurl + 'report/reports/' + view_name;
	var csrf = $('#csrfhashresarvation').val();
	var dataString = "from_date=" + from_date + '&to_date=' + to_date + '&catid=' + catid + '&csrf_test_name=' + csrf;
	$.ajax({
		type: "POST",
		url: myurl,
		data: dataString,
		success: function (data) {
			console.log('Report data received:', data.length, 'characters');
			$('#getresult2').html(data);

			// Initialize DataTable after AJAX content is loaded
			setTimeout(function () {
				try {
					// Destroy existing DataTable if exists
					if ($.fn.DataTable.isDataTable('#respritbl')) {
						$('#respritbl').DataTable().destroy();
					}

					// Check if table exists and has data
					if ($('#respritbl').length > 0) {
						var rowCount = $('#respritbl tbody tr').length;
						console.log('Table found with', rowCount, 'rows');

						if (rowCount > 0) {
							// Initialize DataTable with enhanced options
							$('#respritbl').DataTable({
								responsive: true,
								paging: true,
								pageLength: 25,
								order: [[2, 'desc']], // Sort by quantity descending
								columnDefs: [
									{
										targets: [1, 3], // Price and Total columns
										className: 'text-right',
										render: function (data, type, row) {
											if (type === 'display' && data != null) {
												return parseFloat(data).toLocaleString('id-ID', {
													minimumFractionDigits: 0,
													maximumFractionDigits: 2
												});
											}
											return data;
										}
									},
									{
										targets: [2], // Quantity column
										className: 'text-center'
									}
								],
								"language": {
									"sProcessing": "Memproses...",
									"sSearch": "Cari:",
									"sLengthMenu": "Tampilkan _MENU_ entri",
									"sInfo": "Menampilkan _START_ sampai _END_ dari _TOTAL_ entri",
									"sInfoEmpty": "Menampilkan 0 sampai 0 dari 0 entri",
									"sInfoFiltered": "(disaring dari _MAX_ total entri)",
									"sZeroRecords": "Tidak ada data yang cocok",
									"sEmptyTable": "Tidak ada data tersedia",
									"oPaginate": {
										"sFirst": "Pertama",
										"sPrevious": "Sebelumnya",
										"sNext": "Selanjutnya",
										"sLast": "Terakhir"
									}
								}
							});
							console.log('DataTable initialized successfully');
						} else {
							console.log('Table has no data rows');
						}
					} else {
						console.log('Table #respritbl not found');
					}
				} catch (error) {
					console.error('Error initializing DataTable:', error);
				}
			}, 800);
		},
		error: function (xhr, status, error) {
			console.log('AJAX Error:', error);
			console.log('Response:', xhr.responseText);
			alert('Error loading report data: ' + error);
		}
	});
}
function generatereport() {
	var from_date = $('#from_date').val();
	var to_date = $('#to_date').val();
	var csrf = $('#csrfhashresarvation').val();
	if (from_date == '') {
		alert("Please select from date");
		return false;
	}
	if (to_date == '') {
		alert("Please select To date");
		return false;
	}
	var myurl = baseurl + 'report/reports/generaterpt';
	var dataString = "from_date=" + from_date + '&to_date=' + to_date + '&csrf_test_name=' + csrf;
	$.ajax({
		type: "POST",
		url: myurl,
		data: dataString,
		success: function (data) {


		}
	});
}