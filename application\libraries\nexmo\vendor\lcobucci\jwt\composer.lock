{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#composer-lock-the-lock-file", "This file is @generated automatically"], "hash": "82547d7474ec5fcbf6718c4f2dca46d6", "content-hash": "1c87e501c8e2df5e27eac6bfefe0c1f9", "packages": [], "packages-dev": [{"name": "doctrine/instantiator", "version": "1.0.5", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "8e884e78f9f0eb1329e445619e04456e64d8051d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/8e884e78f9f0eb1329e445619e04456e64d8051d", "reference": "8e884e78f9f0eb1329e445619e04456e64d8051d", "shasum": ""}, "require": {"php": ">=5.3,<8.0-DEV"}, "require-dev": {"athletic/athletic": "~0.1.8", "ext-pdo": "*", "ext-phar": "*", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.com/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://github.com/doctrine/instantiator", "keywords": ["constructor", "instantiate"], "time": "2015-06-14 21:17:01"}, {"name": "fgrosse/phpasn1", "version": "1.3.2", "source": {"type": "git", "url": "https://github.com/fgrosse/PHPASN1.git", "reference": "ee6d1abd18f8bcbaf0b55563ba87e5ed16cd0c98"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fgrosse/PHPASN1/zipball/ee6d1abd18f8bcbaf0b55563ba87e5ed16cd0c98", "reference": "ee6d1abd18f8bcbaf0b55563ba87e5ed16cd0c98", "shasum": ""}, "require": {"ext-gmp": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "~4.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"FG\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Friedrich Große", "email": "<EMAIL>", "homepage": "https://github.com/FGrosse", "role": "Author"}, {"name": "All contributors", "homepage": "https://github.com/FGrosse/PHPASN1/contributors"}], "description": "A PHP Framework that allows you to encode and decode arbitrary ASN.1 structures using the ITU-T X.690 Encoding Rules.", "homepage": "https://github.com/FGrosse/PHPASN1", "time": "2015-07-15 21:26:40"}, {"name": "mdanter/ecc", "version": "v0.3.1", "source": {"type": "git", "url": "https://github.com/phpecc/phpecc.git", "reference": "182d94bc3bbeee6a8591bde36e66a80d8b07ae4b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpecc/phpecc/zipball/182d94bc3bbeee6a8591bde36e66a80d8b07ae4b", "reference": "182d94bc3bbeee6a8591bde36e66a80d8b07ae4b", "shasum": ""}, "require": {"ext-gmp": "*", "ext-mcrypt": "*", "fgrosse/phpasn1": "~1.3.1", "php": ">=5.4.0", "symfony/console": "~2.6"}, "require-dev": {"phpunit/phpunit": "~4.1", "squizlabs/php_codesniffer": "~2", "symfony/yaml": "~2.6"}, "type": "library", "autoload": {"psr-4": {"Mdanter\\Ecc\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "http://matejdanter.com/", "role": "Author"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://aztech.io", "role": "Maintainer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://zikula.org", "role": "Maintainer"}], "description": "PHP Elliptic Curve Cryptography library", "homepage": "https://github.com/mdanter/phpecc", "time": "2014-07-07 12:44:15"}, {"name": "mikey179/vfsStream", "version": "v1.6.4", "source": {"type": "git", "url": "https://github.com/mikey179/vfsStream.git", "reference": "0247f57b2245e8ad2e689d7cee754b45fbabd592"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mikey179/vfsStream/zipball/0247f57b2245e8ad2e689d7cee754b45fbabd592", "reference": "0247f57b2245e8ad2e689d7cee754b45fbabd592", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "~4.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "autoload": {"psr-0": {"org\\bovigo\\vfs\\": "src/main/php"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "http://frankkleine.de/", "role": "Developer"}], "description": "Virtual file system to mock the real file system in unit tests.", "homepage": "http://vfs.bovigo.org/", "time": "2016-07-18 14:02:57"}, {"name": "pdepend/pdepend", "version": "2.2.4", "source": {"type": "git", "url": "https://github.com/pdepend/pdepend.git", "reference": "b086687f3a01dc6bb92d633aef071d2c5dd0db06"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pdepend/pdepend/zipball/b086687f3a01dc6bb92d633aef071d2c5dd0db06", "reference": "b086687f3a01dc6bb92d633aef071d2c5dd0db06", "shasum": ""}, "require": {"php": ">=5.3.7", "symfony/config": "^2.3.0|^3", "symfony/dependency-injection": "^2.3.0|^3", "symfony/filesystem": "^2.3.0|^3"}, "require-dev": {"phpunit/phpunit": "^4.4.0,<4.8", "squizlabs/php_codesniffer": "^2.0.0"}, "bin": ["src/bin/pdepend"], "type": "library", "autoload": {"psr-4": {"PDepend\\": "src/main/php/PDepend"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Official version of pdepend to be handled with Composer", "time": "2016-03-10 15:15:04"}, {"name": "phpdocumentor/reflection-common", "version": "1.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "144c307535e82c8fdcaacbcfc1d6d8eeb896687c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/144c307535e82c8fdcaacbcfc1d6d8eeb896687c", "reference": "144c307535e82c8fdcaacbcfc1d6d8eeb896687c", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "time": "2015-12-27 11:43:31"}, {"name": "phpdocumentor/reflection-docblock", "version": "3.1.1", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "8331b5efe816ae05461b7ca1e721c01b46bafb3e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/8331b5efe816ae05461b7ca1e721c01b46bafb3e", "reference": "8331b5efe816ae05461b7ca1e721c01b46bafb3e", "shasum": ""}, "require": {"php": ">=5.5", "phpdocumentor/reflection-common": "^1.0@dev", "phpdocumentor/type-resolver": "^0.2.0", "webmozart/assert": "^1.0"}, "require-dev": {"mockery/mockery": "^0.9.4", "phpunit/phpunit": "^4.4"}, "type": "library", "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "time": "2016-09-30 07:12:33"}, {"name": "phpdocumentor/type-resolver", "version": "0.2", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "b39c7a5b194f9ed7bd0dd345c751007a41862443"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/b39c7a5b194f9ed7bd0dd345c751007a41862443", "reference": "b39c7a5b194f9ed7bd0dd345c751007a41862443", "shasum": ""}, "require": {"php": ">=5.5", "phpdocumentor/reflection-common": "^1.0"}, "require-dev": {"mockery/mockery": "^0.9.4", "phpunit/phpunit": "^5.2||^4.8.24"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "time": "2016-06-10 07:14:17"}, {"name": "phpmd/phpmd", "version": "2.4.3", "source": {"type": "git", "url": "https://github.com/phpmd/phpmd.git", "reference": "2b9c2417a18696dfb578b38c116cd0ddc19b256e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpmd/phpmd/zipball/2b9c2417a18696dfb578b38c116cd0ddc19b256e", "reference": "2b9c2417a18696dfb578b38c116cd0ddc19b256e", "shasum": ""}, "require": {"pdepend/pdepend": "^2.0.4", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.0", "squizlabs/php_codesniffer": "^2.0"}, "bin": ["src/bin/phpmd"], "type": "project", "autoload": {"psr-0": {"PHPMD\\": "src/main/php"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/man<PERSON><PERSON><PERSON>", "role": "Project Founder"}, {"name": "Other contributors", "homepage": "https://github.com/phpmd/phpmd/graphs/contributors", "role": "Contributors"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/ravage84", "role": "Project Maintainer"}], "description": "PHPMD is a spin-off project of PHP Depend and aims to be a PHP equivalent of the well known Java tool PMD.", "homepage": "http://phpmd.org/", "keywords": ["mess detection", "mess detector", "pdepend", "phpmd", "pmd"], "time": "2016-04-04 11:52:04"}, {"name": "phpspec/prophecy", "version": "v1.6.1", "source": {"type": "git", "url": "https://github.com/phpspec/prophecy.git", "reference": "58a8137754bc24b25740d4281399a4a3596058e0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/prophecy/zipball/58a8137754bc24b25740d4281399a4a3596058e0", "reference": "58a8137754bc24b25740d4281399a4a3596058e0", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": "^5.3|^7.0", "phpdocumentor/reflection-docblock": "^2.0|^3.0.2", "sebastian/comparator": "^1.1", "sebastian/recursion-context": "^1.0"}, "require-dev": {"phpspec/phpspec": "^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "autoload": {"psr-0": {"Prophecy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Highly opinionated mocking framework for PHP 5.3+", "homepage": "https://github.com/phpspec/prophecy", "keywords": ["Double", "Dummy", "fake", "mock", "spy", "stub"], "time": "2016-06-07 08:13:47"}, {"name": "phpunit/php-code-coverage", "version": "2.2.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "eabf68b476ac7d0f73793aada060f1c1a9bf8979"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/eabf68b476ac7d0f73793aada060f1c1a9bf8979", "reference": "eabf68b476ac7d0f73793aada060f1c1a9bf8979", "shasum": ""}, "require": {"php": ">=5.3.3", "phpunit/php-file-iterator": "~1.3", "phpunit/php-text-template": "~1.2", "phpunit/php-token-stream": "~1.3", "sebastian/environment": "^1.3.2", "sebastian/version": "~1.0"}, "require-dev": {"ext-xdebug": ">=2.1.4", "phpunit/phpunit": "~4"}, "suggest": {"ext-dom": "*", "ext-xdebug": ">=2.2.1", "ext-xmlwriter": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "time": "2015-10-06 15:47:00"}, {"name": "phpunit/php-file-iterator", "version": "1.4.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "6150bf2c35d3fc379e50c7602b75caceaa39dbf0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/6150bf2c35d3fc379e50c7602b75caceaa39dbf0", "reference": "6150bf2c35d3fc379e50c7602b75caceaa39dbf0", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "time": "2015-06-21 13:08:43"}, {"name": "phpunit/php-invoker", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "reference": "86074bf0fc2caf02ec8819a93f65a37cd0b44c8e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/86074bf0fc2caf02ec8819a93f65a37cd0b44c8e", "reference": "86074bf0fc2caf02ec8819a93f65a37cd0b44c8e", "shasum": ""}, "require": {"ext-pcntl": "*", "php": ">=5.3.3", "phpunit/php-timer": ">=1.0.6"}, "require-dev": {"phpunit/phpunit": "~4"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for invoking callables with a timeout.", "homepage": "https://github.com/sebastian<PERSON>mann/php-invoker/", "keywords": ["process"], "time": "2015-06-21 13:32:55"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "time": "2015-06-21 13:50:34"}, {"name": "phpunit/php-timer", "version": "1.0.8", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "38e9124049cf1a164f1e4537caf19c99bf1eb260"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-timer/zipball/38e9124049cf1a164f1e4537caf19c99bf1eb260", "reference": "38e9124049cf1a164f1e4537caf19c99bf1eb260", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4|~5"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "time": "2016-05-12 18:03:57"}, {"name": "phpunit/php-token-stream", "version": "1.4.8", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "3144ae21711fb6cac0b1ab4cbe63b75ce3d4e8da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-token-stream/zipball/3144ae21711fb6cac0b1ab4cbe63b75ce3d4e8da", "reference": "3144ae21711fb6cac0b1ab4cbe63b75ce3d4e8da", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "time": "2015-09-15 10:49:45"}, {"name": "phpunit/phpunit", "version": "4.8.27", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "c062dddcb68e44b563f66ee319ddae2b5a322a90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/c062dddcb68e44b563f66ee319ddae2b5a322a90", "reference": "c062dddcb68e44b563f66ee319ddae2b5a322a90", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-pcre": "*", "ext-reflection": "*", "ext-spl": "*", "php": ">=5.3.3", "phpspec/prophecy": "^1.3.1", "phpunit/php-code-coverage": "~2.1", "phpunit/php-file-iterator": "~1.4", "phpunit/php-text-template": "~1.2", "phpunit/php-timer": "^1.0.6", "phpunit/phpunit-mock-objects": "~2.3", "sebastian/comparator": "~1.1", "sebastian/diff": "~1.2", "sebastian/environment": "~1.3", "sebastian/exporter": "~1.2", "sebastian/global-state": "~1.0", "sebastian/version": "~1.0", "symfony/yaml": "~2.1|~3.0"}, "suggest": {"phpunit/php-invoker": "~1.1"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.8.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "time": "2016-07-21 06:48:14"}, {"name": "phpunit/phpunit-mock-objects", "version": "2.3.8", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects.git", "reference": "ac8e7a3db35738d56ee9a76e78a4e03d97628983"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects/zipball/ac8e7a3db35738d56ee9a76e78a4e03d97628983", "reference": "ac8e7a3db35738d56ee9a76e78a4e03d97628983", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": ">=5.3.3", "phpunit/php-text-template": "~1.2", "sebastian/exporter": "~1.2"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "suggest": {"ext-soap": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Mock Object library for PHPUnit", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/phpunit-mock-objects/", "keywords": ["mock", "xunit"], "time": "2015-10-02 06:51:40"}, {"name": "psr/log", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "4ebe3a8bf773a19edfe0a84b6585ba3d401b724d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/4ebe3a8bf773a19edfe0a84b6585ba3d401b724d", "reference": "4ebe3a8bf773a19edfe0a84b6585ba3d401b724d", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2016-10-10 12:19:37"}, {"name": "sebastian/comparator", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "937efb279bd37a375bcadf584dec0726f84dbf22"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/937efb279bd37a375bcadf584dec0726f84dbf22", "reference": "937efb279bd37a375bcadf584dec0726f84dbf22", "shasum": ""}, "require": {"php": ">=5.3.3", "sebastian/diff": "~1.2", "sebastian/exporter": "~1.2"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "http://www.github.com/sebastian<PERSON>mann/comparator", "keywords": ["comparator", "compare", "equality"], "time": "2015-07-26 15:48:44"}, {"name": "sebastian/diff", "version": "1.4.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "13edfd8706462032c2f52b4b862974dd46b71c9e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/diff/zipball/13edfd8706462032c2f52b4b862974dd46b71c9e", "reference": "13edfd8706462032c2f52b4b862974dd46b71c9e", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff"], "time": "2015-12-08 07:14:41"}, {"name": "sebastian/environment", "version": "1.3.8", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "be2c607e43ce4c89ecd60e75c6a85c126e754aea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/environment/zipball/be2c607e43ce4c89ecd60e75c6a85c126e754aea", "reference": "be2c607e43ce4c89ecd60e75c6a85c126e754aea", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8 || ^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "time": "2016-08-18 05:49:44"}, {"name": "sebastian/exporter", "version": "1.2.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "42c4c2eec485ee3e159ec9884f95b431287edde4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/42c4c2eec485ee3e159ec9884f95b431287edde4", "reference": "42c4c2eec485ee3e159ec9884f95b431287edde4", "shasum": ""}, "require": {"php": ">=5.3.3", "sebastian/recursion-context": "~1.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "time": "2016-06-17 09:04:28"}, {"name": "sebastian/global-state", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "bc37d50fea7d017d3d340f230811c9f1d7280af4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/bc37d50fea7d017d3d340f230811c9f1d7280af4", "reference": "bc37d50fea7d017d3d340f230811c9f1d7280af4", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.2"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "time": "2015-10-12 03:26:01"}, {"name": "sebastian/recursion-context", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "913401df809e99e4f47b27cdd781f4a258d58791"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/913401df809e99e4f47b27cdd781f4a258d58791", "reference": "913401df809e99e4f47b27cdd781f4a258d58791", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "time": "2015-11-11 19:50:13"}, {"name": "sebastian/version", "version": "1.0.6", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "58b3a85e7999757d6ad81c787a1fbf5ff6c628c6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/58b3a85e7999757d6ad81c787a1fbf5ff6c628c6", "reference": "58b3a85e7999757d6ad81c787a1fbf5ff6c628c6", "shasum": ""}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "time": "2015-06-21 13:59:46"}, {"name": "squizlabs/php_codesniffer", "version": "2.7.0", "source": {"type": "git", "url": "https://github.com/squizlabs/PHP_CodeSniffer.git", "reference": "571e27b6348e5b3a637b2abc82ac0d01e6d7bbed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/squizlabs/PHP_CodeSniffer/zipball/571e27b6348e5b3a637b2abc82ac0d01e6d7bbed", "reference": "571e27b6348e5b3a637b2abc82ac0d01e6d7bbed", "shasum": ""}, "require": {"ext-simplexml": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": ">=5.1.2"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "bin": ["scripts/phpcs", "scripts/phpcbf"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"classmap": ["CodeSniffer.php", "CodeSniffer/CLI.php", "CodeSniffer/Exception.php", "CodeSniffer/File.php", "CodeSniffer/Fixer.php", "CodeSniffer/Report.php", "CodeSniffer/Reporting.php", "CodeSniffer/Sniff.php", "CodeSniffer/Tokens.php", "CodeSniffer/Reports/", "CodeSniffer/Tokenizers/", "CodeSniffer/DocGenerators/", "CodeSniffer/Standards/AbstractPatternSniff.php", "CodeSniffer/Standards/AbstractScopeSniff.php", "CodeSniffer/Standards/AbstractVariableSniff.php", "CodeSniffer/Standards/IncorrectPatternException.php", "CodeSniffer/Standards/Generic/Sniffs/", "CodeSniffer/Standards/MySource/Sniffs/", "CodeSniffer/Standards/PEAR/Sniffs/", "CodeSniffer/Standards/PSR1/Sniffs/", "CodeSniffer/Standards/PSR2/Sniffs/", "CodeSniffer/Standards/Squiz/Sniffs/", "CodeSniffer/Standards/Zend/Sniffs/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "role": "lead"}], "description": "PHP_CodeSniffer tokenizes PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "homepage": "http://www.squizlabs.com/php-codesniffer", "keywords": ["phpcs", "standards"], "time": "2016-09-01 23:53:02"}, {"name": "symfony/config", "version": "v3.1.6", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "949e7e846743a7f9e46dc50eb639d5fde1f53341"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/949e7e846743a7f9e46dc50eb639d5fde1f53341", "reference": "949e7e846743a7f9e46dc50eb639d5fde1f53341", "shasum": ""}, "require": {"php": ">=5.5.9", "symfony/filesystem": "~2.8|~3.0"}, "suggest": {"symfony/yaml": "To use the yaml reference dumper"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Config Component", "homepage": "https://symfony.com", "time": "2016-09-25 08:27:07"}, {"name": "symfony/console", "version": "v2.8.13", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "7350016c8abcab897046f1aead2b766b84d3eff8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/7350016c8abcab897046f1aead2b766b84d3eff8", "reference": "7350016c8abcab897046f1aead2b766b84d3eff8", "shasum": ""}, "require": {"php": ">=5.3.9", "symfony/debug": "~2.7,>=2.7.2|~3.0.0", "symfony/polyfill-mbstring": "~1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/event-dispatcher": "~2.1|~3.0.0", "symfony/process": "~2.1|~3.0.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Console Component", "homepage": "https://symfony.com", "time": "2016-10-06 01:43:09"}, {"name": "symfony/debug", "version": "v3.0.9", "source": {"type": "git", "url": "https://github.com/symfony/debug.git", "reference": "697c527acd9ea1b2d3efac34d9806bf255278b0a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug/zipball/697c527acd9ea1b2d3efac34d9806bf255278b0a", "reference": "697c527acd9ea1b2d3efac34d9806bf255278b0a", "shasum": ""}, "require": {"php": ">=5.5.9", "psr/log": "~1.0"}, "conflict": {"symfony/http-kernel": ">=2.3,<2.3.24|~2.4.0|>=2.5,<2.5.9|>=2.6,<2.6.2"}, "require-dev": {"symfony/class-loader": "~2.8|~3.0", "symfony/http-kernel": "~2.8|~3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Debug\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Debug Component", "homepage": "https://symfony.com", "time": "2016-07-30 07:22:48"}, {"name": "symfony/dependency-injection", "version": "v3.1.6", "source": {"type": "git", "url": "https://github.com/symfony/dependency-injection.git", "reference": "c578891216090069cd6d2e573402e13e39b3ad5c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/c578891216090069cd6d2e573402e13e39b3ad5c", "reference": "c578891216090069cd6d2e573402e13e39b3ad5c", "shasum": ""}, "require": {"php": ">=5.5.9"}, "require-dev": {"symfony/config": "~2.8|~3.0", "symfony/expression-language": "~2.8|~3.0", "symfony/yaml": "~2.8.7|~3.0.7|~3.1.1|~3.2"}, "suggest": {"symfony/config": "", "symfony/expression-language": "For using expressions in service container configuration", "symfony/proxy-manager-bridge": "Generate service proxies to lazy load them", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\DependencyInjection\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony DependencyInjection Component", "homepage": "https://symfony.com", "time": "2016-10-24 15:52:44"}, {"name": "symfony/filesystem", "version": "v3.1.6", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "0565b61bf098cb4dc09f4f103f033138ae4f42c6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/0565b61bf098cb4dc09f4f103f033138ae4f42c6", "reference": "0565b61bf098cb4dc09f4f103f033138ae4f42c6", "shasum": ""}, "require": {"php": ">=5.5.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Filesystem Component", "homepage": "https://symfony.com", "time": "2016-10-18 04:30:12"}, {"name": "symfony/polyfill-mbstring", "version": "v1.2.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "dff51f72b0706335131b00a7f49606168c582594"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/dff51f72b0706335131b00a7f49606168c582594", "reference": "dff51f72b0706335131b00a7f49606168c582594", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "time": "2016-05-18 14:26:46"}, {"name": "symfony/yaml", "version": "v3.1.6", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "7ff51b06c6c3d5cc6686df69004a42c69df09e27"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/7ff51b06c6c3d5cc6686df69004a42c69df09e27", "reference": "7ff51b06c6c3d5cc6686df69004a42c69df09e27", "shasum": ""}, "require": {"php": ">=5.5.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Yaml Component", "homepage": "https://symfony.com", "time": "2016-10-24 18:41:13"}, {"name": "webmozart/assert", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/webmozart/assert.git", "reference": "bb2d123231c095735130cc8f6d31385a44c7b308"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozart/assert/zipball/bb2d123231c095735130cc8f6d31385a44c7b308", "reference": "bb2d123231c095735130cc8f6d31385a44c7b308", "shasum": ""}, "require": {"php": "^5.3.3|^7.0"}, "require-dev": {"phpunit/phpunit": "^4.6", "sebastian/version": "^1.0.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "time": "2016-08-09 15:02:57"}], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=5.5", "ext-openssl": "*"}, "platform-dev": []}