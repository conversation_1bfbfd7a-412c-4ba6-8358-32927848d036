# Promise

[![Latest Version](https://img.shields.io/github/release/php-http/promise.svg?style=flat-square)](https://github.com/php-http/promise/releases)
[![Software License](https://img.shields.io/badge/license-MIT-brightgreen.svg?style=flat-square)](LICENSE)
[![Build Status](https://img.shields.io/travis/php-http/promise.svg?style=flat-square)](https://travis-ci.org/php-http/promise)
[![Code Coverage](https://img.shields.io/scrutinizer/coverage/g/php-http/promise.svg?style=flat-square)](https://scrutinizer-ci.com/g/php-http/promise)
[![Quality Score](https://img.shields.io/scrutinizer/g/php-http/promise.svg?style=flat-square)](https://scrutinizer-ci.com/g/php-http/promise)
[![Total Downloads](https://img.shields.io/packagist/dt/php-http/promise.svg?style=flat-square)](https://packagist.org/packages/php-http/promise)

**Promise used for asynchronous HTTP requests.**

**Note:** This will eventually be removed/deprecated and replaced with the upcoming Promise PSR.


## Install

Via Composer

``` bash
$ composer require php-http/promise
```


## Documentation

Please see the [official documentation](http://docs.php-http.org).


## Testing

``` bash
$ composer test
```


## Contributing

Please see our [contributing guide](http://docs.php-http.org/en/latest/development/contributing.html).


## Security

If you discover any security related issues, please contact us at [<EMAIL>](mailto:<EMAIL>)
or [<EMAIL>](mailto:<EMAIL>).


## License

The MIT License (MIT). Please see [License File](LICENSE) for more information.
