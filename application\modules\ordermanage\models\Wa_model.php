<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Wa_model extends CI_Model
{
    function dbDate($date)
    {
        // Parse the input date string
        $timestamp = strtotime($date);

        // Format the date as dd-mm-yyyy
        $formattedDate = date('Y-m-d', $timestamp);

        return $formattedDate;
    }

    function dbDateTime($date)
    {
        $timestamp = strtotime($date);
        return date('Y-m-d H:i:s', $timestamp); // keep full datetime
    }


    public function reportSoldItemByCustomer($start_date, $end_date, $customerId = null)
    {
        // Convert date to DB format
        $start_date = $this->dbDateTime($start_date);
        $end_date = $this->dbDateTime($end_date);

        // Base condition for customer order
        $condition = "co.order_status = 4 AND co.order_date BETWEEN '$start_date' AND '$end_date'";

        // Optional filter for specific customer
        if (!empty($customerId)) {
            $customerId = $this->db->escape_like_str($customerId);
            $condition .= " AND co.customer_id = '$customerId'";
        }

        $sql = "
            SELECT 
                combined.menu_id, 
                combined.customer_id,
                ci.customer_name,
                SUM(combined.quantity) AS total_qty, 
                SUM(combined.total_price) AS total_sales,
                item_foods.ProductName as item_name,
                item_foods.OffersRate,
                variant.price AS mprice,
                variant.variantName,
                item_category.Name AS category_name
            FROM (
                -- Direct items
                SELECT 
                    co.customer_id,
                    om.menu_id, 
                    om.price, 
                    om.menuqty AS quantity, 
                    (om.price * om.menuqty) AS total_price
                FROM customer_order co
                INNER JOIN order_menu om ON co.order_id = om.order_id
                WHERE $condition AND om.groupmid = 0 AND om.allfoodready IS NOT NULL

                UNION ALL

                -- Grouped items
                SELECT 
                    co.customer_id,
                    om.groupmid AS menu_id, 
                    om.price, 
                    MAX(om.menuqty) AS quantity, 
                    MAX(om.price * om.menuqty) AS total_price
                FROM customer_order co
                INNER JOIN order_menu om ON co.order_id = om.order_id
                WHERE $condition AND om.groupmid <> 0 AND om.allfoodready IS NOT NULL
                GROUP BY co.customer_id, om.groupmid, om.price
            ) AS combined
            LEFT JOIN customer_info ci ON ci.customer_id = combined.customer_id
            LEFT JOIN item_foods ON combined.menu_id = item_foods.ProductsID 
            LEFT JOIN variant ON item_foods.ProductsID = variant.variantid 
            LEFT JOIN item_category ON item_foods.CategoryID = item_category.CategoryID
            GROUP BY combined.menu_id, combined.customer_id
            ORDER BY ci.customer_name ASC
        ";

        return $this->db->query($sql)->result();
    }

    public function get_top5_customers($start_date, $end_date)
    {
        $start_date = $this->dbDateTime($start_date);
        $end_date = $this->dbDateTime($end_date);

        $condition = "co.order_status = 4 AND co.order_date BETWEEN '$start_date' AND '$end_date'";

        $sql = "
            SELECT 
                ci.customer_id,
                ci.customer_name,
                SUM(combined.total_price) AS total_sales
            FROM (
                SELECT co.customer_id, (om.price * om.menuqty) AS total_price
                FROM customer_order co
                INNER JOIN order_menu om ON co.order_id = om.order_id
                WHERE $condition AND om.groupmid = 0 AND om.allfoodready IS NOT NULL

                UNION ALL

                SELECT co.customer_id, (om.price * om.menuqty) AS total_price
                FROM customer_order co
                INNER JOIN order_menu om ON co.order_id = om.order_id
                WHERE $condition AND om.groupmid <> 0 AND om.allfoodready IS NOT NULL
            ) AS combined
            LEFT JOIN customer_info ci ON ci.customer_id = combined.customer_id
            GROUP BY ci.customer_id
            ORDER BY total_sales DESC
            LIMIT 5
        ";

        return $this->db->query($sql)->result();
    }

    public function get_top5_menu_items($start_date, $end_date)
    {
        $start_date = $this->dbDateTime($start_date);
        $end_date = $this->dbDateTime($end_date);

        $condition = "co.order_status = 4 AND co.order_date BETWEEN '$start_date' AND '$end_date'";

        $sql = "
            SELECT 
                combined.menu_id,
                item_foods.ProductName AS item_name,
                SUM(combined.quantity) AS total_qty
            FROM (
                SELECT om.menu_id, om.menuqty AS quantity
                FROM customer_order co
                INNER JOIN order_menu om ON co.order_id = om.order_id
                WHERE $condition AND om.groupmid = 0 AND om.allfoodready IS NOT NULL

                UNION ALL

                SELECT om.groupmid AS menu_id, om.menuqty AS quantity
                FROM customer_order co
                INNER JOIN order_menu om ON co.order_id = om.order_id
                WHERE $condition AND om.groupmid <> 0 AND om.allfoodready IS NOT NULL
            ) AS combined
            LEFT JOIN item_foods ON combined.menu_id = item_foods.ProductsID
            GROUP BY combined.menu_id
            ORDER BY total_qty DESC
            LIMIT 5
        ";

        return $this->db->query($sql)->result();
    }

    public function get_top5_menu_items_by_category($start_date, $end_date, $category)
    {
        $start_date = $this->dbDateTime($start_date);
        $end_date = $this->dbDateTime($end_date);

        $condition = "co.order_status = 4 AND co.order_date BETWEEN '$start_date' AND '$end_date'";

        $sql = "
            SELECT 
                combined.menu_id,
                itm.ProductName AS item_name,
                SUM(combined.quantity) AS total_qty
            FROM (
                SELECT om.menu_id, om.menuqty AS quantity
                FROM customer_order co
                INNER JOIN order_menu om ON co.order_id = om.order_id
                WHERE $condition AND om.groupmid = 0 AND om.allfoodready IS NOT NULL

                UNION ALL

                SELECT om.groupmid AS menu_id, om.menuqty AS quantity
                FROM customer_order co
                INNER JOIN order_menu om ON co.order_id = om.order_id
                WHERE $condition AND om.groupmid <> 0 AND om.allfoodready IS NOT NULL
            ) AS combined
            LEFT JOIN item_foods itm ON combined.menu_id = itm.ProductsID
            LEFT JOIN item_category cat ON cat.CategoryID = itm.CategoryID
            WHERE cat.CategoryID = (
                SELECT CategoryID FROM item_category WHERE LOWER(Name) = LOWER(?)
            )
            OR cat.parentId = (
                SELECT CategoryID FROM item_category WHERE LOWER(Name) = LOWER(?)
            )
            GROUP BY combined.menu_id
            ORDER BY total_qty DESC
            LIMIT 5
        ";

        return $this->db->query($sql, [$category, $category])->result();
    }

    public function get_payment_method_recap($start_date, $end_date)
    {
        $this->db->select('bill.*, multipay_bill.payment_type_id, SUM(multipay_bill.amount) as totalamount, payment_method.payment_method')
            ->from('multipay_bill')
            ->join('bill', 'bill.order_id = multipay_bill.order_id', 'left')
            ->join('payment_method', 'payment_method.payment_method_id = multipay_bill.payment_type_id', 'left')
            ->where('bill.bill_status', 1)
            ->where("bill.create_at BETWEEN '$start_date' AND '$end_date'")
            ->group_by('multipay_bill.payment_type_id');

        $query = $this->db->get();
        return $query->result();
    }

    public function get_total_payment($start_date, $end_date)
    {
        $this->db->select('SUM(multipay_bill.amount) as total_payment')
            ->from('multipay_bill')
            ->join('bill', 'bill.order_id = multipay_bill.order_id', 'left')
            ->where('bill.bill_status', 1)
            ->where("bill.create_at BETWEEN '$start_date' AND '$end_date'");

        $query = $this->db->get();
        $result = $query->row();

        return $result->total_payment ?? 0;
    }

    public function get_total_transaksi($start_date, $end_date)
    {
        $start_date = $this->dbDateTime($start_date);
        $end_date = $this->dbDateTime($end_date);

        $condition = "co.order_status = 4 AND co.order_date BETWEEN '$start_date' AND '$end_date'";

        $sql = "
            SELECT COUNT(order_id) AS total_transaksi
            FROM customer_order co
            WHERE $condition
        ";

        $query = $this->db->query($sql);
        return $query->row()->total_transaksi; // returns integer
    }

    public function get_total_pelanggan($start_date, $end_date)
    {
        $start_date = $this->dbDateTime($start_date);
        $end_date = $this->dbDateTime($end_date);

        $condition = "created_at BETWEEN '$start_date' AND '$end_date'";

        $sql = "
            SELECT 
                SUM(total_people) AS total_pelanggan
            FROM table_details
            WHERE $condition
        ";

        $query = $this->db->query($sql);
        return $query->row()->total_pelanggan; // returns integer
    }

    public function get_sell_items($start_date, $end_date)
    {
        $start_date = $this->dbDateTime($start_date);
        $end_date = $this->dbDateTime($end_date);

        $condition = "co.order_status = 4 AND co.order_date BETWEEN '$start_date' AND '$end_date'";

        $sql = "
            SELECT 
                combined.menu_id,
                item_foods.ProductName AS item_name,
                SUM(combined.quantity) AS total_qty,
                combined.menu_price as single_price,
                SUM(combined.quantity * combined.menu_price) AS total_sales
            FROM (
                SELECT 
                    om.menu_id, 
                    om.menuqty AS quantity,
                    om.price as menu_price
                FROM customer_order co
                INNER JOIN order_menu om ON co.order_id = om.order_id
                WHERE $condition AND om.groupmid = 0 AND om.allfoodready IS NOT NULL

                UNION ALL

                SELECT 
                    om.groupmid AS menu_id, 
                    om.menuqty AS quantity,
                    om.price as menu_price
                FROM customer_order co
                INNER JOIN order_menu om ON co.order_id = om.order_id
                WHERE $condition AND om.groupmid <> 0 AND om.allfoodready IS NOT NULL
            ) AS combined
            LEFT JOIN item_foods ON combined.menu_id = item_foods.ProductsID
            GROUP BY combined.menu_id
            ORDER BY total_qty DESC
        ";

        return $this->db->query($sql)->result();
    }

    public function get_recap_customer_type($start_date, $end_date)
    {
        $start_date = $this->dbDateTime($start_date);
        $end_date = $this->dbDateTime($end_date);

        $condition = "co.order_status = 4 AND co.order_date BETWEEN '$start_date' AND '$end_date'";

        $sql = "
            SELECT 
                ct.customer_type as name_type,
                COUNT(co.cutomertype) as total_type
            FROM customer_order co
            LEFT JOIN customer_type ct ON ct.customer_type_id = co.cutomertype
            WHERE $condition
            GROUP BY co.cutomertype
            ORDER BY total_type DESC
        ";

        return $this->db->query($sql)->result();
    }
}
