<?php
/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace SquareConnect\Model;
use SquareConnect\Model\CreateCheckoutRequest;

/**
 * CreateCheckoutRequestTest Class Doc Comment
 *
 * @category Class
 * @package  SquareConnect
 * <AUTHOR> Inc.
 * @license  http://www.apache.org/licenses/LICENSE-2.0 Apache Licene v2
 * @link     https://squareup.com/developers
 */
class CreateCheckoutRequestTest extends \PHPUnit_Framework_TestCase
{

    private static $checkoutRequest;

    /**
     * Setup before running each test case
     */
    public static function setUpBeforeClass() {
        self::$checkoutRequest = new \SquareConnect\Model\CreateCheckoutRequest();
    }

    /**
     * Clean up after running each test case
     */
    public static function tearDownAfterClass() {

    }

    /**
     * Test CreateCheckoutRequest
     */
    public function testCreateCheckoutRequest() {

    }

    /**
     * Test AdditionalRecipients
     */
    public function testAdditionalRecipients() {
        $recipient = [
            "location_id" => "location",
            "description" => "description",
            "amount_money" => [
              "amount" => 1,
              "currency" => "USD"
            ]
        ];
        self::$checkoutRequest->setAdditionalRecipients([$recipient]);
        $this->assertContains($recipient, self::$checkoutRequest->getAdditionalRecipients());
    }

}
