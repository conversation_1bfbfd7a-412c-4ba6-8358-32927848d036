<div class="row">
    <div class="col-sm-12 col-md-12">
        <div class="panel panel-bd lobidrag">
            <div class="panel-heading">
                <div class="panel-title">
                    <h4><?php echo display('customer_details') ?></h4>
                    <a href="<?php echo base_url('customermanage/customer/index') ?>" class="btn btn-primary btn-sm pull-right">
                        <i class="fa fa-arrow-left"></i> <?php echo display('back') ?>
                    </a>
                </div>
            </div>
            <div class="panel-body">
                <!-- Customer Information -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4 class="panel-title"><?php echo display('customer_information') ?></h4>
                            </div>
                            <div class="panel-body">
                                <table class="table table-bordered">
                                    <tr>
                                        <td><strong><?php echo display('customer_id') ?>:</strong></td>
                                        <td><?php echo $customer->cuntomer_no; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong><?php echo display('customer_name') ?>:</strong></td>
                                        <td><?php echo $customer->customer_name; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong><?php echo display('email') ?>:</strong></td>
                                        <td><?php echo $customer->customer_email; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong><?php echo display('mobile') ?>:</strong></td>
                                        <td><?php echo $customer->customer_phone; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong><?php echo display('address') ?>:</strong></td>
                                        <td><?php echo $customer->customer_address; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong><?php echo display('customer_type') ?>:</strong></td>
                                        <td><?php echo $customer->customer_type ? $customer->customer_type : 'Regular'; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong><?php echo display('member_since') ?>:</strong></td>
                                        <td><?php echo date('d M Y', strtotime($customer->crdate)); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Customer Statistics -->
                    <div class="col-md-6">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4 class="panel-title"><?php echo display('customer_statistics') ?></h4>
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-md-4 text-center">
                                        <div class="stat-box">
                                            <h3 class="text-primary"><?php echo $statistics['total_orders']; ?></h3>
                                            <p><?php echo display('total_orders') ?></p>
                                        </div>
                                    </div>
                                    <div class="col-md-4 text-center">
                                        <div class="stat-box">
                                            <h3 class="text-success">Rp <?php echo number_format($statistics['total_spent'], 0, ',', '.'); ?></h3>
                                            <p><?php echo display('total_spent') ?></p>
                                        </div>
                                    </div>
                                    <div class="col-md-4 text-center">
                                        <div class="stat-box">
                                            <h3 class="text-info"><?php echo $statistics['last_order_date'] ? date('d M Y', strtotime($statistics['last_order_date'])) : 'Belum Ada'; ?></h3>
                                            <p><?php echo display('last_order') ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Order History -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4 class="panel-title"><?php echo display('order_history') ?></h4>
                            </div>
                            <div class="panel-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th><?php echo display('sl') ?></th>
                                                <th><?php echo display('invoice_no') ?></th>
                                                <th><?php echo display('order_date') ?></th>
                                                <th><?php echo display('table') ?></th>
                                                <th><?php echo display('waiter') ?></th>
                                                <th><?php echo display('total_amount') ?></th>
                                                <th><?php echo display('status') ?></th>
                                                <th><?php echo display('action') ?></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (!empty($orders)) { ?>
                                                <?php $sl = $this->uri->segment(5) ? $this->uri->segment(5) : 0; ?>
                                                <?php foreach ($orders as $order) { ?>
                                                    <tr>
                                                        <td><?php echo ++$sl; ?></td>
                                                        <td><?php echo $order->saleinvoice; ?></td>
                                                        <td><?php echo date('d M Y', strtotime($order->order_date)); ?></td>
                                                        <td><?php echo $order->tablename ? $order->tablename : 'N/A'; ?></td>
                                                        <td><?php echo $order->first_name . ' ' . $order->last_name; ?></td>
                                                        <td>Rp <?php echo number_format($order->totalamount, 0, ',', '.'); ?></td>
                                                        <td>
                                                            <?php
                                                            switch ($order->order_status) {
                                                                case 1:
                                                                    echo '<span class="label label-success">Complete</span>';
                                                                    break;
                                                                case 2:
                                                                    echo '<span class="label label-warning">Processing</span>';
                                                                    break;
                                                                case 3:
                                                                    echo '<span class="label label-info">Ready</span>';
                                                                    break;
                                                                case 4:
                                                                    echo '<span class="label label-primary">Served</span>';
                                                                    break;
                                                                default:
                                                                    echo '<span class="label label-default">Pending</span>';
                                                            }
                                                            ?>
                                                        </td>
                                                        <td>
                                                            <button type="button" class="btn btn-info btn-sm view-order-details" data-order-id="<?php echo $order->order_id; ?>" data-toggle="modal" data-target="#orderDetailsModal">
                                                                <i class="fa fa-eye"></i> <?php echo display('view_details') ?>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                <?php } ?>
                                            <?php } else { ?>
                                                <tr>
                                                    <td colspan="8" class="text-center"><?php echo display('no_record_found'); ?></td>
                                                </tr>
                                            <?php } ?>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="text-right"><?php echo $links; ?></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Order Details Modal -->
<div class="modal fade" id="orderDetailsModal" tabindex="-1" role="dialog" aria-labelledby="orderDetailsModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="orderDetailsModalLabel"><?php echo display('order_details') ?></h4>
            </div>
            <div class="modal-body" id="orderDetailsContent">
                <div class="text-center">
                    <i class="fa fa-spinner fa-spin fa-2x"></i>
                    <p><?php echo display('loading') ?>...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo display('close') ?></button>
            </div>
        </div>
    </div>
</div>

<style>
    .stat-box {
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 5px;
        margin-bottom: 10px;
    }

    .stat-box h3 {
        margin: 0;
        font-weight: bold;
    }

    .stat-box p {
        margin: 5px 0 0 0;
        color: #666;
    }
</style>

<script>
    $(document).ready(function() {
        // View order details
        $('.view-order-details').on('click', function() {
            var order_id = $(this).data('order-id');

            $.ajax({
                url: '<?php echo base_url("customermanage/customer/get_order_details") ?>',
                type: 'POST',
                data: {
                    order_id: order_id
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        var html = '<div class="table-responsive">';
                        html += '<table class="table table-bordered">';
                        html += '<thead>';
                        html += '<tr>';
                        html += '<th><?php echo display('item_name') ?></th>';
                        html += '<th><?php echo display('quantity') ?></th>';
                        html += '<th><?php echo display('price') ?></th>';
                        html += '<th><?php echo display('total') ?></th>';
                        html += '</tr>';
                        html += '</thead>';
                        html += '<tbody>';

                        var total = 0;
                        $.each(response.data, function(index, item) {
                            var item_total = parseFloat(item.price) * parseInt(item.menuqty);
                            total += item_total;

                            html += '<tr>';
                            html += '<td>' + item.ProductName + '</td>';
                            html += '<td>' + item.menuqty + '</td>';
                            html += '<td>Rp ' + parseFloat(item.price).toLocaleString('id-ID') + '</td>';
                            html += '<td>Rp ' + item_total.toLocaleString('id-ID') + '</td>';
                            html += '</tr>';
                        });

                        html += '</tbody>';
                        html += '<tfoot>';
                        html += '<tr>';
                        html += '<th colspan="3"><?php echo display('total') ?></th>';
                        html += '<th>Rp ' + total.toLocaleString('id-ID') + '</th>';
                        html += '</tr>';
                        html += '</tfoot>';
                        html += '</table>';
                        html += '</div>';

                        $('#orderDetailsContent').html(html);
                    } else {
                        $('#orderDetailsContent').html('<div class="alert alert-danger">' + response.message + '</div>');
                    }
                },
                error: function() {
                    $('#orderDetailsContent').html('<div class="alert alert-danger">Error occurred while loading order details</div>');
                }
            });
        });
    });
</script>