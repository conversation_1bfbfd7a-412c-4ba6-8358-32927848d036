# Change Log


## 1.1.1 - 2016-05-10

### Fixed

- Adapter can again be instantiated without a guzzle client.

## 1.1.0 - 2016-05-09

### Added

- Factory method Client::createWithConfig to create an adapter with custom
  configuration for the underlying guzzle client.


## 1.0.0 - 2016-01-26


## 0.4.1 - 2016-01-13

### Changed

- Updated integration tests

### Removed

- Client common dependency


## 0.4.0 - 2016-01-12

### Changed

- Updated package files
- Updated HTTPlug to RC1


## 0.2.1 - 2015-12-17

### Added

- Puli configuration and bindings

### Changed

- Guzzle setup conforms to HTTPlug requirement now: Minimal functionality in client


## 0.2.0 - 2015-12-15

### Added

- Async client capabalities

### Changed

- HTTPlug instead of HTTP Adapter


## 0.1.0 - 2015-06-12

### Added

- Initial release
