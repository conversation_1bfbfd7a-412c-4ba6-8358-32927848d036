/*
 * metismenu - v2.7.4
 * A jQuery menu plugin
 * https://github.com/onokumus/metismenu#readme
 *
 * Made by <PERSON><PERSON> <<EMAIL>> (https://github.com/onokumus)
 * Under MIT License
 */

!function(n,i){if("function"==typeof define&&define.amd)define(["jquery"],i);else if("undefined"!=typeof exports)i(require("jquery"));else{i(n.jQuery),n.metisMenu={}}}(this,function(n){"use strict";var i,e=(i=n,i&&i.__esModule?i:{default:i});var t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n};var s=function(){function n(n,i){for(var e=0;e<i.length;e++){var t=i[e];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(n,t.key,t)}}return function(i,e,t){return e&&n(i.prototype,e),t&&n(i,t),i}}(),a=function(n){var i=!1;function e(i){var e=this,s=!1;return n(this).one(t.TRANSITION_END,function(){s=!0}),setTimeout(function(){s||t.triggerTransitionEnd(e)},i),this}var t={TRANSITION_END:"mmTransitionEnd",triggerTransitionEnd:function(e){n(e).trigger(i.end)},supportsTransitionEnd:function(){return Boolean(i)}};return i=("undefined"==typeof window||!window.QUnit)&&{end:"transitionend"},n.fn.mmEmulateTransitionEnd=e,t.supportsTransitionEnd()&&(n.event.special[t.TRANSITION_END]={bindType:i.end,delegateType:i.end,handle:function(i){if(n(i.target).is(this))return i.handleObj.handler.apply(this,arguments)}}),t}(e.default);!function(n){var i="metisMenu",e="metisMenu",o="."+e,r=n.fn[i],l={toggle:!0,preventDefault:!0,activeClass:"active",collapseClass:"collapse",collapseInClass:"in",collapsingClass:"collapsing",triggerElement:"a",parentTrigger:"li",subMenu:"ul"},c={SHOW:"show"+o,SHOWN:"shown"+o,HIDE:"hide"+o,HIDDEN:"hidden"+o,CLICK_DATA_API:"click"+o+".data-api"},f=function(){function i(n,e){!function(n,i){if(!(n instanceof i))throw new TypeError("Cannot call a class as a function")}(this,i),this._element=n,this._config=this._getConfig(e),this._transitioning=null,this.init()}return s(i,[{key:"init",value:function(){var i=this;n(this._element).find(this._config.parentTrigger+"."+this._config.activeClass).has(this._config.subMenu).children(this._config.subMenu).attr("aria-expanded",!0).addClass(this._config.collapseClass+" "+this._config.collapseInClass),n(this._element).find(this._config.parentTrigger).not("."+this._config.activeClass).has(this._config.subMenu).children(this._config.subMenu).attr("aria-expanded",!1).addClass(this._config.collapseClass),n(this._element).find(this._config.parentTrigger).has(this._config.subMenu).children(this._config.triggerElement).on(c.CLICK_DATA_API,function(e){var t=n(this),s=t.parent(i._config.parentTrigger),a=s.siblings(i._config.parentTrigger).children(i._config.triggerElement),o=s.children(i._config.subMenu);i._config.preventDefault&&e.preventDefault(),"true"!==t.attr("aria-disabled")&&(s.hasClass(i._config.activeClass)?(t.attr("aria-expanded",!1),i._hide(o)):(i._show(o),t.attr("aria-expanded",!0),i._config.toggle&&a.attr("aria-expanded",!1)),i._config.onTransitionStart&&i._config.onTransitionStart(e))})}},{key:"_show",value:function(i){if(!this._transitioning&&!n(i).hasClass(this._config.collapsingClass)){var e=this,t=n(i),s=n.Event(c.SHOW);if(t.trigger(s),!s.isDefaultPrevented()){t.parent(this._config.parentTrigger).addClass(this._config.activeClass),this._config.toggle&&this._hide(t.parent(this._config.parentTrigger).siblings().children(this._config.subMenu+"."+this._config.collapseInClass).attr("aria-expanded",!1)),t.removeClass(this._config.collapseClass).addClass(this._config.collapsingClass).height(0),this.setTransitioning(!0);var o=function(){e._config&&e._element&&(t.removeClass(e._config.collapsingClass).addClass(e._config.collapseClass+" "+e._config.collapseInClass).height("").attr("aria-expanded",!0),e.setTransitioning(!1),t.trigger(c.SHOWN))};a.supportsTransitionEnd()?t.height(t[0].scrollHeight).one(a.TRANSITION_END,o).mmEmulateTransitionEnd(350):o()}}}},{key:"_hide",value:function(i){if(!this._transitioning&&n(i).hasClass(this._config.collapseInClass)){var e=this,t=n(i),s=n.Event(c.HIDE);if(t.trigger(s),!s.isDefaultPrevented()){t.parent(this._config.parentTrigger).removeClass(this._config.activeClass),t.height(t.height())[0].offsetHeight,t.addClass(this._config.collapsingClass).removeClass(this._config.collapseClass).removeClass(this._config.collapseInClass),this.setTransitioning(!0);var o=function(){e._config&&e._element&&(e._transitioning&&e._config.onTransitionEnd&&e._config.onTransitionEnd(),e.setTransitioning(!1),t.trigger(c.HIDDEN),t.removeClass(e._config.collapsingClass).addClass(e._config.collapseClass).attr("aria-expanded",!1))};a.supportsTransitionEnd()?0==t.height()||"none"==t.css("display")?o():t.height(0).one(a.TRANSITION_END,o).mmEmulateTransitionEnd(350):o()}}}},{key:"setTransitioning",value:function(n){this._transitioning=n}},{key:"dispose",value:function(){n.removeData(this._element,e),n(this._element).find(this._config.parentTrigger).has(this._config.subMenu).children(this._config.triggerElement).off("click"),this._transitioning=null,this._config=null,this._element=null}},{key:"_getConfig",value:function(i){return i=n.extend({},l,i)}}],[{key:"_jQueryInterface",value:function(s){return this.each(function(){var a=n(this),o=a.data(e),r=n.extend({},l,a.data(),"object"===(void 0===s?"undefined":t(s))&&s);if(!o&&/dispose/.test(s)&&this.dispose(),o||(o=new i(this,r),a.data(e,o)),"string"==typeof s){if(void 0===o[s])throw new Error('No method named "'+s+'"');o[s]()}})}}]),i}();n.fn[i]=f._jQueryInterface,n.fn[i].Constructor=f,n.fn[i].noConflict=function(){return n.fn[i]=r,f._jQueryInterface}}(e.default)});
//# sourceMappingURL=metisMenu.js.map