<div class="row">
    <div class="col-sm-12 col-md-12">
        <div class="panel panel-bd lobidrag">
            <div class="panel-heading">
                <div class="panel-title">
                    <h4><?php echo $ranking_title; ?></h4>
                    <a href="<?php echo base_url('customermanage/customer/index') ?>" class="btn btn-primary btn-sm pull-right">
                        <i class="fa fa-list"></i> <?php echo display('customer_list') ?>
                    </a>
                </div>
            </div>
            <div class="panel-body">
                <div class="row mb-3">
                    <div class="col-sm-12">
                        <div class="btn-group">
                            <a href="<?php echo base_url('customermanage/customer/ranking/spent'); ?>" class="btn <?php echo ($ranking_type == 'spent') ? 'btn-primary' : 'btn-default'; ?>">
                                <?php echo display('top_customers_by_spent') ?>
                            </a>
                            <a href="<?php echo base_url('customermanage/customer/ranking/orders'); ?>" class="btn <?php echo ($ranking_type == 'orders') ? 'btn-primary' : 'btn-default'; ?>">
                                <?php echo display('top_customers_by_orders') ?>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th><?php echo display('rank') ?></th>
                                <th><?php echo display('customer_id') ?></th>
                                <th><?php echo display('customer_name') ?></th>
                                <th><?php echo display('email') ?></th>
                                <th><?php echo display('mobile') ?></th>
                                <th><?php echo display('customer_type') ?></th>
                                <th><?php echo display('total_orders') ?></th>
                                <th><?php echo display('total_spent') ?></th>
                                <th><?php echo display('last_order') ?></th>
                                <th><?php echo display('action') ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($top_customers)) { ?>
                                <?php $rank = 1; ?>
                                <?php foreach ($top_customers as $customer) { ?>
                                    <tr>
                                        <td>
                                            <?php
                                            // Display medal for top 3
                                            if ($rank == 1) {
                                                echo '<span class="badge" style="background-color: gold; color: #000;"><i class="fa fa-trophy"></i> 1</span>';
                                            } else if ($rank == 2) {
                                                echo '<span class="badge" style="background-color: silver; color: #000;"><i class="fa fa-trophy"></i> 2</span>';
                                            } else if ($rank == 3) {
                                                echo '<span class="badge" style="background-color: #cd7f32; color: #000;"><i class="fa fa-trophy"></i> 3</span>';
                                            } else {
                                                echo $rank;
                                            }
                                            ?>
                                        </td>
                                        <td><?php echo $customer->cuntomer_no; ?></td>
                                        <td><?php echo $customer->customer_name; ?></td>
                                        <td><?php echo $customer->customer_email; ?></td>
                                        <td><?php echo $customer->customer_phone; ?></td>
                                        <td><?php echo $customer->customer_type ? $customer->customer_type : 'Regular'; ?></td>
                                        <td><?php echo $customer->total_orders; ?></td>
                                        <td>Rp <?php echo number_format($customer->total_spent, 0, ',', '.'); ?></td>
                                        <td><?php echo $customer->last_order_date ? date('d M Y', strtotime($customer->last_order_date)) : '-'; ?></td>
                                        <td>
                                            <a href="<?php echo base_url("customermanage/customer/customerdetails/$customer->customer_id") ?>" class="btn btn-info btn-sm" data-toggle="tooltip" data-placement="left" title="<?php echo display('view_details') ?>"><i class="fa fa-eye" aria-hidden="true"></i></a>
                                        </td>
                                    </tr>
                                    <?php $rank++; ?>
                                <?php } ?>
                            <?php } else { ?>
                                <tr>
                                    <td colspan="10" class="text-center"><?php echo display('no_record_found'); ?></td>
                                </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .mb-3 {
        margin-bottom: 15px;
    }
</style>